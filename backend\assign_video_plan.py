#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
django.setup()

from accounts.models import User, UserProfile
from workouts.models import UserWorkoutPlan, WorkoutPlan, WorkoutVideo

def assign_video_plan():
    print("=== Assigning Video Plan to User ===\n")
    
    # Get the workout plan with videos
    plan_with_videos = WorkoutPlan.objects.filter(videos__isnull=False).first()
    if not plan_with_videos:
        print("No workout plans with videos found!")
        return
    
    print(f"Plan with videos: {plan_with_videos.name}")
    video_count = WorkoutVideo.objects.filter(workout_plan=plan_with_videos).count()
    print(f"Video count: {video_count}")
    
    # Get a user to assign
    user = User.objects.filter(userprofile__isnull=False).first()
    if not user:
        print("No users with profiles found!")
        return
    
    user_profile = UserProfile.objects.get(user=user)
    print(f"Assigning to user: {user.username}")
    
    # Deactivate existing plans
    UserWorkoutPlan.objects.filter(user=user_profile, is_active=True).update(is_active=False)
    print("Deactivated existing plans")
    
    # Create or update the user workout plan
    user_workout_plan, created = UserWorkoutPlan.objects.get_or_create(
        user=user_profile,
        workout_plan=plan_with_videos,
        defaults={
            'is_active': True,
            'start_date': '2024-01-01',
            'is_modified': False,
        }
    )
    
    if not created:
        user_workout_plan.is_active = True
        user_workout_plan.save()
        print("Updated existing plan to active")
    else:
        print("Created new user workout plan")
    
    print(f"✓ User {user.username} now has access to {video_count} videos!")
    
    # Test the API endpoint
    print(f"\n=== Testing API Access ===")
    from workouts.serializers import WorkoutVideoSerializer
    videos = WorkoutVideo.objects.filter(workout_plan=plan_with_videos).order_by('order')
    serializer = WorkoutVideoSerializer(videos, many=True)
    
    print(f"Videos that should be accessible:")
    for video_data in serializer.data:
        print(f"  - {video_data['title']} (Order: {video_data['order']})")
        print(f"    Video URL: {video_data['video_url']}")
        print(f"    Thumbnail URL: {video_data['thumbnail_url']}")

if __name__ == "__main__":
    assign_video_plan()
