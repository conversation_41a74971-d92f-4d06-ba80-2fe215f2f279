import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart'; // Import Provider
import 'package:video_player/video_player.dart'; // Import video_player
import 'package:wakelock_plus/wakelock_plus.dart'; // Import wakelock to keep screen on
// Removed unused import

import '../../services/api_service.dart';
import '../../services/navigation_service.dart';
import '../../widgets/common/offline_banner.dart';
import '../../widgets/common/animated_loading.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../models/workout_log.dart'; // Import WorkoutLog
import '../../models/workout.dart'; // Import WorkoutExerciseLog, ExerciseDetail
import '../../providers/workout_provider.dart'; // Import WorkoutProvider
import '../../utils/image_utils.dart'; // Import our new image utility class
import '../../utils/logger.dart'; // Import Logger for logging
import '../../widgets/improved_gif_player.dart'
    as gif_player; // Import our improved GIF player
import '../../services/gif_preloader_service.dart'; // Import GIF preloader service
// Removed unused import
import 'workout_completion_screen.dart'; // Import the new completion screen

class WorkoutPlayerScreen extends StatefulWidget {
  final WorkoutLog workoutLog;
  final int workoutSessionLogId;
  final int startIndex;
  final List<WorkoutExerciseLog>? allExercises; // Add parameter for exercises

  const WorkoutPlayerScreen({
    Key? key,
    required this.workoutLog,
    required this.workoutSessionLogId,
    required this.startIndex,
    this.allExercises, // Make it optional
  }) : super(key: key);

  @override
  State<WorkoutPlayerScreen> createState() => _WorkoutPlayerScreenState();
}

class _WorkoutPlayerScreenState extends State<WorkoutPlayerScreen>
    with WidgetsBindingObserver {
  late final ApiService _apiService;
  bool _isLoading = false; // For logging exercise performance
  final bool _isOffline = false;
  String? _error;
  bool _isCompleting = false; // Flag to prevent multiple completion attempts

  // Store the actual workout log ID that we get from the server
  int _actualWorkoutLogId = 0;

  late List<WorkoutExerciseLog> _allExercises;
  late int _currentExerciseIndex;
  late WorkoutExerciseLog _currentExerciseLog;
  late ExerciseDetail _currentExerciseDetail;
  bool _isPlaying = false;
  int _secondsElapsed = 0;
  int _totalDuration = 0;
  Timer? _timer;
  VideoPlayerController? _videoController;
  Future<void>? _initializeVideoPlayerFuture;

  Timer? _restTimer;
  int _restDuration = 30;
  int _restTimeRemaining = 0;
  bool _showRestTimer = false;

  // Planned values for display
  late int _plannedSets;
  late String _plannedReps; // Keep as String for ranges
  late double? _plannedWeight;

  // Helper function to safely parse dynamic values to int
  int _parseToInt(dynamic value, int defaultValue) {
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is String) return int.tryParse(value) ?? defaultValue;
    if (value is num) return value.toInt();
    return defaultValue;
  }

  // Helper function to safely parse dynamic values to double
  double? _parseToDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  // Check if this is a future workout and prevent access
  void _checkFutureWorkout() {
    try {
      final dateStr = widget.workoutLog.date;
      final logDate = DateTime.parse(dateStr);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      // Removed detailed debug logging

      // Force block any workout with a date in the future
      if (logDate.isAfter(today)) {
        // Removed debug logging
        // This is a future workout, show error and navigate back
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('You cannot access future workouts'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
          // Navigate back after showing the message
          Future.delayed(const Duration(seconds: 1), () {
            NavigationService.goBack();
          });
        });
      }
    } catch (e) {
      // Removed error logging
      // If there's any error, assume it's a future workout and block access
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error checking workout date. Access denied.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
        // Navigate back after showing the message
        Future.delayed(const Duration(seconds: 1), () {
          NavigationService.goBack();
        });
      });
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize API service
    _apiService = Provider.of<ApiService>(context, listen: false);

    // Removed debug logging

    // Check if this is a future workout
    _checkFutureWorkout();

    // Register observer for app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // Keep screen on during workout
    WakelockPlus.enable();

    // Set preferred orientation to portrait
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    // Initialize exercise data
    // SIMPLIFIED APPROACH: Always use the exercises passed from the details screen
    Logger.workout(
        'Initializing workout player with session ID: ${widget.workoutSessionLogId}',
        tag: 'WorkoutPlayer');

    if (widget.allExercises != null && widget.allExercises!.isNotEmpty) {
      // Filter out exercises without exercise details or with empty/invalid names
      _allExercises = widget.allExercises!
          .where((ex) =>
              ex.exerciseDetails != null && ex.exerciseDetails!.name.isNotEmpty)
          .toList();

      Logger.workout('Using ${_allExercises.length} valid exercises',
          tag: 'WorkoutPlayer');
    } else {
      Logger.workout(
          'No exercises passed from details screen, falling back to workout log',
          tag: 'WorkoutPlayer');

      // Fallback to getAllExercises() only if no exercises were passed
      final allExercises = widget.workoutLog.getAllExercises();

      // Filter out exercises without exercise details or with empty/invalid names
      _allExercises = allExercises
          .where((ex) =>
              ex.exerciseDetails != null && ex.exerciseDetails!.name.isNotEmpty)
          .toList();

      Logger.workout(
          'Using ${_allExercises.length} valid exercises from workout log',
          tag: 'WorkoutPlayer');

      // If still no exercises, show an error
      if (_allExercises.isEmpty) {
        // We'll handle this in the build method
        Logger.error('No valid exercises found in workout log',
            tag: 'WorkoutPlayer');
      }
    }
    _currentExerciseIndex = widget.startIndex;

    Logger.workout(
        'Starting with ${_allExercises.length} exercises at index $_currentExerciseIndex',
        tag: 'WorkoutPlayer');

    if (_allExercises.isNotEmpty &&
        _currentExerciseIndex < _allExercises.length) {
      _loadExercise(_currentExerciseIndex);
    } else {
      Logger.debug('No exercises to load or invalid index');
      _error = 'Could not load workout exercises.';
      // Initialize with defaults to prevent null errors in build
      _currentExerciseLog = WorkoutExerciseLog.withExercise(
          id: 0,
          exercise: ExerciseDetail(id: 0, name: 'Error'),
          order: 0,
          isCompleted: false);
      _currentExerciseDetail = _currentExerciseLog.exercise;
      _plannedSets = 1;
      _plannedReps = '0';
      _plannedWeight = null;
      _totalDuration = 0;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Now that we have a valid BuildContext, we can preload images
    _preloadImagesAfterContextAvailable();
  }

  // Method to preload images after context is available
  void _preloadImagesAfterContextAvailable() {
    // Only preload if we have exercises
    if (_allExercises.isEmpty ||
        _currentExerciseIndex >= _allExercises.length) {
      return;
    }

    // Try to preload the current exercise image if it's not a video
    final currentExercise = _allExercises[_currentExerciseIndex];
    if (currentExercise.exerciseDetails != null) {
      final currentExerciseDetail = currentExercise.exercise;
      final String? currentDisplayUrl = currentExerciseDetail.mediaFileUrl ??
          currentExerciseDetail.videoUrl ??
          currentExerciseDetail.imageUrl;

      if (currentDisplayUrl != null && currentDisplayUrl.isNotEmpty) {
        bool isGif = currentDisplayUrl.toLowerCase().endsWith('.gif');
        bool isStandardVideo = _isVideoUrl(currentDisplayUrl) && !isGif;

        // Only preload if it's an image or GIF, not a video
        if (!isStandardVideo) {
          if (isGif) {
            // Use our GIF preloader service for GIFs
            Logger.workout(
                'Preloading current exercise GIF with GifPreloaderService: $currentDisplayUrl',
                tag: 'WorkoutPlayer');
            _gifPreloader.preloadGif(currentDisplayUrl, context);
          } else {
            // Use regular precacheImage for non-GIF images
            Logger.workout(
                'Preloading current exercise image: $currentDisplayUrl',
                tag: 'WorkoutPlayer');
            precacheImage(NetworkImage(currentDisplayUrl), context)
                .catchError((error) {
              Logger.error('Error preloading current exercise image: $error',
                  tag: 'WorkoutPlayer');
            });
          }
        }
      }
    }

    // Check if there's a next exercise to preload
    if (_currentExerciseIndex + 1 < _allExercises.length) {
      final nextExercise = _allExercises[_currentExerciseIndex + 1];
      if (nextExercise.exerciseDetails != null) {
        final nextExerciseDetail = nextExercise.exercise;
        final String? nextDisplayUrl = nextExerciseDetail.mediaFileUrl ??
            nextExerciseDetail.videoUrl ??
            nextExerciseDetail.imageUrl;

        if (nextDisplayUrl != null && nextDisplayUrl.isNotEmpty) {
          bool isGif = nextDisplayUrl.toLowerCase().endsWith('.gif');
          bool isStandardVideo = _isVideoUrl(nextDisplayUrl) && !isGif;

          // Only preload if it's an image or GIF, not a video (videos are handled in _preloadNextExerciseMedia)
          if (!isStandardVideo) {
            if (isGif) {
              // Use our GIF preloader service for GIFs
              Logger.workout(
                  'Preloading next exercise GIF with GifPreloaderService: $nextDisplayUrl',
                  tag: 'WorkoutPlayer');
              _gifPreloader.preloadGif(nextDisplayUrl, context);

              // Also preload the next few exercises if they have GIFs
              _preloadFutureGifs(_currentExerciseIndex + 1, 3);
            } else {
              // Use regular precacheImage for non-GIF images
              Logger.workout('Preloading next exercise image: $nextDisplayUrl',
                  tag: 'WorkoutPlayer');
              precacheImage(NetworkImage(nextDisplayUrl), context)
                  .catchError((error) {
                Logger.error('Error preloading next exercise image: $error',
                    tag: 'WorkoutPlayer');
              });
            }
          }
        }
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Handle app lifecycle changes
    if (state == AppLifecycleState.paused) {
      // App is in background, pause timers and video
      _pauseWorkout();
    } else if (state == AppLifecycleState.resumed) {
      // App is in foreground again, but don't auto-resume
      // Just ensure video controller is ready
      if (_videoController != null && !_videoController!.value.isInitialized) {
        _initializeVideoPlayer();
      }
    }
  }

  // Pause workout when app goes to background
  void _pauseWorkout() {
    if (_isPlaying) {
      _timer?.cancel();
      _videoController?.pause();
      if (mounted) {
        setState(() => _isPlaying = false);
      }
    }
  }

  void _loadExercise(int index) {
    if (index < 0 || index >= _allExercises.length) {
      // Exercise index is out of bounds
      if (mounted) {
        setState(() => _error = 'Error loading next exercise.');
      }
      return;
    }

    try {
      final exerciseLog = _allExercises[index];

      // Ensure exercise details are available and valid
      if (exerciseLog.exerciseDetails == null ||
          exerciseLog.exerciseDetails!.name.isEmpty) {
        Logger.debug(
            'Exercise at index $index has no exercise details or invalid name. Skipping to next exercise.');
        // Try to load the next exercise instead
        if (index + 1 < _allExercises.length) {
          _loadExercise(index + 1);
        } else {
          // No more exercises to try
          if (mounted) {
            setState(
                () => _error = 'No valid exercises found in this workout.');
          }
        }
        return;
      }

      setState(() {
        _currentExerciseIndex = index;
        _currentExerciseLog = exerciseLog;

        // Set the exercise detail (now we've verified it's not null)
        _currentExerciseDetail = _currentExerciseLog.exercise;

        // Check if the exercise detail has valid data
        if (_currentExerciseDetail.name.isEmpty ||
            _currentExerciseDetail.name == 'Unknown Exercise') {
          // Log warning but don't use print in production
          Logger.debug(
              'WARNING: Exercise has default/empty name, enhancing with index');
          // Enhance the name with the index for better user experience
          _currentExerciseDetail = ExerciseDetail(
            id: _currentExerciseDetail.id,
            name: 'Exercise ${index + 1}',
            description: _currentExerciseDetail.description ??
                'No description available',
            imageUrl: _currentExerciseDetail.imageUrl,
            videoUrl: _currentExerciseDetail.videoUrl,
            mediaFileUrl: _currentExerciseDetail.mediaFileUrl,
          );
        }

        _timer?.cancel();
        _restTimer?.cancel();
        _secondsElapsed = 0;
        _isPlaying = false;
        _showRestTimer = false;

        // Initialize planned values with fallbacks
        _plannedSets = _parseToInt(_currentExerciseLog.sets, 1);
        _plannedReps =
            _currentExerciseLog.reps?.toString() ?? 'N/A'; // Keep as string
        _plannedWeight = _parseToDouble(_currentExerciseLog.weight);

        _calculateExerciseDuration();
        _initializeVideoPlayer();
        _error = null;

        // Preload next exercise media if available
        _preloadNextExerciseMedia(index);
      });
    } catch (e) {
      Logger.error('Error loading exercise at index $index: $e',
          tag: 'WorkoutPlayer');
      // Handle any errors when loading the exercise
      if (mounted) {
        setState(() {
          _error = 'Could not load exercise data. Using default values.';

          // Set default values to prevent crashes
          _currentExerciseLog = WorkoutExerciseLog.withExercise(
            id: 0,
            exercise: ExerciseDetail(
              id: 0,
              name: 'Exercise ${index + 1}',
              description: 'No description available',
            ),
            order: index,
            isCompleted: false,
          );
          _currentExerciseDetail = _currentExerciseLog.exercise;
          _plannedSets = 1;
          _plannedReps = 'N/A';
          _plannedWeight = null;
          _calculateExerciseDuration();
          _initializeVideoPlayer();
        });
      }
    }
  }

  // GIF preloader service
  final GifPreloaderService _gifPreloader = GifPreloaderService();

  // Preload the next exercise media to make transitions smoother
  void _preloadNextExerciseMedia(int currentIndex) {
    // Check if there's a next exercise
    if (currentIndex + 1 < _allExercises.length) {
      final nextExercise = _allExercises[currentIndex + 1];

      // Ensure the next exercise has details
      if (nextExercise.exerciseDetails != null) {
        final nextExerciseDetail = nextExercise.exercise;
        final String? nextDisplayUrl = nextExerciseDetail.mediaFileUrl ??
            nextExerciseDetail.videoUrl ??
            nextExerciseDetail.imageUrl;

        // Check if the URL is valid
        if (nextDisplayUrl != null && nextDisplayUrl.isNotEmpty) {
          // Check if it's a GIF
          bool isGif = nextDisplayUrl.toLowerCase().endsWith('.gif');

          // Check if it's a standard video format
          bool isStandardVideo = _isVideoUrl(nextDisplayUrl) && !isGif;

          // If it's a standard video (not GIF), preload it with video controller
          if (isStandardVideo) {
            Logger.workout('Preloading next exercise video: $nextDisplayUrl',
                tag: 'WorkoutPlayer');

            // Create a temporary controller just for preloading
            final tempController =
                VideoPlayerController.networkUrl(Uri.parse(nextDisplayUrl));

            // Initialize but don't store the controller or future
            tempController.initialize().then((_) {
              Logger.workout('Successfully preloaded next exercise video',
                  tag: 'WorkoutPlayer');
              // Dispose after preloading to free resources
              tempController.dispose();
            }).catchError((error) {
              Logger.error('Error preloading next exercise video: $error',
                  tag: 'WorkoutPlayer');
              tempController.dispose();
            });
          }
          // If it's a GIF, use our GIF preloader service
          else if (isGif) {
            Logger.workout(
                'GIF detected, using GifPreloaderService: $nextDisplayUrl',
                tag: 'WorkoutPlayer');
            // Use our GIF preloader service to preload the GIF
            _gifPreloader.preloadGif(nextDisplayUrl, context);

            // Also preload the next 2 exercises if they have GIFs
            _preloadFutureGifs(currentIndex + 1, 2);
          }
          // For regular images, we'll handle this in didChangeDependencies
          else {
            Logger.workout(
                'Regular image detected, will precache in didChangeDependencies: $nextDisplayUrl',
                tag: 'WorkoutPlayer');
            // Don't use precacheImage here as it requires BuildContext
          }
        }
      }
    }
  }

  // Preload GIFs for future exercises
  void _preloadFutureGifs(int startIndex, int count) {
    // Collect GIF URLs to preload
    List<String> gifUrls = [];

    // Look ahead for the specified number of exercises
    for (int i = startIndex;
        i < startIndex + count && i < _allExercises.length;
        i++) {
      final exercise = _allExercises[i];
      if (exercise.exerciseDetails != null) {
        final exerciseDetail = exercise.exercise;
        final String? displayUrl = exerciseDetail.mediaFileUrl ??
            exerciseDetail.videoUrl ??
            exerciseDetail.imageUrl;

        // Check if it's a GIF
        if (displayUrl != null &&
            displayUrl.isNotEmpty &&
            displayUrl.toLowerCase().endsWith('.gif')) {
          gifUrls.add(displayUrl);
          Logger.workout('Adding future GIF to preload queue: $displayUrl',
              tag: 'WorkoutPlayer');
        }
      }
    }

    // Preload all collected GIF URLs
    if (gifUrls.isNotEmpty) {
      _gifPreloader.preloadGifs(gifUrls, context);
    }
  }

  void _calculateExerciseDuration() {
    // Default to 30 seconds if no duration is specified
    int defaultDuration = 30;

    try {
      // Try to get the duration from the exercise log
      _totalDuration =
          _parseToInt(_currentExerciseLog.durationSeconds, defaultDuration);

      // Ensure duration is reasonable (between 10 seconds and 5 minutes)
      if (_totalDuration < 10) {
        _totalDuration = 10; // Minimum 10 seconds
      } else if (_totalDuration > 300) {
        _totalDuration = 300; // Maximum 5 minutes (300 seconds)
      }
    } catch (e) {
      // If any error occurs, use the default duration
      _totalDuration = defaultDuration;
    }
  }

  void _initializeVideoPlayer() {
    // Clean up any existing video controller
    _videoController?.dispose();
    _videoController = null;
    _initializeVideoPlayerFuture = null;

    try {
      // Try to get a display URL from the exercise detail
      final String? displayUrl = _currentExerciseDetail.mediaFileUrl ??
          _currentExerciseDetail.videoUrl ??
          _currentExerciseDetail.imageUrl;

      // Check if we have a valid URL
      if (displayUrl != null && displayUrl.isNotEmpty) {
        // Check if it's a standard video format (not a GIF)
        if (_isVideoUrl(displayUrl)) {
          Logger.workout('Initializing video player with URL: $displayUrl',
              tag: 'WorkoutPlayer');

          // Create a video controller with the URL
          _videoController =
              VideoPlayerController.networkUrl(Uri.parse(displayUrl));

          // Initialize the video player with better error handling
          _initializeVideoPlayerFuture =
              _videoController!.initialize().then((_) {
            // Set the video to loop
            _videoController!.setLooping(true);

            // Set video volume to 0 to avoid unexpected sounds
            _videoController!.setVolume(0.0);

            // Set playback speed to slightly slower for better viewing of exercise form
            _videoController!.setPlaybackSpeed(0.8);

            if (mounted) {
              setState(() {
                // If we're supposed to be playing, start the video
                if (_isPlaying) {
                  _videoController!.play();
                }
              });
            }
            Logger.workout('Video player initialized successfully',
                tag: 'WorkoutPlayer');
          }).catchError((error) {
            // Handle video initialization errors
            Logger.error('Could not load exercise video: $error',
                tag: 'WorkoutPlayer');
            if (mounted) {
              setState(() {
                // Clear the video controller
                _videoController?.dispose();
                _videoController = null;
              });
            }
            // Don't return anything from the catchError handler
          });
        } else {
          // It's a regular image or GIF, just return a completed future
          if (displayUrl.toLowerCase().endsWith('.gif')) {
            Logger.workout(
                'GIF detected, preloading with GifPreloaderService: $displayUrl',
                tag: 'WorkoutPlayer');
            // Preload the current GIF
            _gifPreloader.preloadGif(displayUrl, context);

            // Also preload the next 3 exercises if they have GIFs
            _preloadFutureGifs(_currentExerciseIndex, 3);
          } else {
            Logger.workout('Regular image URL detected: $displayUrl',
                tag: 'WorkoutPlayer');
          }
          _initializeVideoPlayerFuture = Future.value();
        }
      } else {
        // No valid URL, just return a completed future
        Logger.workout(
            'No valid media URL found for exercise: ${_currentExerciseDetail.name}',
            tag: 'WorkoutPlayer');
        _initializeVideoPlayerFuture = Future.value();
      }
    } catch (e) {
      // Handle any unexpected errors
      Logger.error('Failed to initialize video player: $e',
          tag: 'WorkoutPlayer');
      _initializeVideoPlayerFuture = Future.value();
    }
  }

  bool _isVideoUrl(String url) {
    final lowerUrl = url.toLowerCase();

    // Check for standard video formats only
    // GIFs are treated as regular images
    bool isStandardVideo = lowerUrl.endsWith('.mp4') ||
        lowerUrl.endsWith('.mov') ||
        lowerUrl.endsWith('.avi') ||
        lowerUrl.endsWith('.webm');

    // Log the URL type for debugging
    if (isStandardVideo) {
      Logger.workout('Standard video detected: $url', tag: 'WorkoutPlayer');
    }

    return isStandardVideo;
  }

  void _togglePlayPause() {
    if (_showRestTimer) return;
    setState(() => _isPlaying = !_isPlaying);
    if (_isPlaying) {
      _startTimer();
      _videoController?.play();
    } else {
      _timer?.cancel();
      _videoController?.pause();
    }
  }

  void _startTimer() {
    // Cancel any existing timer to prevent duplicates
    _timer?.cancel();

    // Create a new timer that ticks every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // Safety check: if widget is no longer in the tree, cancel timer
      if (!mounted) {
        timer.cancel();
        return;
      }

      // Update UI with new elapsed time
      setState(() {
        _secondsElapsed++;

        // Check if we've reached the target duration
        if (_secondsElapsed >= _totalDuration) {
          // Stop the timer
          timer.cancel();
          _isPlaying = false;
          _videoController?.pause();

          // Provide haptic feedback to indicate completion
          HapticFeedback.mediumImpact();

          // Play a short success sound
          SystemSound.play(SystemSoundType.click);

          // Removed debug logging

          // Show a brief success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Exercise completed!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 1),
            ),
          );

          // Log the exercise as completed
          _logExercisePerformance(isCompleted: true);
        }
      });
    });
  }

  // Removed unused _getWorkoutSessionId method

  Future<void> _logExercisePerformance({required bool isCompleted}) async {
    // Guard against multiple simultaneous logging attempts
    if (_isLoading) {
      return;
    }

    // Set loading state
    setState(() => _isLoading = true);

    // We don't need to create a workout log for each exercise
    // The workout log should already exist or be created once at the beginning
    // Just log that we're processing the exercise
    Logger.workout('Processing exercise completion: isCompleted=$isCompleted',
        tag: 'WorkoutPlayer');

    // If we need the workout log ID, use the one passed to the widget
    if (_actualWorkoutLogId <= 0 && widget.workoutLog.id > 0) {
      _actualWorkoutLogId = widget.workoutLog.id;
    }

    // Show appropriate feedback based on completion status
    if (mounted) {
      if (!isCompleted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exercise skipped'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 1),
          ),
        );
      }
    }

    // Move to next exercise or complete workout
    if (mounted) {
      // Check if this was the last exercise
      bool isLastExercise = _currentExerciseIndex >= _allExercises.length - 1;

      if (isCompleted && isLastExercise) {
        // If this was the last exercise and it was completed, complete the workout
        setState(() => _isLoading = false);
        _completeWorkout();
      } else {
        // Otherwise, start rest timer or move to next exercise
        _startRestTimerIfNeeded();
        setState(() => _isLoading = false);
      }
    }
  }

  // Helper method removed as it's no longer used

  void _addTime({int seconds = 20}) {
    if (_showRestTimer) {
      // Add time to rest timer
      setState(() => _restTimeRemaining += seconds);
      // Removed log
    } else if (_isPlaying) {
      // Add time to exercise timer
      setState(() => _totalDuration += seconds);
      // Removed log
    }
  }

  // Skip the timer and mark exercise as completed
  Future<void> _skipExercise() async {
    // Cancel the current timer
    _timer?.cancel();

    // If we're in rest mode, skip the rest
    if (_showRestTimer) {
      _restTimer?.cancel();
      _restTimer = null;
      setState(() => _showRestTimer = false);
      _nextExercise();
      return;
    }

    // Otherwise, skip the exercise timer and mark as completed
    _videoController?.pause();
    setState(() {
      _isPlaying = false;
      _secondsElapsed = _totalDuration; // Set elapsed time to total duration
    });

    // Show a brief success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exercise completed!'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 1),
      ),
    );

    // Log the exercise as completed (not skipped)
    await _logExercisePerformance(isCompleted: true);
  }

  void _startRestTimerIfNeeded() {
    // Check if this was the last exercise
    if (_currentExerciseIndex >= _allExercises.length - 1) {
      // Removed log
      // Complete the workout if this was the last exercise
      _completeWorkout();
      return;
    }

    // Get rest duration from current exercise or use default
    try {
      _restDuration = _currentExerciseLog.restTime ?? 30;

      // Ensure rest duration is reasonable (between 5 seconds and 2 minutes)
      if (_restDuration < 5) {
        _restDuration = 5; // Minimum 5 seconds rest
      } else if (_restDuration > 120) {
        _restDuration = 120; // Maximum 2 minutes (120 seconds) rest
      }
    } catch (e) {
      // If any error occurs, use a default rest duration
      _restDuration = 30; // Default 30 seconds rest
    }

    if (_restDuration > 0) {
      // Provide haptic feedback to indicate rest period start
      HapticFeedback.lightImpact();

      // Play a subtle sound to indicate rest period
      SystemSound.play(SystemSoundType.alert);

      // Removed log

      // Update UI to show rest timer
      setState(() {
        _showRestTimer = true;
        _restTimeRemaining = _restDuration;
        _isPlaying = false;
      });

      // Cancel any existing rest timer to prevent duplicates
      _restTimer?.cancel();

      // Start a new rest timer with 1-second intervals
      _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        // Safety check: if widget is no longer in the tree, cancel timer
        if (!mounted) {
          timer.cancel();
          return;
        }

        setState(() {
          if (_restTimeRemaining > 1) {
            // Decrement the remaining time
            _restTimeRemaining--;

            // Provide haptic feedback for last 3 seconds as countdown
            if (_restTimeRemaining <= 3) {
              HapticFeedback.lightImpact();
              // Play a tick sound for the final countdown
              SystemSound.play(SystemSoundType.click);
            }
          } else {
            // Rest period complete
            timer.cancel();
            _restTimer = null;
            _showRestTimer = false;

            // Provide stronger haptic feedback at end of rest
            HapticFeedback.mediumImpact();

            // Show a brief message that rest is over
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Rest complete! Next exercise starting...'),
                backgroundColor: Colors.blue,
                duration: Duration(milliseconds: 1500),
              ),
            );

            // Move to next exercise
            _nextExercise();
          }
        });
      });
    } else {
      // No rest time configured, move directly to next exercise
      // Removed log
      _nextExercise();
    }
  }

  void _nextExercise() {
    // Only load next if not the last one
    if (_currentExerciseIndex < _allExercises.length - 1) {
      _loadExercise(_currentExerciseIndex + 1);
    } else {
      // Removed log
      _completeWorkout(); // Should have already been called by _startRestTimerIfNeeded
    }
  }

  // --- COMPLETE WORKOUT LOGIC ---
  Future<void> _completeWorkout() async {
    Logger.workout('Starting workout completion process',
        tag: 'WorkoutPlayer', force: true);
    Logger.workout('Current workout log: ${widget.workoutLog.toJson()}',
        tag: 'WorkoutPlayer', force: true);
    Logger.workout(
        'Current workout session log ID: ${widget.workoutSessionLogId}',
        tag: 'WorkoutPlayer',
        force: true);

    if (_isCompleting) {
      Logger.workout('Already completing workout, ignoring duplicate call',
          tag: 'WorkoutPlayer', force: true);
      return; // Prevent multiple calls
    }
    setState(() => _isCompleting = true);

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: AnimatedLoading()),
    );

    // Calculate total duration and calories (do this early in case we need to exit early)
    final totalWorkoutDuration = _calculateTotalWorkoutDuration();
    final estimatedCalories = _calculateEstimatedCalories(totalWorkoutDuration);

    // Ensure we have the actual workout log ID
    int workoutLogId =
        _actualWorkoutLogId > 0 ? _actualWorkoutLogId : widget.workoutLog.id;
    if (workoutLogId <= 0) {
      Logger.error('Invalid workoutLogId before completing workout',
          tag: 'WorkoutPlayer');
      if (mounted) Navigator.pop(context); // Dismiss loading
      setState(() => _isCompleting = false);

      // Instead of showing an error, just show a success message and navigate to completion screen
      // This allows the user to complete the workout even if the workout log ID is missing
      _completeWorkoutWithoutSaving(totalWorkoutDuration, estimatedCalories);
      return;
    }

    // Check if the workout day exists in the backend
    // We'll continue even if the workout day doesn't exist, as the emergency completion endpoint
    // will create a new workout day if needed
    if (widget.workoutLog.workoutDay?.id == null ||
        widget.workoutLog.workoutDay!.id <= 0) {
      Logger.warning(
          'Workout day ID is missing or invalid, but continuing with emergency completion',
          tag: 'WorkoutPlayer');
      // We'll still send the request to the emergency completion endpoint
      // which will create a new workout day if needed
    }

    // Complete the session
    try {
      Logger.workout('Marking workout day log $workoutLogId as complete',
          tag: 'WorkoutPlayer');

      // We'll skip trying to complete the workout session log with ID 243
      // since it's causing a 404 error. Instead, we'll focus on completing the workout log.
      Logger.workout(
          'Skipping workout session log completion due to 404 errors. Proceeding with workout log completion.',
          tag: 'WorkoutPlayer');

      // If you want to debug the issue with session log 243, uncomment this code:
      /*
      // First, try to complete the individual workout session if it exists
      int sessionLogId = widget.workoutSessionLogId;

      // If we don't have a valid session log ID, try to create one
      if (sessionLogId <= 0) {
        try {
          // Get the workout session ID from the workout log
          final workoutDayId = widget.workoutLog.workoutDay?.id ?? 0;

          // Find the session ID from the workout log
          int sessionId = 0;
          final sections = widget.workoutLog.workoutDay?.sections;
          if (sections != null && sections.isNotEmpty) {
            for (final section in sections) {
              if (section.workoutSessionId != null && section.workoutSessionId! > 0) {
                sessionId = section.workoutSessionId!;
                break;
              }
            }
          }

          if (sessionId > 0 && workoutDayId > 0) {
            Logger.workout('Creating workout session log for session ID: $sessionId and day ID: $workoutDayId', tag: 'WorkoutPlayer');

            // Create a new workout session log
            final sessionLogResponse = await _apiService.createWorkoutSessionLog(
              workoutSessionId: sessionId,
              workoutDayId: workoutDayId,
            );

            // Get the new session log ID
            if (sessionLogResponse.containsKey('id')) {
              sessionLogId = sessionLogResponse['id'];
              Logger.workout('Created workout session log with ID: $sessionLogId', tag: 'WorkoutPlayer');
            } else {
              Logger.error('Failed to create workout session log: Invalid response', tag: 'WorkoutPlayer');
            }
          } else {
            Logger.error('Cannot create workout session log: Missing session ID ($sessionId) or workout day ID ($workoutDayId)', tag: 'WorkoutPlayer');
          }
        } catch (e) {
          Logger.error('Error creating workout session log: $e', tag: 'WorkoutPlayer');
          // Continue with workout log completion even if session log creation fails
        }
      }

      // Now try to complete the session log if we have a valid ID
      if (sessionLogId > 0) {
        try {
          Logger.workout('Attempting to complete workout session log ID: $sessionLogId', tag: 'WorkoutPlayer');

          final sessionResponse = await _apiService
              .completeWorkoutSessionLog(workoutSessionLogId: sessionLogId)
              .timeout(const Duration(seconds: 15), onTimeout: () {
                Logger.error('Timeout while marking workout session log as complete', tag: 'WorkoutPlayer');
                throw TimeoutException('Workout session log completion timed out');
              });

          Logger.workout('Successfully completed workout session: $sessionLogId. Response: $sessionResponse', tag: 'WorkoutPlayer');
        } catch (e) {
          Logger.error('Error completing workout session: $e', tag: 'WorkoutPlayer');
          // Continue with workout log completion even if session completion fails
          // This allows the user to complete the workout even if there are issues with the session log
        }
      } else {
        Logger.warning('No valid workout session log ID available. Skipping session completion.', tag: 'WorkoutPlayer');
      }
      */

      // Use the direct_complete endpoint instead of trying to complete a specific session log
      Logger.workout('Using direct_complete endpoint to complete workout',
          tag: 'WorkoutPlayer');

      // Parse and Format Date
      DateTime dateObj;
      try {
        dateObj = DateTime.parse(widget.workoutLog.date);
      } catch (e) {
        Logger.error(
            'Error parsing date: ${widget.workoutLog.date}. Using today\'s date.',
            tag: 'WorkoutPlayer');
        dateObj = DateTime.now();
      }
      final formattedDate =
          '${dateObj.year}-${dateObj.month.toString().padLeft(2, '0')}-${dateObj.day.toString().padLeft(2, '0')}';

      // Calculate duration and calories
      final totalWorkoutDuration = _calculateTotalWorkoutDuration();
      final estimatedCalories =
          _calculateEstimatedCalories(totalWorkoutDuration);

      // Get Session Name/Order
      String? sessionName;
      int? sessionOrder;
      final sessions = widget.workoutLog.workoutDay?.sessions;
      if (sessions != null) {
        for (final session in sessions) {
          if (session.id == widget.workoutSessionLogId) {
            sessionName = session.name;
            sessionOrder = session.order;
            break;
          }
        }
      }

      // <<< ADD DETAILED LOGGING HERE >>>
      Logger.debug('--- Preparing directCompleteData ---',
          tag: 'WorkoutPlayer');
      Logger.debug(
          'Workout Log ID (widget.workoutLog.id): ${widget.workoutLog.id}',
          tag: 'WorkoutPlayer');
      Logger.debug(
          'Workout Day ID (widget.workoutLog.workoutDay?.id): ${widget.workoutLog.workoutDay?.id}',
          tag: 'WorkoutPlayer');
      Logger.debug(
          'Workout Session Log ID (widget.workoutSessionLogId): ${widget.workoutSessionLogId}',
          tag: 'WorkoutPlayer');
      Logger.debug('Formatted Date: $formattedDate', tag: 'WorkoutPlayer');
      Logger.debug('Total Duration (mins): ${totalWorkoutDuration ~/ 60}',
          tag: 'WorkoutPlayer');
      Logger.debug('Estimated Calories: $estimatedCalories',
          tag: 'WorkoutPlayer');
      Logger.debug('Session Name: $sessionName', tag: 'WorkoutPlayer');
      Logger.debug('Session Order: $sessionOrder', tag: 'WorkoutPlayer');
      Logger.debug('-------------------------------------',
          tag: 'WorkoutPlayer');

      // Prepare Data for API
      // CRITICAL FIX: Check if workout day ID is valid
      // This is a common issue that causes 404 errors
      int workoutDayId = widget.workoutLog.workoutDay?.id ?? 0;
      int workoutSessionId = widget.workoutSessionLogId;

      // CRITICAL FIX: Use the correct workout day ID from the workout log
      // Don't hardcode the workout day ID - use the actual ID from the data
      if (workoutDayId <= 0) {
        Logger.error('Invalid workout day ID: $workoutDayId',
            tag: 'WorkoutPlayer');
        throw Exception('Invalid workout day ID: $workoutDayId');
      }

      final directCompleteData = {
        'date': formattedDate,
        'workout_day_id': workoutDayId, // Use the fixed workout day ID
        'workout_session_id': workoutSessionId,
        'duration_minutes': totalWorkoutDuration ~/ 60,
        'calories_burned': estimatedCalories,
        'notes': 'Completed via workout player',
        if (sessionName != null) 'session_name': sessionName,
        if (sessionOrder != null) 'session_order': sessionOrder,
        // Add fallback session name if not provided
        if (sessionName == null) 'session_name': 'Workout Session',
      };

      // Log before API call
      Logger.workout(
          'Completing workout session: day_id=${widget.workoutLog.workoutDay?.id}, session_id=${widget.workoutSessionLogId}',
          tag: 'WorkoutPlayer');

      // Call API and Handle Response
      try {
        await _apiService
            .directCompleteWorkoutSession(data: directCompleteData)
            .timeout(const Duration(seconds: 15), onTimeout: () {
          Logger.error('Timeout while completing workout session',
              tag: 'WorkoutPlayer');
          throw TimeoutException('Workout completion timed out');
        });
        Logger.workout('Workout session completed successfully',
            tag: 'WorkoutPlayer');

        // Dismiss loading indicator ON SUCCESS before potential navigation
        if (mounted) Navigator.pop(context);

        // Update provider state to reflect completion
        if (mounted) {
          HapticFeedback.heavyImpact();
          SystemSound.play(SystemSoundType.alert);
          final workoutProvider =
              Provider.of<WorkoutProvider>(context, listen: false);
          // Use the validated workoutLogId here
          await workoutProvider.markWorkoutComplete(
              workoutLogId, widget.workoutLog.date);

          // Navigate to Completion Screen
          final representativeWorkoutLog = WorkoutLog(
            id: workoutLogId,
            workoutDay: widget.workoutLog.workoutDay,
            date: widget.workoutLog.date,
            isCompleted: true,
            sections: widget.workoutLog.sections,
          );
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => WorkoutCompletionScreen(
                  workoutLog: representativeWorkoutLog,
                  totalDuration: totalWorkoutDuration,
                  estimatedCalories: estimatedCalories,
                ),
              ),
            );
          }
        }
      } catch (e) {
        Logger.error(
            'Error completing workout session API call: ${e.toString()}',
            tag: 'WorkoutPlayer');
        // Dismiss loading indicator on error before showing message/fake completion
        if (mounted) Navigator.pop(context);

        // Create a fake completion response
        Map<String, dynamic> fakeResponse = {
          'id': workoutLogId,
          'is_completed': true,
          'completion_time': DateTime.now().toIso8601String(),
          'workout_day': widget.workoutLog.workoutDay?.id,
          'workout_session': widget.workoutSessionLogId,
        };

        // Log the fake response
        Logger.workout('Created fake completion response: $fakeResponse',
            tag: 'WorkoutPlayer');
        Logger.workout('Using local completion due to API error',
            tag: 'WorkoutPlayer');

        // Show a warning to the user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Workout completed locally. Server sync failed: ${e.toString().split('Exception:').last}'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 5),
            ),
          );
          // Still navigate to completion screen even on API error
          final representativeWorkoutLog = WorkoutLog(
            id: workoutLogId,
            workoutDay: widget.workoutLog.workoutDay,
            date: widget.workoutLog.date,
            isCompleted: true, // Mark as completed locally
            sections: widget.workoutLog.sections,
          );
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => WorkoutCompletionScreen(
                workoutLog: representativeWorkoutLog,
                totalDuration: totalWorkoutDuration,
                estimatedCalories: estimatedCalories,
              ),
            ),
          );
        }
      } // End API call catch
    } finally {
      // Ensure loading indicator is always dismissed if it wasn't already
      // and reset the completion flag
      // Check if the dialog is still open before popping
      // Note: This might not be perfectly reliable, better to handle pop in success/error blocks
      // if (Navigator.of(context, rootNavigator: true).canPop()) {
      //    Navigator.pop(context);
      // }
      if (mounted) {
        setState(() => _isCompleting = false);
      }
    }
  }

  // Method removed: _showCompletionDialog

  // Calculate total workout duration in seconds
  int _calculateTotalWorkoutDuration() {
    int totalSeconds = 0;
    for (var exercise in _allExercises) {
      totalSeconds += exercise.durationSeconds;
    }
    return totalSeconds;
  }

  // Method removed: _formatDuration

  // Calculate estimated calories burned (very rough estimate)
  int _calculateEstimatedCalories(int durationSeconds) {
    // Very simple calculation - about 8-10 calories per minute for moderate exercise
    return (durationSeconds / 60 * 9).round();
  }

  // Helper method to complete workout without saving to backend
  void _completeWorkoutWithoutSaving(
      int totalWorkoutDuration, int estimatedCalories) {
    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Workout completed successfully!'),
        backgroundColor: Colors.green,
      ),
    );

    // Navigate to completion screen
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => WorkoutCompletionScreen(
            workoutLog: widget.workoutLog,
            totalDuration: totalWorkoutDuration,
            estimatedCalories: estimatedCalories,
          ),
        ),
      );
    }
  }

  // Helper methods for showing messages are now handled directly in the relevant functions

  // --- END COMPLETE WORKOUT LOGIC ---

  @override
  void dispose() {
    // Cancel all timers
    _timer?.cancel();
    _restTimer?.cancel();

    // Dispose video controller
    _videoController?.dispose();

    // Remove lifecycle observer
    WidgetsBinding.instance.removeObserver(this);

    // Allow screen to turn off again
    WakelockPlus.disable();

    // Reset orientation settings
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Removed log
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Check if this workout is for today
    bool isFutureWorkout = false;
    try {
      final dateStr = widget.workoutLog.date;
      final logDate = DateTime.parse(dateStr);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final daysSinceToday = logDate.difference(today).inDays;
      isFutureWorkout = daysSinceToday > 0;

      // If it's a future workout, show error and navigate back
      if (isFutureWorkout) {
        // Use a post-frame callback to show the snackbar and navigate back
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('You can only complete workouts for today'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
          Navigator.of(context).pop();
        });
      }
    } catch (e) {
      // If there's an error parsing the date, continue
    }

    // Add PopScope to handle back button press (modern replacement for WillPopScope)
    return PopScope(
      canPop: false, // Prevent default back navigation
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        // This is called when the user tries to pop the route
        if (didPop) {
          // If the system allowed the pop, we shouldn't get here
          return;
        }

        // Cancel any active timers
        _timer?.cancel();
        _restTimer?.cancel();

        // Handle custom navigation
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        } else {
          // If we can't pop, navigate to the main screen
          NavigationService.navigateToReplacementNamed(NavigationService.main);
        }
      },
      child: Scaffold(
        backgroundColor: const Color.fromRGBO(243, 243, 243, 1),
        body: Column(
          children: [
            if (_isOffline) const OfflineBanner(),
            Expanded(
              child: _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading && !_showRestTimer) {
      return const Center(child: AnimatedLoading());
    }
    if (_error != null) {
      return CustomErrorWidget(
        message: _error!,
        onRetry: () {
          setState(() {
            _error = null;
            _loadExercise(_currentExerciseIndex);
          });
        },
      );
    }

    final exercise = _currentExerciseDetail;
    final String? displayUrl =
        exercise.mediaFileUrl ?? exercise.videoUrl ?? exercise.imageUrl;

    // Check if the URL is a video format
    final bool isVideo = displayUrl != null && _isVideoUrl(displayUrl);

    // Check if it's a GIF
    final bool isGif =
        displayUrl != null && displayUrl.toLowerCase().endsWith('.gif');

    // Ensure we have a valid URL for GIFs
    final String gifUrl = isGif ? displayUrl : '';

    // For regular images (excluding GIFs), use the image display path
    final String? imageUrlForDisplay =
        (!isVideo && !isGif) ? displayUrl : (exercise.imageUrl ?? displayUrl);

    return Stack(
      children: [
        // Media Background with fade transition
        Positioned.fill(
          child: AnimatedSwitcher(
            duration: const Duration(
                milliseconds:
                    1500), // Longer fade transition for smoother experience
            child: isVideo
                ? FutureBuilder(
                    key: ValueKey<String>(
                        'video-${_currentExerciseDetail.id}'), // Key for transition
                    future: _initializeVideoPlayerFuture,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.done &&
                          _videoController != null &&
                          _videoController!.value.isInitialized) {
                        // Calculate aspect ratio with fallback
                        final aspectRatio =
                            _videoController!.value.aspectRatio > 0
                                ? _videoController!.value.aspectRatio
                                : 16 / 9;

                        // Get screen dimensions
                        final screenWidth = MediaQuery.of(context).size.width;
                        final screenHeight = MediaQuery.of(context).size.height;

                        // Calculate dimensions to maintain aspect ratio while covering the screen
                        double videoWidth, videoHeight;

                        if (screenWidth / screenHeight > aspectRatio) {
                          // Screen is wider than video
                          videoWidth = screenWidth;
                          videoHeight = screenWidth / aspectRatio;
                        } else {
                          // Screen is taller than video
                          videoHeight = screenHeight;
                          videoWidth = screenHeight * aspectRatio;
                        }

                        return Container(
                          color: Colors.black, // Background color
                          child: Center(
                            child: SizedBox(
                              width: videoWidth,
                              height: videoHeight,
                              child: VideoPlayer(_videoController!),
                            ),
                          ),
                        );
                      } else if (snapshot.hasError) {
                        Logger.error('Error loading video: ${snapshot.error}',
                            tag: 'WorkoutPlayer');
                        return Container(
                            color: Colors.black,
                            child: const Center(
                                child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.error, color: Colors.red, size: 50),
                                SizedBox(height: 16),
                                Text(
                                  'Could not load exercise video',
                                  style: TextStyle(color: Colors.white70),
                                )
                              ],
                            )));
                      } else {
                        // Show loading indicator with black background
                        return Container(
                          color: Colors.black,
                          child: const Center(child: AnimatedLoading()),
                        );
                      }
                    },
                  )
                : isGif
                    ? gif_player.GifPlayer(
                        key: ValueKey<String>(
                            'gif-${_currentExerciseDetail.id}'), // Key for transition
                        gifUrl: gifUrl,
                        width: MediaQuery.of(context).size.width,
                        height: MediaQuery.of(context).size.height,
                        fit: BoxFit.cover,
                        placeholder: Container(
                          color: Colors.black,
                          child: const Center(child: AnimatedLoading()),
                        ),
                      )
                    : ImageUtils.getNetworkImageWithFallback(
                        key: ValueKey<String>(
                            'image-${_currentExerciseDetail.id}'), // Key for transition
                        imageUrl: imageUrlForDisplay,
                        width: MediaQuery.of(context).size.width,
                        height: MediaQuery.of(context).size.height,
                        fit: BoxFit.cover,
                        placeholder: Container(
                          color: Colors.black,
                          child: const Center(child: AnimatedLoading()),
                        ),
                        fallbackIcon: Icons.fitness_center,
                        fallbackIconColor: Colors.white70,
                      ),
          ),
        ),

        // Gradient overlay
        Positioned(
          bottom: 0, // Start gradient from bottom
          left: 0,
          right: 0,
          height: MediaQuery.of(context).size.height * 0.6, // Adjust height
          child: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, Color.fromRGBO(17, 18, 20, 1)],
                stops: [0.0, 0.5], // Adjust stops for smoother transition
              ),
            ),
          ),
        ),

        // Exercise information
        if (!_showRestTimer)
          Positioned(
            bottom: 150, // Position above controls
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Exercise type badge
                if (exercise.exerciseType != null ||
                    exercise.equipmentRequired != null)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                          color: Colors.white.withAlpha(128)), // 50% opacity
                      color: Colors.black.withAlpha(77), // 30% opacity
                    ),
                    child: Text(
                      exercise.exerciseType ??
                          exercise.equipmentRequired ??
                          'Info',
                      style: const TextStyle(
                          color: Colors.white,
                          fontFamily: 'Work Sans',
                          fontSize: 14),
                    ),
                  ),
                const SizedBox(height: 12),
                // Exercise name
                Text(
                  exercise.name,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      color: Colors.white,
                      fontFamily: 'Work Sans',
                      fontSize: 36,
                      fontWeight: FontWeight.w600,
                      height: 1.2),
                ),
                const SizedBox(height: 8),
                // Display Planned Sets/Reps
                Text(
                  '$_plannedSets Sets / $_plannedReps Reps ${_plannedWeight != null ? '/ $_plannedWeight kg' : ''}',
                  style: TextStyle(
                      color: Colors.white.withAlpha(204), // 80% opacity
                      fontSize: 16,
                      fontFamily: 'Work Sans'),
                ),
                const SizedBox(height: 24),
                // Timer Display with progress indicator
                Column(
                  children: [
                    Text(
                      (_isPlaying)
                          ? '${_totalDuration - _secondsElapsed}s'
                          : '${_totalDuration}s',
                      style: const TextStyle(
                          color: Colors.white,
                          fontFamily: 'Work Sans',
                          fontSize: 48,
                          fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    // Progress indicator
                    if (_totalDuration > 0)
                      SizedBox(
                        width: 200,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10),
                          child: LinearProgressIndicator(
                            value: _secondsElapsed / _totalDuration,
                            backgroundColor:
                                Colors.white.withAlpha(51), // 20% opacity
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                Color.fromRGBO(255, 127, 54, 1)),
                            minHeight: 8,
                          ),
                        ),
                      ),
                  ],
                ),
                // Removed Performance Input Row
              ],
            ),
          ),

        // Play/Pause/Log buttons
        if (!_showRestTimer)
          Positioned(
            bottom: 32,
            left: 16,
            right: 16,
            child: Row(
              // Keep Row for button layout
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildControlButton(
                    icon: Icons.skip_next, onTap: _skipExercise, label: 'Skip'),
                GestureDetector(
                  onTap: _togglePlayPause,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(28),
                      color: const Color.fromRGBO(255, 127, 54, 1),
                      boxShadow: const [
                        BoxShadow(
                            color: Color.fromRGBO(255, 128, 54, 0.25),
                            blurRadius: 8,
                            spreadRadius: 2)
                      ],
                    ),
                    child: Icon(_isPlaying ? Icons.pause : Icons.play_arrow,
                        color: Colors.white, size: 40),
                  ),
                ),
                _buildControlButton(
                    icon: Icons.add_circle_outline,
                    onTap: () => _addTime(seconds: 20),
                    label: '+20 sec'),
              ],
            ),
          ),

        // Back button
        Positioned(
          top: 60,
          left: 16,
          child: InkWell(
            onTap: () {
              // Cancel any active timers
              _timer?.cancel();
              _restTimer?.cancel();

              // Use the same navigation logic as in PopScope
              if (Navigator.canPop(context)) {
                Navigator.of(context).pop();
              } else {
                // If we can't pop, navigate to the main screen
                NavigationService.navigateToReplacementNamed(
                    NavigationService.main);
              }
            },
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18),
                  color: const Color.fromRGBO(255, 255, 255, 0.32)),
              padding: const EdgeInsets.all(12),
              child: const Icon(Icons.arrow_back, color: Colors.white),
            ),
          ),
        ),

        // Rest Timer Overlay with improved design
        if (_showRestTimer)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(230), // 90% opacity
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(100), // 40% opacity
                    blurRadius: 10,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Rest indicator with badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(51), // 20% opacity
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.self_improvement,
                              color: Colors.white70, size: 24),
                          SizedBox(width: 8),
                          Text(
                            'REST TIME',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white70,
                              fontFamily: 'Work Sans',
                              letterSpacing: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 30),

                    // Timer with circular progress indicator
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        SizedBox(
                          width: 200,
                          height: 200,
                          child: CircularProgressIndicator(
                            value: _restTimeRemaining / _restDuration,
                            strokeWidth: 8,
                            backgroundColor:
                                Colors.white.withAlpha(51), // 20% opacity
                            valueColor: const AlwaysStoppedAnimation<Color>(
                              Color.fromRGBO(255, 127, 54, 1),
                            ),
                          ),
                        ),
                        Text(
                          '$_restTimeRemaining',
                          style: const TextStyle(
                            fontSize: 80,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontFamily: 'Work Sans',
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 30),

                    // Next exercise preview
                    if (_currentExerciseIndex + 1 < _allExercises.length)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(26), // 10% opacity
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'NEXT EXERCISE',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white60,
                                fontFamily: 'Work Sans',
                                letterSpacing: 1.0,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _allExercises[_currentExerciseIndex + 1]
                                  .exercise
                                  .name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontFamily: 'Work Sans',
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                    const SizedBox(height: 40),

                    // Add time buttons with improved styling
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildRestTimeButton(seconds: 10),
                        const SizedBox(width: 20),
                        _buildRestTimeButton(seconds: 20),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Skip button with improved styling
                    TextButton.icon(
                      onPressed: () {
                        _restTimer?.cancel();
                        _restTimer = null;
                        setState(() => _showRestTimer = false);
                        _nextExercise();
                      },
                      icon: const Icon(Icons.skip_next, color: Colors.white70),
                      label: const Text(
                        'Skip Rest',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Work Sans',
                        ),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        backgroundColor:
                            Colors.white.withAlpha(26), // 10% opacity
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30)),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  // Helper to build control buttons (+20s, Skip)
  Widget _buildControlButton(
      {required IconData icon,
      required VoidCallback onTap,
      required String label}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(24),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withAlpha(51)), // 20% opacity
            child: Icon(icon, color: Colors.white, size: 28),
          ),
        ),
        const SizedBox(height: 4),
        Text(label,
            style: const TextStyle(
                color: Colors.white70, fontSize: 12, fontFamily: 'Work Sans')),
      ],
    );
  }

  // Helper to build rest time buttons (+10s, +20s)
  Widget _buildRestTimeButton({required int seconds}) {
    return ElevatedButton(
      onPressed: () => _addTime(seconds: seconds),
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color.fromRGBO(255, 127, 54, 1),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 4,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.add, size: 20),
          const SizedBox(width: 4),
          Text(
            '${seconds}s',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Work Sans',
            ),
          ),
        ],
      ),
    );
  }

  // REMOVED _buildPerformanceInputRow, _buildInputChip, _buildWeightInputChip
}
