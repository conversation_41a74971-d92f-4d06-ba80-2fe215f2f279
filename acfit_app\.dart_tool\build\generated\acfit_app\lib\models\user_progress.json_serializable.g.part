// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProgress _$UserProgressFromJson(Map<String, dynamic> json) => UserProgress(
      id: (json['id'] as num).toInt(),
      user: (json['user'] as num).toInt(),
      startDate: json['start_date'] == null
          ? null
          : DateTime.parse(json['start_date'] as String),
      totalWorkoutsCompleted: (json['total_workouts_completed'] as num).toInt(),
      totalWorkoutsPlanned: (json['total_workouts_planned'] as num).toInt(),
      totalCaloriesBurned: (json['total_calories_burned'] as num).toInt(),
      totalExerciseMinutes: (json['total_exercise_minutes'] as num).toInt(),
      totalSteps: (json['total_steps'] as num).toInt(),
      totalMealsCompleted: (json['total_meals_completed'] as num).toInt(),
      totalMealsPlanned: (json['total_meals_planned'] as num).toInt(),
      totalCaloriesConsumed: (json['total_calories_consumed'] as num).toInt(),
      totalProteinConsumed: (json['total_protein_consumed'] as num).toDouble(),
      totalCarbsConsumed: (json['total_carbs_consumed'] as num).toDouble(),
      totalFatConsumed: (json['total_fat_consumed'] as num).toDouble(),
      lastWorkoutDate: json['last_workout_date'] as String?,
      lastMealDate: json['last_meal_date'] as String?,
      streakDays: (json['streak_days'] as num).toInt(),
      currentStreak: (json['current_streak'] as num).toInt(),
      longestStreak: (json['longest_streak'] as num).toInt(),
      workoutDay: (json['workout_day'] as num?)?.toInt() ?? 1,
      mealDay: (json['meal_day'] as num?)?.toInt() ?? 1,
      notes: json['notes'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$UserProgressToJson(UserProgress instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user': instance.user,
      'start_date': instance.startDate?.toIso8601String(),
      'total_workouts_completed': instance.totalWorkoutsCompleted,
      'total_workouts_planned': instance.totalWorkoutsPlanned,
      'total_calories_burned': instance.totalCaloriesBurned,
      'total_exercise_minutes': instance.totalExerciseMinutes,
      'total_steps': instance.totalSteps,
      'total_meals_completed': instance.totalMealsCompleted,
      'total_meals_planned': instance.totalMealsPlanned,
      'total_calories_consumed': instance.totalCaloriesConsumed,
      'total_protein_consumed': instance.totalProteinConsumed,
      'total_carbs_consumed': instance.totalCarbsConsumed,
      'total_fat_consumed': instance.totalFatConsumed,
      'last_workout_date': instance.lastWorkoutDate,
      'last_meal_date': instance.lastMealDate,
      'streak_days': instance.streakDays,
      'current_streak': instance.currentStreak,
      'longest_streak': instance.longestStreak,
      'workout_day': instance.workoutDay,
      'meal_day': instance.mealDay,
      'notes': instance.notes,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
