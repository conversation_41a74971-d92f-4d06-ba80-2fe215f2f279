#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
django.setup()

from django.test import Client
from accounts.models import User, UserProfile
from workouts.models import UserWorkoutPlan, WorkoutPlan, WorkoutVideo
from workouts.serializers import WorkoutVideoSerializer

def test_video_functionality():
    print("=== Testing Video Functionality ===\n")
    
    # Check videos in database
    videos = WorkoutVideo.objects.all()
    print(f"Videos in database: {videos.count()}")
    for video in videos:
        print(f"- {video.title} (Plan: {video.workout_plan.name}, Order: {video.order})")
    print()
    
    # Check users
    users = User.objects.all()
    print(f"Users in database: {users.count()}")
    
    if users.exists():
        user = users.first()
        print(f"Testing with user: {user.username}")
        
        # Check user profile
        try:
            user_profile = UserProfile.objects.get(user=user)
            print(f"User profile found: {user_profile}")
            
            # Check user workout plans
            user_workout_plans = UserWorkoutPlan.objects.filter(user=user_profile)
            print(f"User workout plans: {user_workout_plans.count()}")
            
            for uwp in user_workout_plans:
                print(f"- {uwp.workout_plan.name} (Active: {uwp.is_active})")
                
                # Check videos for this plan
                plan_videos = WorkoutVideo.objects.filter(workout_plan=uwp.workout_plan)
                print(f"  Videos for this plan: {plan_videos.count()}")
                for video in plan_videos:
                    print(f"    - {video.title}")
            
        except UserProfile.DoesNotExist:
            print("No user profile found for this user")
    
    print("\n=== Testing Serializer ===")
    if videos.exists():
        video = videos.first()
        serializer = WorkoutVideoSerializer(video)
        print(f"Serialized video data: {serializer.data}")
    
    print("\n=== Testing API Endpoint ===")
    client = Client()
    
    # Test without authentication
    response = client.get('/api/workouts/videos/')
    print(f"Unauthenticated request status: {response.status_code}")
    
    # Test with authentication (if possible)
    if users.exists():
        user = users.first()
        client.force_login(user)
        response = client.get('/api/workouts/videos/')
        print(f"Authenticated request status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response data: {response.json()}")
        else:
            print(f"Response content: {response.content.decode()}")

if __name__ == "__main__":
    test_video_functionality()
