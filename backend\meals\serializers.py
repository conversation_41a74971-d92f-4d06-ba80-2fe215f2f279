from rest_framework import serializers
from .models import (
    Meal, MealCategory, MealVariety, MealTiming, MealSubstitution,
    MealLog, MealPlan, UserMealPlan, DailyMealPlan, MealSchedule,
    HydrationLog, MealReminder # Removed MealIngredient, Ingredient, GroceryList, GroceryItem
)

# Removed IngredientSerializer
# Removed MealIngredientSerializer

# Serializer for MealCategory model
class MealCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = MealCategory
        fields = '__all__'

# Serializer for Meal model
class MealSerializer(serializers.ModelSerializer):
    # ingredients = MealIngredientSerializer(many=True, read_only=True, source='mealingredient_set') # Removed field
    category_name = serializers.CharField(source='category.name', read_only=True)
    meal_image_url = serializers.SerializerMethodField() # Add method field for image URL

    class Meta:
        model = Meal
        fields = '__all__' # Ensure 'meal_image_url' is included if not using '__all__'

    def get_meal_image_url(self, obj):
        request = self.context.get('request')
        # Prioritize uploaded image, fallback to URL field
        if obj.meal_image and hasattr(obj.meal_image, 'url'):
            if request:
                return request.build_absolute_uri(obj.meal_image.url)
            return obj.meal_image.url
        # Note: image_url field was removed, only meal_image field exists now
        return None # Return None if neither is set

# Serializer for MealVariety model
class MealVarietySerializer(serializers.ModelSerializer):
    class Meta:
        model = MealVariety
        fields = '__all__'
        read_only_fields = ('user',)

# Serializer for MealTiming model
class MealTimingSerializer(serializers.ModelSerializer):
    class Meta:
        model = MealTiming
        fields = '__all__'
        read_only_fields = ('user',)

# Serializer for MealSubstitution model
class MealSubstitutionSerializer(serializers.ModelSerializer):
    original_meal_name = serializers.CharField(source='original_meal.name', read_only=True)
    substitute_meal_name = serializers.CharField(source='substitute_meal.name', read_only=True)

    class Meta:
        model = MealSubstitution
        fields = '__all__'

# Serializer for MealLog model
class MealLogSerializer(serializers.ModelSerializer):
    meal_name = serializers.CharField(source='meal.name', read_only=True)
    # FIX: Explicitly define 'date' as DateField and specify format
    date = serializers.DateField(format="%Y-%m-%d")

    class Meta:
        model = MealLog
        fields = '__all__'
        read_only_fields = ('user', 'completion_time', 'created_at', 'updated_at')

# Serializer for MealPlan model (Global Template)
class MealPlanSerializer(serializers.ModelSerializer):
    cover_image_url = serializers.SerializerMethodField()

    class Meta:
        model = MealPlan
        fields = '__all__' # Add 'cover_image_url'

    def get_cover_image_url(self, obj):
        request = self.context.get('request')
        if obj.cover_image and hasattr(obj.cover_image, 'url'):
            if request:
                return request.build_absolute_uri(obj.cover_image.url)
            return obj.cover_image.url
        return None

# Serializer for DailyMealPlan model
class DailyMealPlanSerializer(serializers.ModelSerializer):
    breakfast_details = MealSerializer(source='breakfast', read_only=True)
    lunch_details = MealSerializer(source='lunch', read_only=True)
    dinner_details = MealSerializer(source='dinner', read_only=True)

    class Meta:
        model = DailyMealPlan
        fields = '__all__'

# Serializer for MealSchedule model
class MealScheduleSerializer(serializers.ModelSerializer):
    # Include details for linked DailyMealPlans
    monday_details = DailyMealPlanSerializer(source='monday', read_only=True)
    tuesday_details = DailyMealPlanSerializer(source='tuesday', read_only=True)
    wednesday_details = DailyMealPlanSerializer(source='wednesday', read_only=True)
    thursday_details = DailyMealPlanSerializer(source='thursday', read_only=True)
    friday_details = DailyMealPlanSerializer(source='friday', read_only=True)
    saturday_details = DailyMealPlanSerializer(source='saturday', read_only=True)
    sunday_details = DailyMealPlanSerializer(source='sunday', read_only=True)
    # Explicitly include the days field
    days = serializers.JSONField(required=False)

    class Meta:
        model = MealSchedule
        fields = '__all__'

# Removed GroceryListSerializer
# Removed GroceryItemSerializer

# Serializer for HydrationLog model
class HydrationLogSerializer(serializers.ModelSerializer):
    # FIX: Explicitly define 'date' as DateField and specify format
    date = serializers.DateField(format="%Y-%m-%d")

    class Meta:
        model = HydrationLog
        fields = '__all__'
        read_only_fields = ('user',)

# --- ADD UserMealPlanSerializer ---
class UserMealPlanSerializer(serializers.ModelSerializer):
    # Optionally include nested serializers for related fields if needed
    # meal_plan_details = MealPlanSerializer(source='meal_plan', read_only=True)
    # user_details = UserProfileSerializer(source='user', read_only=True) # Need to import UserProfileSerializer

    class Meta:
        model = UserMealPlan
        fields = '__all__'
        read_only_fields = ('user', 'created_at', 'updated_at', 'version', 'parent_plan', 'is_modified', 'modifications') # User is set implicitly or via signals

# --- ADD MealReminderSerializer ---
class MealReminderSerializer(serializers.ModelSerializer):
    class Meta:
        model = MealReminder
        fields = '__all__'
        read_only_fields = ('user',) # User is set in perform_create

# Add other meal-related serializers here as needed
