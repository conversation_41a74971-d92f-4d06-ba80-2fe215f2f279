#!/usr/bin/env python
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
django.setup()

from accounts.models import User, UserProfile
from workouts.models import UserWorkoutPlan, WorkoutPlan

def test_video_http_endpoints():
    print("=== Testing Video HTTP Endpoints ===\n")
    
    base_url = "http://127.0.0.1:8000"
    
    # Test 1: Unauthenticated request
    print("1. Testing unauthenticated request...")
    try:
        response = requests.get(f"{base_url}/api/workouts/videos/", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 401:
            print("   ✓ Correctly requires authentication")
        else:
            print(f"   Response: {response.text[:100]}...")
    except requests.exceptions.RequestException as e:
        print(f"   Error: {e}")
    
    # Test 2: Test by-plan endpoint (unauthenticated)
    print("\n2. Testing by-plan endpoint (unauthenticated)...")
    try:
        workout_plan = WorkoutPlan.objects.first()
        if workout_plan:
            response = requests.get(f"{base_url}/api/workouts/videos/by-plan/{workout_plan.id}/", timeout=5)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✓ Success! Found {len(data)} videos")
                for video in data:
                    print(f"     - {video['title']} (Order: {video['order']})")
            else:
                print(f"   Response: {response.text[:100]}...")
        else:
            print("   No workout plans found")
    except requests.exceptions.RequestException as e:
        print(f"   Error: {e}")
    
    # Test 3: Test specific video endpoint
    print("\n3. Testing specific video endpoint...")
    try:
        response = requests.get(f"{base_url}/api/workouts/videos/1/", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Success! Video: {data['title']}")
            print(f"     Duration: {data['duration_seconds']} seconds")
            print(f"     Order: {data['order']}")
        else:
            print(f"   Response: {response.text[:100]}...")
    except requests.exceptions.RequestException as e:
        print(f"   Error: {e}")
    
    # Test 4: Test admin endpoint
    print("\n4. Testing admin endpoint...")
    try:
        response = requests.get(f"{base_url}/acfit-admin/workouts/workoutvideo/", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✓ Admin page accessible!")
        elif response.status_code == 302:
            print("   → Redirected (probably to login)")
        else:
            print(f"   Response: {response.text[:100]}...")
    except requests.exceptions.RequestException as e:
        print(f"   Error: {e}")
    
    print("\n=== HTTP Test Complete ===")

if __name__ == "__main__":
    test_video_http_endpoints()
