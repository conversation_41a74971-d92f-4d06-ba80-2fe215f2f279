import 'package:json_annotation/json_annotation.dart';
import '../utils/logger.dart';
import '../utils/image_url_helper.dart';

part 'workout.g.dart'; // Add part directive for generated code

// Represents the base Workout Plan details
@JsonSerializable()
class WorkoutPlan {
  final int id;
  final String name;
  final String? description;
  @JsonKey(name: 'cover_image_url')
  final String? coverImageUrl;
  // Add other relevant fields from API if needed (e.g., difficulty, duration_weeks)

  WorkoutPlan({
    required this.id,
    required this.name,
    this.description,
    this.coverImageUrl,
  });

  factory WorkoutPlan.fromJson(Map<String, dynamic> json) =>
      _$WorkoutPlanFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutPlanToJson(this);
}

// Represents the details of a scheduled workout day
// Moved from workout_log.dart
@JsonSerializable()
class WorkoutDay {
  final int id;
  final String name;
  final String? description;
  @Json<PERSON>ey(name: 'is_rest_day', defaultValue: false)
  final bool isRestDay;
  @<PERSON>son<PERSON>ey(name: 'total_duration') // Match API log response
  final int? totalDuration;
  @JsonKey(name: 'calories_burn_estimate') // Match API log response
  final int? caloriesBurnEstimate;
  @JsonKey(name: 'cover_image_url')
  final String? coverImageUrl;
  @JsonKey(name: 'day_number')
  final int? dayNumber; // Add day number field
  final List<WorkoutSection>? sections; // Keep nullable
  @JsonKey(name: 'sessions')
  final List<WorkoutSession>? sessions; // Add sessions list

  WorkoutDay({
    required this.id,
    required this.name,
    this.description,
    required this.isRestDay,
    this.totalDuration,
    this.caloriesBurnEstimate,
    this.coverImageUrl,
    this.dayNumber,
    this.sections,
    this.sessions,
  });

  factory WorkoutDay.fromJson(Map<String, dynamic> json) {
    // Debug log the raw JSON for the workout day
    Logger.meal('WorkoutDay.fromJson raw data: $json', tag: 'WorkoutDay');

    // Check if the name field exists and log its value
    if (json.containsKey('name')) {
      Logger.meal('WorkoutDay name from JSON: "${json['name']}"',
          tag: 'WorkoutDay');
    } else {
      Logger.meal('WorkoutDay JSON does not contain name field',
          tag: 'WorkoutDay');
    }

    // Check if cover_image_url exists and log its value
    if (json.containsKey('cover_image_url')) {
      Logger.debug(
          'WorkoutDay cover_image_url from JSON: "${json['cover_image_url']}"',
          tag: 'WorkoutDay');

      // If the URL contains localhost or ********, transform it to use S3 URL
      if (json['cover_image_url'] != null &&
          (json['cover_image_url'].toString().contains('********:8000') ||
              json['cover_image_url'].toString().contains('localhost') ||
              json['cover_image_url'].toString().contains('127.0.0.1'))) {
        Logger.debug(
            'Transforming localhost URL in WorkoutDay: ${json['cover_image_url']}',
            tag: 'WorkoutDay');

        // Use the ImageUrlHelper to transform the URL
        String originalUrl = json['cover_image_url'].toString();
        String transformedUrl = ImageUrlHelper.transformImageUrl(originalUrl);
        json['cover_image_url'] = transformedUrl;

        Logger.debug('Transformed to: ${json['cover_image_url']}',
            tag: 'WorkoutDay');
      }
    } else {
      Logger.debug('WorkoutDay JSON does not contain cover_image_url field',
          tag: 'WorkoutDay');
    }

    // Ensure sessions is treated as a list, even if null or missing
    if (!json.containsKey('sessions') || json['sessions'] == null) {
      json['sessions'] = [];
    } else if (json['sessions'] is List) {
      // Filter out any null entries within the sessions list
      json['sessions'] = (json['sessions'] as List)
          .where((session) => session != null)
          .toList();
    } else {
      // If it's not null and not a list, treat it as an error and default to empty
      Logger.meal(
          'WorkoutDay.fromJson received non-list for sessions: ${json['sessions']}. Defaulting to empty list.',
          tag: 'WorkoutDay');
      json['sessions'] = [];
    }

    return _$WorkoutDayFromJson(json);
  }

  Map<String, dynamic> toJson() => _$WorkoutDayToJson(this);
}

// Represents a workout session (e.g., Morning Cardio, Evening Strength)
@JsonSerializable(explicitToJson: true)
class WorkoutSession {
  final int id;
  final String name;
  final String? description;
  final int order;
  @JsonKey(name: 'duration')
  final int? duration;
  @JsonKey(name: 'scheduled_time')
  final String? scheduledTime;
  @JsonKey(name: 'calories_burn_estimate')
  final int? caloriesBurnEstimate;
  @JsonKey(name: 'sections')
  final List<WorkoutSection>? sections;

  WorkoutSession({
    required this.id,
    required this.name,
    this.description,
    required this.order,
    this.duration,
    this.scheduledTime,
    this.caloriesBurnEstimate,
    this.sections,
  });

  factory WorkoutSession.fromJson(Map<String, dynamic> json) {
    try {
      // Handle null sections
      if (json['sections'] == null) {
        json = Map<String, dynamic>.from(json);
        json['sections'] = [];
      } else if (json['sections'] is List) {
        // Make a copy of the json object
        json = Map<String, dynamic>.from(json);

        // Create a new list for sections that filters out null values
        final sections = (json['sections'] as List)
            .where((section) => section != null)
            .toList();

        // Create a new list with valid sections
        List<Map<String, dynamic>> validSections = [];

        for (var section in sections) {
          if (section is Map<String, dynamic>) {
            // Make sure section has required fields
            if (!section.containsKey('id') ||
                !section.containsKey('name') ||
                !section.containsKey('order')) {
              continue;
            }

            // Make sure exercises is a list
            if (!section.containsKey('exercises') ||
                section['exercises'] == null) {
              section['exercises'] = [];
            }

            validSections.add(section);
          }
        }

        json['sections'] = validSections;
      }
      return _$WorkoutSessionFromJson(json);
    } catch (e) {
      // If anything goes wrong, return a default session with no sections
      return WorkoutSession(
        id: json['id'] is int ? json['id'] as int : 0,
        name:
            json['name'] is String ? json['name'] as String : 'Unknown Session',
        description: json['description'] as String?,
        order: json['order'] is int ? json['order'] as int : 0,
        duration: json['duration'] is int ? json['duration'] as int : null,
        scheduledTime: json['scheduled_time'] as String?,
        caloriesBurnEstimate: json['calories_burn_estimate'] is int
            ? json['calories_burn_estimate'] as int
            : null,
        sections: [],
      );
    }
  }

  Map<String, dynamic> toJson() => _$WorkoutSessionToJson(this);

  // Helper method to format the scheduled time
  String? getFormattedScheduledTime() {
    if (scheduledTime == null) return null;

    // Parse the time string (assuming format like "14:30:00")
    final parts = scheduledTime!.split(':');
    if (parts.length < 2) return scheduledTime;

    int hour = int.tryParse(parts[0]) ?? 0;
    int minute = int.tryParse(parts[1]) ?? 0;

    // Format in 24-hour format
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }
}

// Represents a section within a workout log (e.g., Warm-up, Main Workout)
// Moved from workout_log.dart
@JsonSerializable(explicitToJson: true)
class WorkoutSection {
  final int id;
  final String name;
  final String? description;
  final int order;
  @JsonKey(name: 'exercises')
  final List<WorkoutExerciseLog> exercises;
  @JsonKey(name: 'workout_session')
  final int? workoutSessionId;

  WorkoutSection({
    required this.id,
    required this.name,
    this.description,
    required this.order,
    required this.exercises,
    this.workoutSessionId,
  });

  factory WorkoutSection.fromJson(Map<String, dynamic> json) {
    try {
      // Handle null exercises
      if (json['exercises'] == null) {
        json = Map<String, dynamic>.from(json);
        json['exercises'] = [];
      } else if (json['exercises'] is List) {
        // Make a copy of the json object
        json = Map<String, dynamic>.from(json);

        // Create a new list for exercises that filters out null values
        final exercises = (json['exercises'] as List)
            .where((exercise) => exercise != null)
            .toList();

        // Create a new list with valid exercises
        List<Map<String, dynamic>> validExercises = [];

        for (var exercise in exercises) {
          if (exercise is Map<String, dynamic>) {
            // Make sure exercise has required fields
            if (!exercise.containsKey('id') || !exercise.containsKey('order')) {
              continue;
            }

            // Make sure exercise_details exists
            if (!exercise.containsKey('exercise_details') ||
                exercise['exercise_details'] == null) {
              // Check if exercise has 'exercise' field instead of 'exercise_details'
              if (exercise.containsKey('exercise') &&
                  exercise['exercise'] != null) {
                // Silently use 'exercise' field as 'exercise_details'
                exercise['exercise_details'] = exercise['exercise'];
              } else {
                // Create default exercise details if neither field exists
                exercise['exercise_details'] = {
                  'id': 0,
                  'name': 'Unknown Exercise',
                  'description': 'No details available',
                };
              }
            }

            // Fix type conversion issues for numeric fields
            // Convert string numbers to actual numbers
            if (exercise['weight'] is String) {
              try {
                exercise['weight'] = double.parse(exercise['weight']);
              } catch (e) {
                exercise['weight'] = null;
              }
            }
            if (exercise['sets'] is String) {
              try {
                exercise['sets'] = int.parse(exercise['sets']);
              } catch (e) {
                exercise['sets'] = 0;
              }
            }
            if (exercise['reps'] is String) {
              try {
                exercise['reps'] = int.parse(exercise['reps']);
              } catch (e) {
                exercise['reps'] = 0;
              }
            }
            if (exercise['rest_seconds'] is String) {
              try {
                exercise['rest_seconds'] = int.parse(exercise['rest_seconds']);
              } catch (e) {
                exercise['rest_seconds'] = null;
              }
            }
            if (exercise['duration_seconds'] is String) {
              try {
                exercise['duration_seconds'] =
                    int.parse(exercise['duration_seconds']);
              } catch (e) {
                exercise['duration_seconds'] = null;
              }
            }

            validExercises.add(exercise);
          }
        }

        json['exercises'] = validExercises;
      }

      return _$WorkoutSectionFromJson(json);
    } catch (e) {
      // If anything goes wrong, return a default section with no exercises
      return WorkoutSection(
        id: json['id'] is int ? json['id'] as int : 0,
        name:
            json['name'] is String ? json['name'] as String : 'Unknown Section',
        description: json['description'] as String?,
        order: json['order'] is int ? json['order'] as int : 0,
        exercises: [],
        workoutSessionId: json['workout_session'] is int
            ? json['workout_session'] as int
            : null,
      );
    }
  }

  Map<String, dynamic> toJson() => _$WorkoutSectionToJson(this);
}

// Represents a specific exercise log within a workout section
// Moved from workout_log.dart
@JsonSerializable(explicitToJson: true)
class WorkoutExerciseLog {
  final int id;
  // Use exercise_id from JSON key 'exercise'
  @JsonKey(name: 'exercise')
  final int exerciseId;
  // Use exercise_details from JSON (optional)
  @JsonKey(name: 'exercise_details')
  final ExerciseDetail? exerciseDetails;
  final int? sets;
  final String? reps; // Changed to String? to handle ranges like "8-12"
  final double? weight; // Use double for weight
  @JsonKey(name: 'duration_seconds') // Map from duration_seconds
  final int? duration;
  @JsonKey(name: 'rest_seconds') // Map from rest_seconds
  final int? restTime;
  final int order;
  @JsonKey(name: 'is_completed', defaultValue: false)
  final bool isCompleted;

  WorkoutExerciseLog({
    required this.id,
    required this.exerciseId,
    this.exerciseDetails,
    this.sets,
    this.reps,
    this.weight,
    this.duration,
    this.restTime,
    required this.order,
    required this.isCompleted,
  });

  // Constructor that accepts 'exercise' parameter for compatibility
  WorkoutExerciseLog.withExercise({
    required this.id,
    required ExerciseDetail exercise,
    this.sets,
    this.reps,
    this.weight,
    this.duration,
    this.restTime,
    required this.order,
    required this.isCompleted,
  })  : exerciseId = exercise.id,
        exerciseDetails = exercise;

  // Custom fromJson factory to handle type conversion issues
  factory WorkoutExerciseLog.fromJson(Map<String, dynamic> json) {
    // Fix type conversion issues for numeric fields
    // Convert string numbers to actual numbers
    if (json['weight'] is String) {
      try {
        json['weight'] = double.parse(json['weight']);
      } catch (e) {
        json['weight'] = null;
      }
    }
    if (json['sets'] is String) {
      try {
        json['sets'] = int.parse(json['sets']);
      } catch (e) {
        json['sets'] = 0;
      }
    }
    // Convert reps to string if it's an integer (since reps is String? to handle ranges)
    if (json['reps'] is int) {
      json['reps'] = json['reps'].toString();
    }
    if (json['rest_seconds'] is String) {
      try {
        json['rest_seconds'] = int.parse(json['rest_seconds']);
      } catch (e) {
        json['rest_seconds'] = null;
      }
    }
    if (json['duration_seconds'] is String) {
      try {
        json['duration_seconds'] = int.parse(json['duration_seconds']);
      } catch (e) {
        json['duration_seconds'] = null;
      }
    }

    return _$WorkoutExerciseLogFromJson(json);
  }

  Map<String, dynamic> toJson() => _$WorkoutExerciseLogToJson(this);

  // Getter for durationSeconds to maintain compatibility
  int get durationSeconds => duration ?? 30; // Default to 30 seconds if null

  // Getter for exercise to maintain compatibility
  ExerciseDetail get exercise =>
      exerciseDetails ??
      ExerciseDetail(id: exerciseId, name: 'Unknown Exercise');
}

// Represents the details of an exercise
// Moved from workout_log.dart
@JsonSerializable()
class ExerciseDetail {
  final int id;
  final String name;
  final String? description;
  @JsonKey(name: 'muscle_group')
  final String? muscleGroup;
  @JsonKey(name: 'equipment_required')
  final String? equipmentRequired;
  @JsonKey(name: 'difficulty_level')
  final int? difficultyLevel;
  @JsonKey(name: 'exercise_type')
  final String? exerciseType;
  @JsonKey(name: 'media_file_url')
  final String? mediaFileUrl;
  // Add fallback image/video URLs
  @JsonKey(name: 'video_url') // Assuming this key exists in API response
  final String? videoUrl;
  @JsonKey(name: 'image_url') // Assuming this key exists in API response
  final String? imageUrl;

  ExerciseDetail({
    required this.id,
    required this.name,
    this.description,
    this.muscleGroup,
    this.equipmentRequired,
    this.difficultyLevel,
    this.exerciseType,
    this.mediaFileUrl,
    this.videoUrl, // Add to constructor
    this.imageUrl, // Add to constructor
  });

  factory ExerciseDetail.fromJson(Map<String, dynamic> json) {
    // Transform localhost URLs to S3 URLs for image_url, video_url, and media_file_url
    if (json['image_url'] != null &&
        (json['image_url'].toString().contains('********:8000') ||
            json['image_url'].toString().contains('localhost') ||
            json['image_url'].toString().contains('127.0.0.1'))) {
      String originalUrl = json['image_url'].toString();
      String transformedUrl = ImageUrlHelper.transformImageUrl(originalUrl);
      json['image_url'] = transformedUrl;
    }

    if (json['video_url'] != null &&
        (json['video_url'].toString().contains('********:8000') ||
            json['video_url'].toString().contains('localhost') ||
            json['video_url'].toString().contains('127.0.0.1'))) {
      String originalUrl = json['video_url'].toString();
      String transformedUrl = ImageUrlHelper.transformImageUrl(originalUrl);
      json['video_url'] = transformedUrl;
    }

    if (json['media_file_url'] != null &&
        (json['media_file_url'].toString().contains('********:8000') ||
            json['media_file_url'].toString().contains('localhost') ||
            json['media_file_url'].toString().contains('127.0.0.1'))) {
      String originalUrl = json['media_file_url'].toString();
      String transformedUrl = ImageUrlHelper.transformImageUrl(originalUrl);
      json['media_file_url'] = transformedUrl;
    }

    return _$ExerciseDetailFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ExerciseDetailToJson(this);
}

// Note: Removed old/duplicate definitions of Exercise, ExerciseInstance, WorkoutSection, WorkoutDay, WorkoutLog
// Note: Removed TodayWorkout as TodaysWorkoutResponse exists in workout_log.dart
