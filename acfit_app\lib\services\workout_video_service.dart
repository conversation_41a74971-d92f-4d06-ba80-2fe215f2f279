import 'package:dio/dio.dart';
import '../models/workout_video.dart';
import '../utils/logger.dart';
import '../services/auth_service.dart';

class WorkoutVideoService {
  static final Dio _dio = Dio();
  static const String _baseUrl = 'http://127.0.0.1:8000/api/workouts/videos';

  /// Get videos for the user's active workout plan
  static Future<List<WorkoutVideo>> getVideosForActiveWorkoutPlan() async {
    try {
      final authService = AuthService();
      final token = await authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await _dio.get(
        '$_baseUrl/',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> videosJson = response.data;
        return videosJson.map((json) => WorkoutVideo.fromJson(json)).toList();
      } else {
        Logger.log('Failed to fetch workout videos: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      Logger.log('Error fetching workout videos: $e');
      return [];
    }
  }

  /// Get videos for a specific workout plan
  static Future<List<WorkoutVideo>> getVideosForWorkoutPlan(int planId) async {
    try {
      final authService = AuthService();
      final token = await authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await _dio.get(
        '$_baseUrl/by-plan/$planId/',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> videosJson = response.data;
        return videosJson.map((json) => WorkoutVideo.fromJson(json)).toList();
      } else {
        Logger.log(
            'Failed to fetch workout videos for plan $planId: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      Logger.log('Error fetching workout videos for plan $planId: $e');
      return [];
    }
  }

  /// Get a specific video by ID
  static Future<WorkoutVideo?> getVideoById(int videoId) async {
    try {
      final authService = AuthService();
      final token = await authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await _dio.get(
        '$_baseUrl/$videoId/',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return WorkoutVideo.fromJson(response.data);
      } else {
        Logger.log('Failed to fetch video $videoId: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      Logger.log('Error fetching video $videoId: $e');
      return null;
    }
  }
}
