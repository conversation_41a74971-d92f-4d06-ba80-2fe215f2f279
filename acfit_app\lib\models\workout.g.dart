// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workout.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkoutPlan _$WorkoutPlanFromJson(Map<String, dynamic> json) => WorkoutPlan(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      coverImageUrl: json['cover_image_url'] as String?,
    );

Map<String, dynamic> _$WorkoutPlanToJson(WorkoutPlan instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'cover_image_url': instance.coverImageUrl,
    };

WorkoutDay _$WorkoutDayFromJson(Map<String, dynamic> json) => WorkoutDay(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      isRestDay: json['is_rest_day'] as bool? ?? false,
      totalDuration: (json['total_duration'] as num?)?.toInt(),
      caloriesBurnEstimate: (json['calories_burn_estimate'] as num?)?.toInt(),
      coverImageUrl: json['cover_image_url'] as String?,
      dayNumber: (json['day_number'] as num?)?.toInt(),
      sections: (json['sections'] as List<dynamic>?)
          ?.map((e) => WorkoutSection.fromJson(e as Map<String, dynamic>))
          .toList(),
      sessions: (json['sessions'] as List<dynamic>?)
          ?.map((e) => WorkoutSession.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkoutDayToJson(WorkoutDay instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'is_rest_day': instance.isRestDay,
      'total_duration': instance.totalDuration,
      'calories_burn_estimate': instance.caloriesBurnEstimate,
      'cover_image_url': instance.coverImageUrl,
      'day_number': instance.dayNumber,
      'sections': instance.sections,
      'sessions': instance.sessions,
    };

WorkoutSession _$WorkoutSessionFromJson(Map<String, dynamic> json) =>
    WorkoutSession(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      order: (json['order'] as num).toInt(),
      duration: (json['duration'] as num?)?.toInt(),
      scheduledTime: json['scheduled_time'] as String?,
      caloriesBurnEstimate: (json['calories_burn_estimate'] as num?)?.toInt(),
      sections: (json['sections'] as List<dynamic>?)
          ?.map((e) => WorkoutSection.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkoutSessionToJson(WorkoutSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'order': instance.order,
      'duration': instance.duration,
      'scheduled_time': instance.scheduledTime,
      'calories_burn_estimate': instance.caloriesBurnEstimate,
      'sections': instance.sections?.map((e) => e.toJson()).toList(),
    };

WorkoutSection _$WorkoutSectionFromJson(Map<String, dynamic> json) =>
    WorkoutSection(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      order: (json['order'] as num).toInt(),
      exercises: (json['exercises'] as List<dynamic>)
          .map((e) => WorkoutExerciseLog.fromJson(e as Map<String, dynamic>))
          .toList(),
      workoutSessionId: (json['workout_session'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WorkoutSectionToJson(WorkoutSection instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'order': instance.order,
      'exercises': instance.exercises.map((e) => e.toJson()).toList(),
      'workout_session': instance.workoutSessionId,
    };

WorkoutExerciseLog _$WorkoutExerciseLogFromJson(Map<String, dynamic> json) =>
    WorkoutExerciseLog(
      id: (json['id'] as num).toInt(),
      exerciseId: (json['exercise'] as num).toInt(),
      exerciseDetails: json['exercise_details'] == null
          ? null
          : ExerciseDetail.fromJson(
              json['exercise_details'] as Map<String, dynamic>),
      sets: (json['sets'] as num?)?.toInt(),
      reps: json['reps'] as String?,
      weight: (json['weight'] as num?)?.toDouble(),
      duration: (json['duration_seconds'] as num?)?.toInt(),
      restTime: (json['rest_seconds'] as num?)?.toInt(),
      order: (json['order'] as num).toInt(),
      isCompleted: json['is_completed'] as bool? ?? false,
    );

Map<String, dynamic> _$WorkoutExerciseLogToJson(WorkoutExerciseLog instance) =>
    <String, dynamic>{
      'id': instance.id,
      'exercise': instance.exerciseId,
      'exercise_details': instance.exerciseDetails?.toJson(),
      'sets': instance.sets,
      'reps': instance.reps,
      'weight': instance.weight,
      'duration_seconds': instance.duration,
      'rest_seconds': instance.restTime,
      'order': instance.order,
      'is_completed': instance.isCompleted,
    };

ExerciseDetail _$ExerciseDetailFromJson(Map<String, dynamic> json) =>
    ExerciseDetail(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      muscleGroup: json['muscle_group'] as String?,
      equipmentRequired: json['equipment_required'] as String?,
      difficultyLevel: (json['difficulty_level'] as num?)?.toInt(),
      exerciseType: json['exercise_type'] as String?,
      mediaFileUrl: json['media_file_url'] as String?,
      videoUrl: json['video_url'] as String?,
      imageUrl: json['image_url'] as String?,
    );

Map<String, dynamic> _$ExerciseDetailToJson(ExerciseDetail instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'muscle_group': instance.muscleGroup,
      'equipment_required': instance.equipmentRequired,
      'difficulty_level': instance.difficultyLevel,
      'exercise_type': instance.exerciseType,
      'media_file_url': instance.mediaFileUrl,
      'video_url': instance.videoUrl,
      'image_url': instance.imageUrl,
    };
