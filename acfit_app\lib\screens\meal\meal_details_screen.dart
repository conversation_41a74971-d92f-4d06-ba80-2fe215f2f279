import 'package:flutter/material.dart';
import '../../models/meal.dart'; // Import MealDetail model - KEEPING YOUR PATH
import '../../models/meal_log.dart'
    as meal_log_models; // Import prefix for MealLog - KEEPING YOUR PATH
import '../../services/api_service.dart'; // Keep for future completion logic - KEEPING YOUR PATH
import '../../services/navigation_service.dart'; // KEEPING YOUR PATH
import '../../services/notification_service.dart'; // Import for notifications - KEEPING YOUR PATH
import '../../widgets/common/offline_banner.dart'; // KEEPING YOUR PATH
import '../../widgets/common/animated_loading.dart'; // KEEPING YOUR PATH
import '../../utils/image_utils.dart'; // Import our new image utility class
import 'meal_completion_screen.dart'; // Import the new completion screen - KEEPING YOUR PATH
import '../../utils/logger.dart'; // Import the Logger utility

class MealDetailsScreen extends StatefulWidget {
  // Accept arguments map instead of just MealLog
  final Map<String, dynamic> arguments;

  const MealDetailsScreen({
    Key? key,
    required this.arguments, // Accept arguments map
  }) : super(key: key);

  @override
  State<MealDetailsScreen> createState() => _MealDetailsScreenState();
}

class _MealDetailsScreenState extends State<MealDetailsScreen> {
  final ApiService _apiService = ApiService(); // Keep for future use
  bool _isLoading = false; // For completion logic later
  final bool _isOffline = false; // Assuming online for now
  // String? _error; // Error state not currently used, handled by SnackBar
  late bool _isCompleted;
  late int _currentMealLogId;
  late meal_log_models.MealLog _mealLog; // Store extracted MealLog
  DateTime? _date; // Store extracted date string

  @override
  void initState() {
    super.initState();
    // Extract MealLog and date from arguments safely
    final dynamic mealLogArg = widget.arguments['mealLog'];

    if (mealLogArg is meal_log_models.MealLog) {
      _mealLog = mealLogArg;
      Logger.log("Received MealLog object directly", tag: 'MealDetailsScreen');
    } else if (mealLogArg is Map<String, dynamic>) {
      try {
        _mealLog = meal_log_models.MealLog.fromJson(mealLogArg);
        Logger.log("Successfully deserialized MealLog from Map",
            tag: 'MealDetailsScreen');
      } catch (e) {
        Logger.error("Error deserializing MealLog from Map: $e",
            tag: 'MealDetailsScreen');
        // Handle error: Create a dummy log to avoid crashing, but ideally show an error and pop
        _mealLog = meal_log_models.MealLog(
          id: -1,
          meal: MealDetail(
              id: -1, name: 'Error Loading Meal'), // Need MealDetail model
          isCompleted: false,
        );
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                content: Text('Error loading meal data.'),
                backgroundColor: Colors.red));
            Navigator.of(context).pop();
          }
        });
      }
    } else {
      Logger.error(
          "Invalid type for mealLog argument: ${mealLogArg?.runtimeType}",
          tag: 'MealDetailsScreen');
      // Handle error: Create a dummy log to avoid crashing, but ideally show an error and pop
      _mealLog = meal_log_models.MealLog(
        id: -1,
        meal: MealDetail(
            id: -1, name: 'Error Loading Meal'), // Need MealDetail model
        isCompleted: false,
      );
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
              content: Text('Invalid meal data provided.'),
              backgroundColor: Colors.red));
          Navigator.of(context).pop();
        }
      });
    }

    // Extract date correctly as DateTime?
    final dateArg = widget.arguments['date'];
    if (dateArg is DateTime) {
      _date = dateArg;
    } else if (dateArg is String) {
      // Handle potential older calls passing date as String
      _date = DateTime.tryParse(dateArg);
    } else {
      _date = null; // Default to null if missing or wrong type
    }

    // Initialize completion status and current ID from the (potentially dummy) mealLog
    _isCompleted = _mealLog.id != -1 ? _mealLog.isCompleted : false;
    _currentMealLogId = _mealLog.id;
  }

  // Helper method to check if the meal is for today
  bool _isMealForToday() {
    if (_date == null) {
      // If date is not provided, default to false (don't allow completion)
      Logger.log('Date is null in _isMealForToday', tag: 'MealDetailsScreen');
      return false;
    }
    try {
      // No need to parse, _date is already DateTime
      final mealDate = _date!;
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final mealDay = DateTime(mealDate.year, mealDate.month, mealDate.day);
      return mealDay.isAtSameMomentAs(today);
    } catch (e) {
      Logger.log('Error in _isMealForToday: $e', tag: 'MealDetailsScreen');
      // Default to false to prevent completion if there's an error
      return false;
    }
  }

  // Helper method to check if the meal is for a future day
  bool _isMealForFutureDay() {
    // Check if the meal log is marked as not completable
    if (!_mealLog.isCompletable) {
      Logger.log('Meal is not completable', tag: 'MealDetailsScreen');
      return true;
    }

    if (_date == null) {
      // If date is not provided, default to false (allow completion)
      Logger.log('Date is null in _isMealForFutureDay',
          tag: 'MealDetailsScreen');
      return false;
    }

    try {
      // Get current date
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // Convert meal date
      final mealDate = _date!;
      final mealDay = DateTime(mealDate.year, mealDate.month, mealDate.day);

      // Check if meal day is after today
      final isFuture = mealDay.isAfter(today);

      Logger.log(
          '_isMealForFutureDay: mealDate=$mealDate, today=$today, isFuture=$isFuture',
          tag: 'MealDetailsScreen');

      return isFuture;
    } catch (e) {
      Logger.log('Error in _isMealForFutureDay: $e', tag: 'MealDetailsScreen');
      // Default to false to allow completion if there's an error
      return false;
    }
  }

  // --- Meal Completion Logic ---
  Future<void> _completeMeal() async {
    Logger.log("_completeMeal function entered", tag: 'MealDetailsScreen');
    // If already completed or loading, do nothing
    if (_isCompleted || _isLoading) return;

    // First check if the meal is for a future day - always block future meals
    final isFutureDay = _isMealForFutureDay();
    if (isFutureDay) {
      Logger.log("Blocked completion of future meal", tag: 'MealDetailsScreen');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('You cannot complete future meals'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    // Check if the meal is for today
    final isToday = _isMealForToday();

    // If the meal is for today, allow completion
    // Otherwise, check if it's completable based on day number
    if (!isToday && !_mealLog.isCompletable) {
      Logger.log("Meal is not for today and not completable",
          tag: 'MealDetailsScreen');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('You can only complete meals for the current day'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    final mealDetail = _mealLog.meal;

    final mealName = mealDetail.name;
    int logIdToComplete = _currentMealLogId; // Use the state variable

    // Removed log

    // Show loading state
    setState(() => _isLoading = true);

    // Add a small delay to show the loading animation
    await Future.delayed(const Duration(milliseconds: 500));

    try {
      // Step 1: Create the log if it doesn't exist yet (ID is 0)
      if (logIdToComplete == 0) {
        // Removed log
        // Use the date passed via arguments if available, otherwise default to now
        final String dateStr = _date?.toIso8601String().split('T')[0] ??
            DateTime.now().toIso8601String().split('T')[0];
        final createData = {
          'meal': mealDetail.id,
          'date': dateStr,
          // Add any other required fields for creation if necessary
          'calories': mealDetail.calories ?? 0,
        };
        // Removed log
        final createResponse =
            await _apiService.createOrUpdateMealLog(createData);

        if (createResponse['id'] != null && createResponse['id'] is int) {
          logIdToComplete = createResponse['id'];
          _currentMealLogId = logIdToComplete; // Update state variable
          // Removed log
        } else {
          // Removed log
          throw Exception('Failed to create meal log entry before completion.');
        }
      }

      // Step 2: Complete the log using the (potentially new) ID
      // Removed log
      await _apiService.completeMealLog(mealLogId: logIdToComplete);

      // Success flow
      if (mounted) {
        // Play a small animation by keeping loading state for a moment
        await Future.delayed(const Duration(milliseconds: 300));

        // Update state to completed
        setState(() {
          _isLoading = false;
          _isCompleted = true; // Update local state
        });

        // Force refresh calorie data
        try {
          await _apiService.getCalories(skipCache: true);
          Logger.log('Calories refreshed after meal completion',
              tag: 'MealDetailsScreen');
        } catch (e) {
          Logger.error('Failed to refresh calories after meal completion: $e',
              tag: 'MealDetailsScreen');
        }

        // Notify HomeScreen that a meal was completed
        // Add logging right before the notification
        Logger.log(
            'About to send meal_completed notification from MealDetailsScreen',
            tag: 'MealDetailsScreen');
        NotificationService.instance.notifyListeners('meal_completed', {
          'meal_id': mealDetail.id,
          'meal_log_id': logIdToComplete,
          'meal_name': mealName,
          'calories': mealDetail.calories ?? 0,
        });

        // Create an updated MealLog with the completed status
        final updatedMealLog = meal_log_models.MealLog(
          id: logIdToComplete,
          // date: widget.mealLog.date, // Removed date
          meal: _mealLog.meal,
          mealType: _mealLog.mealType, // Use mealType
          isCompleted: true,
          isCompletable: _mealLog.isCompletable,
          completionTime: DateTime.now(),
          // Include other fields from the original _mealLog if needed
          actualCalories: _mealLog.actualCalories,
          actualProtein: _mealLog.actualProtein,
          actualCarbs: _mealLog.actualCarbs,
          actualFat: _mealLog.actualFat,
          originalMealLogId:
              _mealLog.originalMealLogId, // Preserve original ID if present
          scheduledTime: _mealLog.scheduledTime,
        );

        // Navigate to the new completion screen with the updated MealLog
        // Use pushReplacement to replace this screen with the completion screen
        // This ensures the user sees the new enhanced completion screen and prevents flickering
        if (mounted) {
          // Use Navigator.pushReplacement directly to avoid potential issues with NavigationService
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) =>
                  MealCompletionScreen(mealLog: updatedMealLog),
            ),
          );
        }

        // No need to pop since we're using replacement navigation
        // If you want to *always* pop after navigating *away* to completion,
        // you might call pop *before* navigating or immediately after,
        // but the current logic pops on *return* with a specific result.
      }
    } catch (e) {
      // Error flow
      // Removed log
      if (mounted) {
        setState(() => _isLoading = false); // Reset loading state

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing meal: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
  // --- End Meal Completion Logic ---

  @override
  Widget build(BuildContext context) {
    // Access MealDetail from the MealLog stored in state
    final MealDetail meal = _mealLog.meal;

    // Only show the regular meal details view - completion screen is handled separately
    return Scaffold(
      backgroundColor: Colors.white,
      // Always extend body behind AppBar for the details view
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        // AppBar for the regular meal details state
        backgroundColor: Colors.transparent, // Make AppBar transparent
        elevation: 0, // No shadow
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(102), // ~0.4 opacity
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => NavigationService.goBack(),
          ),
        ),
        actions: const [],
      ),
      body: _buildRegularMealDetailsContent(meal, context),
    );
  }

  // Builds the CONTENT for the regular meal details view (No Scaffold wrapper)
  Widget _buildRegularMealDetailsContent(
      MealDetail meal, BuildContext context) {
    final String? imageUrl =
        meal.mealImageUrl ?? meal.imageUrl ?? meal.mealImage;

    // This Column is the main content structure for the non-completed state
    return Column(
      children: [
        // Header Image with fixed aspect ratio
        AspectRatio(
          aspectRatio: 4 / 3, // 4:3 aspect ratio for consistent image display
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Image - Using our improved ImageUtils class
              ImageUtils.getNetworkImageWithFallback(
                imageUrl: imageUrl,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                fallbackIcon: Icons.restaurant_menu,
                fallbackIconColor: Colors.grey.shade600,
                placeholder: Container(
                  color: Colors.grey[300],
                  child: const Center(child: AnimatedLoading()),
                ),
              ),
              // Gradient overlay for better text readability
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withAlpha(77), // ~0.3 opacity
                    ],
                    stops: const [0.6, 1.0], // Gradient starts lower down
                  ),
                ),
              ),
              // Meal type and info overlay at bottom of image
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Row(
                  children: [
                    _buildHeaderChip(_getMealTypeText(meal.mealType)),
                    const Spacer(),
                    _buildHeaderInfo(
                      Icons.local_fire_department_outlined,
                      '${meal.calories ?? 0} kcal',
                    ),
                    const SizedBox(width: 16),
                    _buildHeaderInfo(
                      Icons.access_time,
                      '${meal.preparationTime ?? '?'} min',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Content Area (Scrollable part)
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Meal Title
                Text(
                  meal.name,
                  style: const TextStyle(
                    color: Color(0xFF101114),
                    fontSize: 24,
                    fontFamily: 'Work Sans',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),

                // Meal Content Details (Description, Nutrition)
                _buildContentDetails(meal),

                // If you decided to show a completed chip *here* instead of the separate screen:
                // if (_isCompleted) ... add completed chip/indicator ...
              ],
            ),
          ),
        ),

        // --- Bottom Action Area ---
        // Offline banner if needed
        if (_isOffline) const OfflineBanner(),

        // Complete Meal Button (Only show if not completed and completable)
        // This button is part of the main Column now
        if (!_isCompleted && _mealLog.isCompletable)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            // Adding padding also at the bottom for spacing from screen edge
            margin: const EdgeInsets.only(bottom: 16),
            decoration: const BoxDecoration(
              color: Colors.white, // Keep button area background white
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 10,
                  offset: Offset(0, -2), // Shadow pointing upwards
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _completeMeal,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFF97316),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0, // Handled by container shadow
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 3,
                      ),
                    )
                  : const Text(
                      'Complete Meal',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Work Sans',
                      ),
                    ),
            ),
          ),
      ],
    );
  }

  // Helper for header chips (like meal type)
  Widget _buildHeaderChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(26), // ~0.1 opacity
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withAlpha(51), // ~0.2 opacity
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontFamily: 'Work Sans',
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // Helper for header info (calories, time)
  Widget _buildHeaderInfo(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(51), // ~0.2 opacity
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 13,
              fontFamily: 'Work Sans',
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Builds the content inside the scrollable area (Description, Nutrition)
  Widget _buildContentDetails(MealDetail meal) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Description Section
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(5), // ~0.02 opacity
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Description',
                style: TextStyle(
                  color: Color(0xFF101114),
                  fontSize: 18,
                  fontFamily: 'Work Sans',
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                meal.description ?? 'No description available for this meal.',
                style: const TextStyle(
                  color: Color(0xFF676B74),
                  fontSize: 16,
                  fontFamily: 'Work Sans',
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),

        // Nutrition Section (Keeping original style, fixing opacity)
        _buildNutritionSection(meal),

        // ** NOTE: Removed the _isCompleted check/indicator from here **
        // because the main build method switches to _buildCompletedIndicator
        // when _isCompleted is true. If you wanted an indicator *within*
        // this scroll view *instead* of the separate screen, you'd add it here.

        const SizedBox(height: 20), // Bottom padding inside scroll view
      ],
    );
  }

  // Removed _buildCompletedIndicator method as it's no longer needed
  // We now use the dedicated MealCompletionScreen instead

  // Nutrition section with original design (fixing opacity)
  Widget _buildNutritionSection(MealDetail meal) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5), // ~0.02 opacity
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          const Text(
            'Nutrition',
            style: TextStyle(
              color: Color(0xFF101114),
              fontSize: 18,
              fontFamily: 'Work Sans',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Calories row with icon
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: const Color(0xFFF97316).withAlpha(26), // ~0.1 opacity
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Icon(
                    Icons.local_fire_department_outlined,
                    color: Color(0xFFF97316),
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Calories',
                    style: TextStyle(
                      color: Color(0xFF676B74),
                      fontSize: 14,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${meal.calories ?? 0} kcal',
                    style: const TextStyle(
                      color: Color(0xFF101114),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                ],
              ),
            ],
          ),

          const Divider(height: 32, thickness: 1, color: Color(0xFFEEEEEE)),

          // Macronutrients grid (original style)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNutrientInfo('Protein', '${meal.protein ?? 0}g',
                  const Color(0xFF3B82F6)), // Blue
              _buildNutrientInfo('Carbs', '${meal.carbs ?? 0}g',
                  const Color(0xFF10B981)), // Green
              _buildNutrientInfo(
                  'Fat', '${meal.fat ?? 0}g', const Color(0xFFEF4444)), // Red
            ],
          ),
        ],
      ),
    );
  }

  // Simplified nutrient info builder (original style, fixing opacity)
  Widget _buildNutrientInfo(String label, String value, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color.withAlpha(26), // ~0.1 opacity
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              value,
              style: TextStyle(
                color: color, // Use the original color for text
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'Work Sans',
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            color: Color(0xFF676B74),
            fontSize: 14,
            fontWeight: FontWeight.w500,
            fontFamily: 'Work Sans',
          ),
        ),
      ],
    );
  }

  // Helper to map meal type code to display text (keeping original)
  String _getMealTypeText(String? mealType) {
    switch (mealType) {
      case 'B':
        return 'BREAKFAST';
      case 'L':
        return 'LUNCH';
      case 'D':
        return 'DINNER';
      case 'S':
        return 'SNACK';
      default:
        return 'MEAL';
    }
  }
}
