#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
django.setup()

from workouts.models import WorkoutVideo, WorkoutPlan
from workouts.serializers import WorkoutVideoSerializer

def test_video_api():
    print("=== Testing Video API ===\n")
    
    # Test 1: Check videos in database
    videos = WorkoutVideo.objects.all()
    print(f"1. Videos in database: {videos.count()}")
    for video in videos:
        print(f"   - {video.title} (Plan: {video.workout_plan.name})")
    
    # Test 2: Test serializer
    if videos.exists():
        video = videos.first()
        serializer = WorkoutVideoSerializer(video)
        print(f"\n2. Serialized video data:")
        print(f"   Title: {serializer.data['title']}")
        print(f"   Duration: {serializer.data['duration_seconds']} seconds")
        print(f"   Order: {serializer.data['order']}")
        print(f"   Video URL: {serializer.data['video_url']}")
        print(f"   Thumbnail URL: {serializer.data['thumbnail_url']}")
    
    # Test 3: Test by-plan filtering
    workout_plan = WorkoutPlan.objects.first()
    if workout_plan:
        plan_videos = WorkoutVideo.objects.filter(workout_plan=workout_plan)
        print(f"\n3. Videos for plan '{workout_plan.name}': {plan_videos.count()}")
        for video in plan_videos:
            print(f"   - {video.title} (Order: {video.order})")
    
    # Test 4: Test ordering
    ordered_videos = WorkoutVideo.objects.all().order_by('workout_plan', 'order')
    print(f"\n4. Videos ordered by plan and order:")
    for video in ordered_videos:
        print(f"   - {video.title} (Plan: {video.workout_plan.name}, Order: {video.order})")
    
    print("\n=== API Test Complete ===")

if __name__ == "__main__":
    test_video_api()
