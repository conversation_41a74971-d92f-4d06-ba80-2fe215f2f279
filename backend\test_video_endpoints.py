#!/usr/bin/env python
import os
import django
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
django.setup()

from accounts.models import User, UserProfile
from workouts.models import UserWorkoutPlan, WorkoutPlan, WorkoutVideo

def test_video_endpoints():
    print("=== Testing Video API Endpoints ===\n")
    
    base_url = "http://127.0.0.1:8000"
    
    # Test 1: Check if videos endpoint exists (unauthenticated)
    print("1. Testing unauthenticated access...")
    response = requests.get(f"{base_url}/api/workouts/videos/")
    print(f"   Status: {response.status_code}")
    print(f"   Response: {response.text[:100]}...")
    
    # Test 2: Check specific video endpoint
    print("\n2. Testing specific video endpoint...")
    response = requests.get(f"{base_url}/api/workouts/videos/1/")
    print(f"   Status: {response.status_code}")
    print(f"   Response: {response.text[:100]}...")
    
    # Test 3: Check by-plan endpoint
    print("\n3. Testing by-plan endpoint...")
    # Get the first workout plan ID
    workout_plan = WorkoutPlan.objects.first()
    if workout_plan:
        response = requests.get(f"{base_url}/api/workouts/videos/by-plan/{workout_plan.id}/")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
    else:
        print("   No workout plans found")
    
    # Test 4: Check admin endpoint
    print("\n4. Testing admin endpoint...")
    response = requests.get(f"{base_url}/acfit-admin/workouts/workoutvideo/")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print("   Admin page accessible!")
    elif response.status_code == 302:
        print("   Redirected (probably to login)")
    else:
        print(f"   Error: {response.text[:100]}...")
    
    # Test 5: Check if videos exist in database
    print("\n5. Database check...")
    videos = WorkoutVideo.objects.all()
    print(f"   Videos in database: {videos.count()}")
    for video in videos:
        print(f"   - {video.title} (Plan: {video.workout_plan.name})")
    
    # Test 6: Check URL patterns
    print("\n6. URL patterns check...")
    from django.urls import reverse
    try:
        admin_url = reverse('admin:workouts_workoutvideo_changelist')
        print(f"   Standard admin URL: {admin_url}")
    except:
        print("   Standard admin URL not found")
    
    # Check if custom admin URLs work
    from admin_site.admin import acfit_admin_site
    try:
        # This might not work directly, but let's try
        print(f"   Custom admin site registered models: {len(acfit_admin_site._registry)}")
        print(f"   WorkoutVideo in registry: {'workoutvideo' in [m._meta.model_name for m in acfit_admin_site._registry.keys()]}")
    except Exception as e:
        print(f"   Error checking custom admin: {e}")

if __name__ == "__main__":
    test_video_endpoints()
