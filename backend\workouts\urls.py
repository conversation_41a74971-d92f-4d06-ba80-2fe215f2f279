from django.urls import path, include
from rest_framework.routers import DefaultRouter

# FIX: Correct imports based on actual view file locations
from workouts.views.workout import (
    WorkoutDayViewSet, WorkoutPlanViewSet, WorkoutLogViewSet,
    WorkoutSessionViewSet, WorkoutSectionViewSet, UserWorkoutPlanViewSet,
    WorkoutSessionLogViewSet
)
from workouts.views.schedule import (
    WorkoutScheduleViewSet, WorkoutReminderViewSet # Removed UserEquipmentViewSet
)
# FIX: Import ExerciseViewSet from exercise.py and ExerciseLogViewSet from progress.py
from workouts.views.exercise import ExerciseViewSet
from workouts.views.progress import ExerciseLogViewSet
# Import EquipmentViewSet if it exists (check workouts/views/equipment.py or similar)
# from workouts.views.equipment import EquipmentViewSet

# Import function views if they are still separate
from workouts.views import get_todays_workout, get_upcoming_workouts
from workouts.views.date import get_workout_for_date
from workouts.views.program_day import get_workout_by_program_day
from workouts.views.direct_complete import direct_complete_workout_session
from workouts.views.test_direct_complete import test_direct_complete
from workouts.views.emergency_complete import emergency_complete_workout


router = DefaultRouter()
router.register(r'exercises', ExerciseViewSet, basename='exercise')
router.register(r'workout-days', WorkoutDayViewSet, basename='workout-day')
router.register(r'plans', WorkoutPlanViewSet, basename='workout-plan')
router.register(r'sessions', WorkoutSessionViewSet, basename='workout-session')
router.register(r'sections', WorkoutSectionViewSet, basename='workout-section')
router.register(r'logs', WorkoutLogViewSet, basename='workout-log')
router.register(r'session-logs', WorkoutSessionLogViewSet, basename='workout-session-log')
# Make sure the direct-complete action is registered with the router
router.register(r'schedules', WorkoutScheduleViewSet, basename='workout-schedule')
router.register(r'reminders', WorkoutReminderViewSet, basename='workout-reminder')
# Removed router registration for user-equipment
router.register(r'exercise-logs', ExerciseLogViewSet, basename='exercise-log')
router.register(r'user-plans', UserWorkoutPlanViewSet, basename='user-workout-plan')
# Register EquipmentViewSet if imported and defined
# router.register(r'equipment', EquipmentViewSet, basename='equipment')


urlpatterns = [
    path('', include(router.urls)),
    path('today/', get_todays_workout, name='todays-workout'),
    path('upcoming/', get_upcoming_workouts, name='upcoming-workouts'),
    path('date/', get_workout_for_date, name='workout-for-date'),
    path('program-day/', get_workout_by_program_day, name='workout-by-program-day'),
    # Add direct-complete endpoint as a standalone function
    path('session-logs/direct-complete/', direct_complete_workout_session, name='direct-complete-workout-session'),
    # Also add version without trailing slash
    path('session-logs/direct-complete', direct_complete_workout_session, name='direct-complete-workout-session-no-slash'),
    # Add test endpoint for direct completion
    path('session-logs/test-direct-complete/', test_direct_complete, name='test-direct-complete'),
    path('session-logs/test-direct-complete', test_direct_complete, name='test-direct-complete-no-slash'),
    # Add emergency completion endpoint
    path('session-logs/emergency-complete/', emergency_complete_workout, name='emergency-complete-workout'),
    path('session-logs/emergency-complete', emergency_complete_workout, name='emergency-complete-workout-no-slash'),
]
