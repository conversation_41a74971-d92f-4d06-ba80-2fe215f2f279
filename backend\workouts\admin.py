from django.contrib import admin
from django.urls import reverse, path
from django.utils.safestring import mark_safe
from django import forms
from django.contrib import messages
from django.db.models import Max, Q
from django.db import transaction
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse
from django.template.response import TemplateResponse
from django.utils import timezone
from datetime import timedelta, date
from .models import (
    WorkoutPlan, WorkoutDay, WorkoutSession, WorkoutSection, Exercise,
    ExerciseInstance, WorkoutLog, ExerciseLog, WorkoutSchedule,
    WorkoutReminder, Equipment, UserWorkoutPlan, WorkoutSessionLog, WorkoutVideo
)
import logging # Import logging
import calendar
import json

logger = logging.getLogger(__name__) # Define logger

@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name',)

@admin.register(Exercise)
class ExerciseAdmin(admin.ModelAdmin):
    list_display = ('name', 'muscle_group', 'fitness_level', 'equipment_required', 'image_preview', 'gif_preview')
    list_filter = ('muscle_group', 'fitness_level', 'equipment_required')
    search_fields = ['name', 'description', 'muscle_group']
    ordering = ['name']
    readonly_fields = ('image_preview', 'gif_preview') # Make previews read-only

    def image_preview(self, obj):
        if obj.cover_image:
            return mark_safe(f'<img src="{obj.cover_image.url}" width="100" />')
        return "No Image"
    image_preview.short_description = 'Image'

    def gif_preview(self, obj):
        if obj.media_file and obj.media_file.name.lower().endswith('.gif'):
            return mark_safe(f'<img src="{obj.media_file.url}" width="100" />')
        return "No GIF"
    gif_preview.short_description = 'GIF'

# Inline for WorkoutSchedule (Template Schedules only)
class WorkoutScheduleInline(admin.TabularInline):
    model = WorkoutSchedule
    extra = 1 # Number of empty forms to display
    fields = ('week_number', 'days', 'days_display')
    readonly_fields = ('days_display',)
    verbose_name = "Template Schedule Week"
    verbose_name_plural = "Template Schedules"
    fk_name = 'workout_plan' # Explicitly state FK for template plan

    def days_display(self, obj):
        """Display the days mapping in a more readable format"""
        if not obj.days:
            return "No days assigned"

        from django.utils.safestring import mark_safe
        from workouts.models import WorkoutDay

        html = ["<table style='width:100%; border-collapse:collapse;'>",
                "<tr style='background-color:#f5f5f5;'>",
                "<th style='padding:8px; border:1px solid #ddd;'>Program Day</th>",
                "<th style='padding:8px; border:1px solid #ddd;'>Workout</th>",
                "</tr>"]

        # Sort the days by day number
        sorted_days = sorted(obj.days.items(), key=lambda x: int(x[0]))

        for day_num, day_id in sorted_days:
            if day_id is not None:
                try:
                    workout_day = WorkoutDay.objects.get(id=day_id)
                    day_name = workout_day.name
                    day_url = f"/admin/workouts/workoutday/{day_id}/change/"
                    row_html = f"<tr><td style='padding:8px; border:1px solid #ddd;'>Day {day_num}</td><td style='padding:8px; border:1px solid #ddd;'><a href='{day_url}'>{day_name}</a></td></tr>"
                except WorkoutDay.DoesNotExist:
                    row_html = f"<tr><td style='padding:8px; border:1px solid #ddd;'>Day {day_num}</td><td style='padding:8px; border:1px solid #ddd;'>Workout not found (ID: {day_id})</td></tr>"
            else:
                row_html = f"<tr><td style='padding:8px; border:1px solid #ddd;'>Day {day_num}</td><td style='padding:8px; border:1px solid #ddd;'>Rest Day</td></tr>"
            html.append(row_html)

        html.append("</table>")
        return mark_safe(''.join(html))
    days_display.short_description = "Program Days"

    def get_queryset(self, request):
        # Ensure we only deal with template schedules in this inline context
        qs = super().get_queryset(request)
        return qs.filter(user_workout_plan__isnull=True)

    # Limit choices for day fields to only TEMPLATE days for this plan
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']:
            obj_id = request.resolver_match.kwargs.get('object_id')
            if obj_id:
                try:
                    plan = WorkoutPlan.objects.get(pk=obj_id)
                    kwargs["queryset"] = WorkoutDay.objects.filter(workout_plan=plan, user_workout_plan__isnull=True)
                except WorkoutPlan.DoesNotExist:
                    kwargs["queryset"] = WorkoutDay.objects.none()
            else:
                 kwargs["queryset"] = WorkoutDay.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

# Separate Inline for UserWorkoutPlan Schedules
class UserWorkoutScheduleInline(admin.TabularInline):
    model = WorkoutSchedule
    extra = 1
    fk_name = 'user_workout_plan'
    verbose_name = "User Schedule Week"
    verbose_name_plural = "User Schedules"
    template = 'admin/workout_schedule_tabular.html'

    class UserWorkoutScheduleForm(forms.ModelForm):
        # Create fields for day numbers instead of weekdays
        day_1 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 1")
        day_2 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 2")
        day_3 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 3")
        day_4 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 4")
        day_5 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 5")
        day_6 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 6")
        day_7 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 7")

        class Meta:
            model = WorkoutSchedule
            fields = ['week_number']

        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            instance = kwargs.get('instance')

            if instance:
                # Calculate the base day number for this week
                base_day = (instance.week_number - 1) * 7 + 1

                # Map weekday fields to day numbers
                weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                for i, field_name in enumerate(weekday_fields):
                    day_number = base_day + i
                    day_field = f'day_{i+1}'
                    self.fields[day_field].initial = getattr(instance, field_name)

                    # Update the label to show the absolute day number
                    self.fields[day_field].label = f"Day {day_number}"

                    # Get the user plan from the instance
                    user_plan = instance.user_workout_plan
                    if user_plan:
                        # Filter workout days by user plan
                        self.fields[day_field].queryset = WorkoutDay.objects.filter(user_workout_plan=user_plan)

        def save(self, commit=True):
            instance = super().save(commit=False)

            # Map day fields to weekday fields
            weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            for i in range(7):
                day_field = f'day_{i+1}'
                weekday_field = weekday_fields[i]
                setattr(instance, weekday_field, self.cleaned_data.get(day_field))

            # Update the days JSON field if it exists
            if hasattr(instance, 'days'):
                days_mapping = {}
                base_day = (instance.week_number - 1) * 7 + 1

                for i in range(7):
                    day_field = f'day_{i+1}'
                    day_number = base_day + i
                    workout_day = self.cleaned_data.get(day_field)
                    if workout_day:
                        days_mapping[str(day_number)] = workout_day.id

                instance.days = days_mapping

            if commit:
                instance.save()
            return instance

    form = UserWorkoutScheduleForm

    def get_fields(self, request, obj=None):
        return ['week_number', 'day_1', 'day_2', 'day_3', 'day_4', 'day_5', 'day_6', 'day_7']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        formset.form = self.form
        return formset


@admin.register(WorkoutPlan)
class WorkoutPlanAdmin(admin.ModelAdmin):
    class CustomWorkoutPlanForm(forms.ModelForm):
        SYNC_CHOICES = [
            ('none', 'Don\'t sync with user plans'),
            ('all', 'Sync everything (overwrite all user modifications)'),
            ('unmodified', 'Only sync unmodified user plans'),
            ('future', 'Only apply changes to future days in user plans')
        ]

        sync_option = forms.ChoiceField(
            choices=SYNC_CHOICES,
            required=False,
            initial='none',
            widget=forms.RadioSelect,
            help_text="Choose how to sync changes with user workout plans"
        )

        class Meta:
            model = WorkoutPlan
            fields = '__all__'

    form = CustomWorkoutPlanForm
    list_display = ('name', 'fitness_level', 'goal', 'location', 'workouts_per_week', 'duration_weeks', 'view_workout_days_link', 'view_schedules_link')
    list_filter = ('fitness_level', 'goal', 'location', 'requires_equipment', 'gender', 'age_group')
    search_fields = ('name', 'description')
    readonly_fields = ('view_workout_days_link', 'view_schedules_link', 'created_at', 'updated_at')
    fieldsets = (
        ('Basic Info', {'fields': ('name', 'description')}),
        ('Plan Structure', {'fields': ('duration_weeks', 'workouts_per_week', 'workout_days')}),
        ('Targeting', {'fields': ('fitness_level', 'goal', 'location', 'requires_equipment', 'gender', 'age_group', 'health_conditions_allowed')}),
        ('Media', {'fields': ('cover_image',)}),
        ('Change Propagation', {'fields': ('sync_option',), 'classes': ('wide',)}),
        ('Metadata', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )
    inlines = [WorkoutScheduleInline]

    # Temporary attributes to pass info to response_change
    _should_sync_users = False
    _overwrite_custom_changes = False

    def view_workout_days_link(self, obj):
        count = WorkoutDay.objects.filter(workout_plan=obj).count()
        url = (
            reverse("admin:workouts_workoutday_changelist")
            + "?"
            + f"workout_plan__id__exact={obj.id}"
        )
        return mark_safe(f'<a href="{url}">View {count} Workout Days</a>')
    view_workout_days_link.short_description = "Workout Days"

    def view_schedules_link(self, obj):
        count = WorkoutSchedule.objects.filter(workout_plan=obj, user_workout_plan__isnull=True).count()
        url = (
            reverse("admin:workouts_workoutschedule_changelist")
            + "?"
            + f"workout_plan__id__exact={obj.id}"
            + "&"
            + "user_workout_plan__isnull=True"
        )
        return mark_safe(f'<a href="{url}">View {count} Template Schedules</a>')
    view_schedules_link.short_description = "Template Schedules"

    def save_model(self, request, obj, form, change):
        # Save the workout plan first
        super().save_model(request, obj, form, change)

        # Handle sync options
        if change and 'sync_option' in form.cleaned_data:
            sync_option = form.cleaned_data['sync_option']

            if sync_option != 'none':
                # Get all user plans based on this global plan
                user_plans = UserWorkoutPlan.objects.filter(workout_plan=obj)
                success_count = 0
                error_count = 0

                if sync_option == 'all':
                    # Sync all user plans, overwriting modifications
                    for user_plan in user_plans:
                        try:
                            # Each sync operation is in its own transaction
                            with transaction.atomic():
                                user_plan.sync_with_global(overwrite_all=True)
                                success_count += 1
                        except Exception as e:
                            error_count += 1
                            logger.error(f"Error syncing plan for user {user_plan.user.user.username}: {e}", exc_info=True)
                            messages.error(request, f"Error syncing plan for user {user_plan.user.user.username}'s profile: {str(e)}")

                    if success_count > 0:
                        messages.success(request, f"Successfully synced {success_count} user workout plans, overwriting modifications.")

                elif sync_option == 'unmodified':
                    # Only sync unmodified user plans
                    for user_plan in user_plans:
                        if not user_plan.is_modified:
                            try:
                                with transaction.atomic():
                                    user_plan.sync_with_global(overwrite_all=True)
                                    success_count += 1
                            except Exception as e:
                                error_count += 1
                                logger.error(f"Error syncing plan for user {user_plan.user.user.username}: {e}", exc_info=True)
                                messages.error(request, f"Error syncing plan for user {user_plan.user.user.username}'s profile: {str(e)}")

                    if success_count > 0:
                        messages.success(request, f"Successfully synced {success_count} unmodified user workout plans.")

                elif sync_option == 'future':
                    # Only sync future days in all user plans
                    for user_plan in user_plans:
                        try:
                            with transaction.atomic():
                                user_plan.sync_future_days()
                                success_count += 1
                        except Exception as e:
                            error_count += 1
                            logger.error(f"Error syncing future days for user {user_plan.user.user.username}: {e}", exc_info=True)
                            messages.error(request, f"Error syncing future days for user {user_plan.user.user.username}'s profile: {str(e)}")

                    if success_count > 0:
                        messages.success(request, f"Successfully applied changes to future days in {success_count} user workout plans.")

    def save_formset(self, request, form, formset, change):
        """Handle saving inline TEMPLATE schedules, attempting early NULL update."""
        # Check if we're using 'don't sync with user plans' option
        sync_option = form.cleaned_data.get('sync_option', 'none')

        # Get current schedules before any changes
        parent_plan = form.instance
        current_schedules = list(WorkoutSchedule.objects.filter(
            workout_plan=parent_plan,
            user_workout_plan__isnull=True
        ).values_list('id', flat=True))

        # Log the current schedules
        logger.info(f"Current template schedules before formset save: {current_schedules}")

        # Check for schedules that would be deleted
        schedules_to_delete = []
        for form_instance in formset.forms:
            if formset._should_delete_form(form_instance) and form_instance.instance.pk:
                schedules_to_delete.append(form_instance.instance.pk)

        # If we're not syncing with user plans and there are schedules to delete, prevent deletion
        if sync_option == 'none' and schedules_to_delete:
            logger.warning(f"Preventing deletion of schedules {schedules_to_delete} when using 'don't sync with user plans' option")
            # Remove the DELETE flag from these forms
            for form_instance in formset.forms:
                if form_instance.instance.pk in schedules_to_delete:
                    form_instance.cleaned_data['DELETE'] = False
                    # Also update the form's data to remove the DELETE flag
                    if hasattr(form_instance, 'data') and isinstance(form_instance.data, dict):
                        form_instance.data = form_instance.data.copy()
                        delete_field = f'{formset.prefix}-{form_instance.prefix}-DELETE'
                        if delete_field in form_instance.data:
                            form_instance.data[delete_field] = False

            # Show a message to the user
            messages.warning(request, f"Schedule deletion was prevented because 'Don't sync with user plans' option was selected. To delete schedules, choose a sync option.")

        # Continue with normal save process
        instances = formset.save(commit=False)

        for form_instance in formset.forms:
            if form_instance.has_changed() and not formset._should_delete_form(form_instance):
                instance = form_instance.instance
                if isinstance(instance, WorkoutSchedule) and instance.user_workout_plan is None:
                    save_instance = False
                    for day_field in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']:
                        if day_field in form_instance.changed_data and not form_instance.cleaned_data.get(day_field):
                            setattr(instance, day_field, None)
                            save_instance = True
                    if save_instance and instance.pk:
                        instance.save()

        for instance in instances:
            if instance.user_workout_plan is None:
                instance.workout_plan = parent_plan

        formset.save()
        formset.save_m2m()

        # Check if any schedules were deleted
        remaining_schedules = list(WorkoutSchedule.objects.filter(
            workout_plan=parent_plan,
            user_workout_plan__isnull=True
        ).values_list('id', flat=True))

        # Log the remaining schedules
        logger.info(f"Remaining template schedules after formset save: {remaining_schedules}")

    def response_change(self, request, obj):
        """Trigger sync after model and formsets have been saved."""
        response = super().response_change(request, obj)
        if getattr(self, '_should_sync_users', False):
            overwrite = getattr(self, '_overwrite_custom_changes', False)
            user_plans = UserWorkoutPlan.objects.filter(workout_plan=obj)
            modified_count = user_plans.filter(is_modified=True).count()
            sync_count = 0
            error_count = 0

            for user_plan in user_plans:
                if overwrite or not user_plan.is_modified:
                    try:
                        # Each sync operation is in its own transaction
                        with transaction.atomic():
                            user_plan.sync_with_global(overwrite_all=overwrite)
                            sync_count += 1
                    except Exception as e:
                        error_count += 1
                        logger.error(f"Error syncing UserWorkoutPlan {user_plan.id} during response_change for WorkoutPlan {obj.id}: {e}", exc_info=True)
                        messages.error(request, f"Error syncing plan for user {user_plan.user.user.username}'s profile: {str(e)}")

            if sync_count > 0:
                messages.success(request, f'Successfully synced {sync_count} user workout plans.')
            if modified_count > 0 and not overwrite:
                messages.warning(request, f'{modified_count} user(s) with custom plans were not updated. Check the "Overwrite custom changes" option to update all plans.')
            if error_count > 0:
                messages.warning(request, f'Failed to sync {error_count} user workout plans. See error messages for details.')

            self._should_sync_users = False
            self._overwrite_custom_changes = False

        return response


class WorkoutSessionInline(admin.TabularInline):
    model = WorkoutSession
    extra = 1
    fields = ('name', 'order', 'duration', 'calories_burn_estimate')

@admin.register(WorkoutDay)
class WorkoutDayAdmin(admin.ModelAdmin):
    inlines = [WorkoutSessionInline]
    list_display = ('name', 'workout_plan_link', 'day_number', 'fitness_level', 'view_sessions_link')
    list_filter = ('workout_plan', 'fitness_level')
    search_fields = ('name', 'description', 'workout_plan__name')
    readonly_fields = ('workout_plan_link', 'view_sessions_link')
    exclude = ('day_of_week',)  # Hide the day_of_week field

    def workout_plan_link(self, obj):
        if obj.workout_plan:
            url = reverse("admin:workouts_workoutplan_change", args=[obj.workout_plan.id])
            return mark_safe(f'<a href="{url}">{obj.workout_plan.name}</a>')
        return "N/A"
    workout_plan_link.short_description = 'Workout Plan'

    def view_sessions_link(self, obj):
        count = WorkoutSession.objects.filter(workout_day=obj).count()
        url = (
            reverse("admin:workouts_workoutsession_changelist")
            + "?"
            + f"workout_day__id__exact={obj.id}"
        )
        return mark_safe(f'<a href="{url}">View {count} Sessions</a>')
    view_sessions_link.short_description = "Sessions"

    def get_changeform_initial_data(self, request):
        """Pre-fill the form with user_workout_plan and day_number from URL parameters."""
        initial = super().get_changeform_initial_data(request)

        # Check if user_workout_plan is in the URL parameters
        if 'user_workout_plan' in request.GET:
            try:
                user_workout_plan_id = int(request.GET.get('user_workout_plan'))
                user_workout_plan = UserWorkoutPlan.objects.get(id=user_workout_plan_id)
                initial['user_workout_plan'] = user_workout_plan

                # Mark this as a user-specific workout day
                initial['workout_plan'] = None
            except (ValueError, UserWorkoutPlan.DoesNotExist):
                pass

        # Check if day_number is in the URL parameters
        if 'day_number' in request.GET:
            try:
                day_number = int(request.GET.get('day_number'))
                initial['day_number'] = day_number
            except ValueError:
                pass

        return initial

    def save_model(self, request, obj, form, change):
        """Set parent UserWorkoutPlan as modified if a cloned day is changed."""
        if change and obj.user_workout_plan:
            obj.user_workout_plan.is_modified = True
            obj.user_workout_plan.save(update_fields=['is_modified'])
            messages.info(request, f"Marked {obj.user_workout_plan} as modified due to change in Workout Day.")
        super().save_model(request, obj, form, change)

class WorkoutSectionInline(admin.TabularInline):
    model = WorkoutSection
    extra = 1
    fields = ('name', 'section_type', 'order', 'duration')

@admin.register(WorkoutSession)
class WorkoutSessionAdmin(admin.ModelAdmin):
    inlines = [WorkoutSectionInline]
    list_display = ('name', 'workout_day_link', 'order', 'duration', 'view_sections_link')
    list_filter = ('workout_day__workout_plan', 'workout_day__user_workout_plan')
    search_fields = ('name', 'description', 'workout_day__name')
    readonly_fields = ('workout_day_link', 'view_sections_link')

    def workout_day_link(self, obj):
        if obj.workout_day:
            url = reverse("admin:workouts_workoutday_change", args=[obj.workout_day.id])
            return mark_safe(f'<a href="{url}">{obj.workout_day.name}</a>')
        return "N/A"
    workout_day_link.short_description = 'Workout Day'

    def view_sections_link(self, obj):
        count = WorkoutSection.objects.filter(workout_session=obj).count()
        url = (
            f"/admin/workouts/workoutsection/?workout_session__id__exact={obj.id}"
        )
        return mark_safe(f'<a href="{url}">{count} sections</a>')
    view_sections_link.short_description = 'Sections'

    def get_changeform_initial_data(self, request):
        """Pre-fill the form with workout_day and user_workout_plan from URL parameters."""
        initial = super().get_changeform_initial_data(request)

        # Check if workout_day is in the URL parameters
        if 'workout_day' in request.GET:
            try:
                workout_day_id = int(request.GET.get('workout_day'))
                workout_day = WorkoutDay.objects.get(id=workout_day_id)
                initial['workout_day'] = workout_day

                # If the workout day belongs to a user plan, set that too
                if workout_day.user_workout_plan:
                    initial['user_workout_plan'] = workout_day.user_workout_plan
            except (ValueError, WorkoutDay.DoesNotExist):
                pass

        # Check if user_workout_plan is in the URL parameters
        if 'user_workout_plan' in request.GET:
            try:
                user_workout_plan_id = int(request.GET.get('user_workout_plan'))
                user_workout_plan = UserWorkoutPlan.objects.get(id=user_workout_plan_id)
                initial['user_workout_plan'] = user_workout_plan
            except (ValueError, UserWorkoutPlan.DoesNotExist):
                pass

        return initial

    def save_model(self, request, obj, form, change):
        """Set parent UserWorkoutPlan as modified if a cloned session is changed."""
        if change and obj.user_workout_plan:
            obj.user_workout_plan.is_modified = True
            obj.user_workout_plan.save(update_fields=['is_modified'])
            messages.info(request, f"Marked {obj.user_workout_plan} as modified due to change in Workout Session.")
        super().save_model(request, obj, form, change)

class ExerciseInstanceInline(admin.TabularInline):
    model = ExerciseInstance
    extra = 1
    fields = ('exercise', 'order', 'sets', 'reps', 'duration_seconds', 'rest_seconds')
    autocomplete_fields = ['exercise']

@admin.register(WorkoutSection)
class WorkoutSectionAdmin(admin.ModelAdmin):
    inlines = [ExerciseInstanceInline]
    list_display = ('name', 'workout_session_link', 'section_type', 'order', 'view_exercises_link')
    list_filter = ('section_type', 'workout_session__workout_day__workout_plan')
    search_fields = ('name', 'notes', 'workout_session__name')
    readonly_fields = ('workout_session_link', 'view_exercises_link')

    def workout_session_link(self, obj):
        if obj.workout_session:
            url = reverse("admin:workouts_workoutsession_change", args=[obj.workout_session.id])
            return mark_safe(f'<a href="{url}">{obj.workout_session.name}</a>')
        return "N/A"
    workout_session_link.short_description = 'Workout Session'

    def view_exercises_link(self, obj):
        count = ExerciseInstance.objects.filter(workout_section=obj).count()
        url = (
            reverse("admin:workouts_exerciseinstance_changelist")
            + "?"
            + f"workout_section__id__exact={obj.id}"
        )
        return mark_safe(f'<a href="{url}">View {count} Exercises</a>')
    view_exercises_link.short_description = "Exercises"

    def get_changeform_initial_data(self, request):
        """Pre-fill the form with workout_session and user_workout_plan from URL parameters."""
        initial = super().get_changeform_initial_data(request)

        # Check if workout_session is in the URL parameters
        if 'workout_session' in request.GET:
            try:
                workout_session_id = int(request.GET.get('workout_session'))
                workout_session = WorkoutSession.objects.get(id=workout_session_id)
                initial['workout_session'] = workout_session

                # If the workout session belongs to a user plan, set that too
                if workout_session.user_workout_plan:
                    initial['user_workout_plan'] = workout_session.user_workout_plan
            except (ValueError, WorkoutSession.DoesNotExist):
                pass

        # Check if user_workout_plan is in the URL parameters
        if 'user_workout_plan' in request.GET:
            try:
                user_workout_plan_id = int(request.GET.get('user_workout_plan'))
                user_workout_plan = UserWorkoutPlan.objects.get(id=user_workout_plan_id)
                initial['user_workout_plan'] = user_workout_plan
            except (ValueError, UserWorkoutPlan.DoesNotExist):
                pass

        return initial

    def save_model(self, request, obj, form, change):
        """Set parent UserWorkoutPlan as modified if a cloned section is changed."""
        if change and obj.user_workout_plan:
            obj.user_workout_plan.is_modified = True
            obj.user_workout_plan.save(update_fields=['is_modified'])
            messages.info(request, f"Marked {obj.user_workout_plan} as modified due to change in Workout Section.")
        super().save_model(request, obj, form, change)

@admin.register(ExerciseInstance)
class ExerciseInstanceAdmin(admin.ModelAdmin):
    list_display = ('order', 'exercise_link', 'workout_section_link', 'sets', 'reps', 'duration_seconds')
    list_display_links = ('order',)
    list_filter = ('workout_section__workout_session__workout_day__workout_plan', 'exercise__muscle_group')
    search_fields = ('exercise__name', 'workout_section__name', 'notes')
    readonly_fields = ('exercise_link', 'workout_section_link')

    def exercise_link(self, obj):
        if obj.exercise:
            url = reverse("admin:workouts_exercise_change", args=[obj.exercise.id])
            return mark_safe(f'<a href="{url}">{obj.exercise.name}</a>')
        return "N/A"
    exercise_link.short_description = 'Exercise'

    def workout_section_link(self, obj):
        if obj.workout_section:
            url = reverse("admin:workouts_workoutsection_change", args=[obj.workout_section.id])
            return mark_safe(f'<a href="{url}">{obj.workout_section.name}</a>')
        return "N/A"
    workout_section_link.short_description = 'Workout Section'

    def save_model(self, request, obj, form, change):
        """Set parent UserWorkoutPlan as modified if a cloned instance is changed."""
        if change and obj.user_workout_plan:
            obj.user_workout_plan.is_modified = True
            obj.user_workout_plan.save(update_fields=['is_modified'])
            messages.info(request, f"Marked {obj.user_workout_plan} as modified due to change in Exercise Instance.")
        super().save_model(request, obj, form, change)

@admin.register(WorkoutSchedule)
class WorkoutScheduleAdmin(admin.ModelAdmin):
    class WorkoutScheduleForm(forms.ModelForm):
        # Create fields for day numbers instead of weekdays
        day_1 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 1")
        day_2 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 2")
        day_3 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 3")
        day_4 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 4")
        day_5 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 5")
        day_6 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 6")
        day_7 = forms.ModelChoiceField(queryset=WorkoutDay.objects.all(), required=False, label="Day 7")

        class Meta:
            model = WorkoutSchedule
            fields = ['workout_plan', 'user_workout_plan', 'week_number']

        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            instance = kwargs.get('instance')

            if instance:
                # Calculate the base day number for this week
                base_day = (instance.week_number - 1) * 7 + 1

                # Map weekday fields to day numbers
                weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                for i, field_name in enumerate(weekday_fields):
                    day_number = base_day + i
                    day_field = f'day_{i+1}'
                    self.fields[day_field].initial = getattr(instance, field_name)

                    # Update the label to show the absolute day number
                    self.fields[day_field].label = f"Day {day_number}"

                    # Filter workout days by plan
                    if instance.workout_plan:
                        self.fields[day_field].queryset = WorkoutDay.objects.filter(
                            Q(workout_plan=instance.workout_plan) |
                            Q(user_workout_plan=instance.user_workout_plan)
                        )
                    elif instance.user_workout_plan:
                        self.fields[day_field].queryset = WorkoutDay.objects.filter(
                            Q(workout_plan=instance.user_workout_plan.workout_plan) |
                            Q(user_workout_plan=instance.user_workout_plan)
                        )

        def clean(self):
            cleaned_data = super().clean()
            week_number = cleaned_data.get('week_number')
            workout_plan = cleaned_data.get('workout_plan')
            user_workout_plan = cleaned_data.get('user_workout_plan')

            # Check for duplicate week numbers
            if week_number:
                if user_workout_plan:
                    # Check for duplicate week in user workout plan
                    existing = WorkoutSchedule.objects.filter(
                        user_workout_plan=user_workout_plan,
                        week_number=week_number
                    )
                    if self.instance and self.instance.pk:
                        existing = existing.exclude(pk=self.instance.pk)

                    if existing.exists():
                        self.add_error('week_number', f'Week {week_number} already exists for this user workout plan. Please choose a different week number.')

                elif workout_plan:
                    # Check for duplicate week in template workout plan
                    existing = WorkoutSchedule.objects.filter(
                        workout_plan=workout_plan,
                        week_number=week_number,
                        user_workout_plan__isnull=True
                    )
                    if self.instance and self.instance.pk:
                        existing = existing.exclude(pk=self.instance.pk)

                    if existing.exists():
                        self.add_error('week_number', f'Week {week_number} already exists for this workout plan template. Please choose a different week number.')

            return cleaned_data

        def save(self, commit=True):
            instance = super().save(commit=False)

            # Map day fields to weekday fields
            weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            for i in range(7):
                day_field = f'day_{i+1}'
                weekday_field = weekday_fields[i]
                setattr(instance, weekday_field, self.cleaned_data.get(day_field))

            # Update the days JSON field
            days_mapping = {}
            base_day = (instance.week_number - 1) * 7 + 1

            for i in range(7):
                day_field = f'day_{i+1}'
                day_number = base_day + i
                workout_day = self.cleaned_data.get(day_field)
                if workout_day:
                    days_mapping[str(day_number)] = workout_day.id

            instance.days = days_mapping

            if commit:
                instance.save()
            return instance

    form = WorkoutScheduleForm
    list_display = ('__str__', 'week_number', 'days_display')
    list_filter = ('workout_plan', 'user_workout_plan', 'week_number')
    search_fields = ('workout_plan__name', 'user_workout_plan__user__user__username')
    ordering = ('workout_plan', 'user_workout_plan', 'week_number')
    readonly_fields = ('days_display',)

    def days_display(self, obj):
        """Display the days mapping in a more readable format"""
        if not obj.days:
            return "No days assigned"

        from django.utils.safestring import mark_safe
        from workouts.models import WorkoutDay

        html = ["<table style='width:100%; border-collapse:collapse;'>",
                "<tr style='background-color:#f5f5f5;'>",
                "<th style='padding:8px; border:1px solid #ddd;'>Program Day</th>",
                "<th style='padding:8px; border:1px solid #ddd;'>Date</th>",
                "<th style='padding:8px; border:1px solid #ddd;'>Workout</th>",
                "</tr>"]

        # Sort the days by day number
        sorted_days = sorted(obj.days.items(), key=lambda x: int(x[0]))

        # Calculate dates if this is a user workout plan
        start_date = None
        if obj.user_workout_plan and obj.user_workout_plan.start_date:
            from datetime import timedelta
            start_date = obj.user_workout_plan.start_date

        for day_num, day_id in sorted_days:
            # Calculate the date for this program day
            date_str = "N/A"
            if start_date:
                day_offset = int(day_num) - 1  # Convert to 0-indexed for timedelta
                program_date = start_date + timedelta(days=day_offset)
                date_str = program_date.strftime("%b %d, %Y")

            if day_id is not None:
                try:
                    workout_day = WorkoutDay.objects.get(id=day_id)
                    day_name = workout_day.name
                    day_url = f"/admin/workouts/workoutday/{day_id}/change/"
                    row_html = f"<tr><td style='padding:8px; border:1px solid #ddd;'>Day {day_num}</td><td style='padding:8px; border:1px solid #ddd;'>{date_str}</td><td style='padding:8px; border:1px solid #ddd;'><a href='{day_url}'>{day_name}</a></td></tr>"
                except WorkoutDay.DoesNotExist:
                    row_html = f"<tr><td style='padding:8px; border:1px solid #ddd;'>Day {day_num}</td><td style='padding:8px; border:1px solid #ddd;'>{date_str}</td><td style='padding:8px; border:1px solid #ddd;'>Workout not found (ID: {day_id})</td></tr>"
            else:
                row_html = f"<tr><td style='padding:8px; border:1px solid #ddd;'>Day {day_num}</td><td style='padding:8px; border:1px solid #ddd;'>{date_str}</td><td style='padding:8px; border:1px solid #ddd;'>Rest Day</td></tr>"
            html.append(row_html)

        html.append("</table>")
        return mark_safe(''.join(html))

    fieldsets = (
        ('Schedule Information', {
            'fields': ('workout_plan', 'user_workout_plan', 'week_number')
        }),
        ('Workout Days', {
            'fields': ('day_1', 'day_2', 'day_3', 'day_4', 'day_5', 'day_6', 'day_7')
        }),
        ('Days Display', {
            'fields': ('days_display',)
        }),
    )

    def get_changeform_initial_data(self, request):
        """Pre-fill the form with user_workout_plan from URL parameters."""
        initial = super().get_changeform_initial_data(request)

        # Check if user_workout_plan is in the URL parameters
        if 'user_workout_plan' in request.GET:
            try:
                user_workout_plan_id = int(request.GET.get('user_workout_plan'))
                user_workout_plan = UserWorkoutPlan.objects.get(id=user_workout_plan_id)
                initial['user_workout_plan'] = user_workout_plan

                # Mark this as a user-specific schedule
                initial['workout_plan'] = None

                # Set a default week number if not provided
                if 'week_number' not in initial:
                    # Find the highest week number for this user plan
                    highest_week = WorkoutSchedule.objects.filter(user_workout_plan=user_workout_plan).aggregate(Max('week_number'))
                    highest_week_num = highest_week.get('week_number__max') or 0
                    initial['week_number'] = highest_week_num + 1
            except (ValueError, UserWorkoutPlan.DoesNotExist):
                pass

        return initial

    def days_display(self, obj):
        """Display the days mapping in a readable format using day numbers instead of weekdays"""
        if not obj.days:
            return "No days assigned"

        # Get the days mapping
        days_mapping = {}
        for day_num, day_id in obj.days.items():
            try:
                day = WorkoutDay.objects.get(id=day_id)
                days_mapping[int(day_num)] = day
            except (WorkoutDay.DoesNotExist, ValueError):
                pass

        # Sort the days mapping by day number
        sorted_days = sorted(days_mapping.items())

        # Create a list of day links
        day_links = []
        for day_num, workout_day in sorted_days:
            url = reverse("admin:workouts_workoutday_change", args=[workout_day.id])
            day_links.append(f'Day {day_num}: <a href="{url}">{workout_day.name}</a>')

        # Join the day links with line breaks
        return mark_safe('<br>'.join(day_links))
    days_display.short_description = 'Days'

    def save_model(self, request, obj, form, change):
        """Mark parent UserWorkoutPlan as modified if a user-specific schedule is changed directly."""
        super().save_model(request, obj, form, change)
        if change and obj.user_workout_plan:
             obj.user_workout_plan.is_modified = True
             if obj.user_workout_plan.pk:
                 obj.user_workout_plan.save(update_fields=['is_modified'])
                 messages.info(request, f"Marked {obj.user_workout_plan} as modified due to direct change in user schedule.")


class WorkoutCalendarAdmin(admin.ModelAdmin):
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('workout-calendar/', self.admin_site.admin_view(self.workout_calendar_view), name='workout-calendar'),
        ]
        return custom_urls + urls

    def workout_calendar_view(self, request):
        # Get the current year and month
        now = timezone.now()
        year = int(request.GET.get('year', now.year))
        month = int(request.GET.get('month', now.month))

        # Get the calendar for the current month
        cal = calendar.monthcalendar(year, month)
        month_name = calendar.month_name[month]

        # Get the previous and next month/year
        prev_month = month - 1
        prev_year = year
        if prev_month == 0:
            prev_month = 12
            prev_year -= 1

        next_month = month + 1
        next_year = year
        if next_month == 13:
            next_month = 1
            next_year += 1

        # Get all active user workout plans
        user_workout_plans = UserWorkoutPlan.objects.filter(is_active=True)

        # Get the selected plan if provided
        selected_plan_id = request.GET.get('plan_id')
        selected_plan = None
        plan_data = {}

        if selected_plan_id:
            try:
                selected_plan = UserWorkoutPlan.objects.get(id=selected_plan_id)

                # Get all workout days for this plan
                workout_days = WorkoutDay.objects.filter(user_workout_plan=selected_plan)

                # Create a mapping of dates to workout days
                plan_data = {
                    'days': {},
                    'start_date': selected_plan.start_date.strftime('%Y-%m-%d'),
                    'end_date': selected_plan.end_date.strftime('%Y-%m-%d') if selected_plan.end_date else None,
                }

                # Calculate the current program day
                current_day = selected_plan.get_current_day()

                # For each day in the program, calculate its date and add to the mapping
                for day_number in range(1, 90):  # Limit to 90 days for performance
                    day_date = selected_plan.start_date + timedelta(days=day_number - 1)
                    day_date_str = day_date.strftime('%Y-%m-%d')

                    # Find the workout day for this program day
                    workout_day = None

                    # Calculate which week and day number within the program
                    week_number = (day_number - 1) // 7 + 1
                    day_in_week = (day_number - 1) % 7 + 1  # 1-7 for days mapping

                    # Check if we need to loop back to week 1
                    total_weeks = selected_plan.workout_plan.duration_weeks or 4
                    if week_number > total_weeks:
                        week_number = ((week_number - 1) % total_weeks) + 1

                    # Try to find the workout schedule for this week
                    try:
                        schedule = WorkoutSchedule.objects.get(
                            user_workout_plan=selected_plan,
                            week_number=week_number
                        )

                        # Get the workout day for this day in the week
                        weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                        workout_day = getattr(schedule, weekday_fields[day_in_week - 1])

                        if workout_day:
                            plan_data['days'][day_date_str] = {
                                'id': workout_day.id,
                                'name': workout_day.name,
                                'day_number': day_number,
                                'is_rest_day': False,
                                'url': reverse('admin:workouts_workoutday_change', args=[workout_day.id])
                            }
                        else:
                            # Rest day
                            plan_data['days'][day_date_str] = {
                                'day_number': day_number,
                                'is_rest_day': True,
                                'name': 'Rest Day'
                            }
                    except WorkoutSchedule.DoesNotExist:
                        # No schedule for this week
                        pass
            except UserWorkoutPlan.DoesNotExist:
                selected_plan = None

        context = {
            'title': 'Workout Calendar',
            'calendar': cal,
            'month_name': month_name,
            'year': year,
            'prev_month': prev_month,
            'prev_year': prev_year,
            'next_month': next_month,
            'next_year': next_year,
            'user_workout_plans': user_workout_plans,
            'selected_plan': selected_plan,
            'plan_data': plan_data,
            'today': now.date().strftime('%Y-%m-%d'),
        }

        return render(request, 'admin/workout_calendar.html', context)

@admin.register(UserWorkoutPlan)
class UserWorkoutPlanAdmin(admin.ModelAdmin):
    # Add a custom form with sync options
    class CustomUserWorkoutPlanForm(forms.ModelForm):
        SYNC_CHOICES = [
            ('none', 'Don\'t sync with global plan (no changes)'),
            ('all', 'Sync everything with global plan (overwrite all modifications)'),
            ('unmodified', 'Only sync unmodified parts (preserve custom changes)'),
            ('future', 'Only update future days (preserve past/current days)')
        ]

        sync_option = forms.ChoiceField(
            choices=SYNC_CHOICES,
            required=False,
            initial='none',
            widget=forms.RadioSelect,
            help_text="Choose how to sync this user plan with the global workout plan"
        )

        class Meta:
            model = UserWorkoutPlan
            fields = '__all__'

    form = CustomUserWorkoutPlanForm
    list_display = ('user_link', 'workout_plan_link', 'start_date', 'end_date', 'is_active', 'is_modified', 'view_calendar_link')
    list_filter = ('is_active', 'workout_plan', 'start_date', 'is_modified')
    search_fields = ('user__user__username', 'workout_plan__name')
    readonly_fields = ('created_at', 'updated_at', 'user_link', 'workout_plan_link', 'is_modified', 'modifications', 'view_calendar_link')
    ordering = ('-start_date',)
    inlines = [UserWorkoutScheduleInline]

    fieldsets = (
        ('User and Plan', {'fields': ('user', 'workout_plan')}),
        ('Schedule', {'fields': ('start_date', 'end_date', 'is_active', 'view_calendar_link')}),
        ('Sync Options', {'fields': ('sync_option',), 'classes': ('wide',)}),
        ('Status', {'fields': ('is_modified', 'modifications', 'has_completed_one_cycle'), 'classes': ('collapse',)}),
        ('Metadata', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )

    def view_calendar_link(self, obj):
        if obj.pk:
            url = reverse('acfit_admin:workout-calendar') + f'?plan_id={obj.pk}'
            return mark_safe(f'<a href="{url}" class="button" target="_blank">View Calendar</a>')
        return "Save the plan first to view calendar"
    view_calendar_link.short_description = 'Calendar'

    def get_formsets_with_inlines(self, request, obj=None):
        for inline in self.get_inline_instances(request, obj):
            if isinstance(inline, UserWorkoutScheduleInline) and obj is not None:
                 pass
            yield inline.get_formset(request, obj), inline

    # Add a custom save method to handle sync options
    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)

        if change and 'sync_option' in form.cleaned_data:
            sync_option = form.cleaned_data['sync_option']

            try:
                if sync_option == 'all':
                    with transaction.atomic():
                        obj.sync_with_global(overwrite_all=True)
                        messages.success(request, f"Successfully synced all parts of the plan with the global plan.")
                elif sync_option == 'unmodified':
                    with transaction.atomic():
                        obj.sync_with_global(overwrite_all=False)
                        messages.success(request, f"Successfully synced unmodified parts of the plan.")
                elif sync_option == 'future':
                    with transaction.atomic():
                        obj.sync_future_days()
                        messages.success(request, f"Successfully applied changes to future days only.")
            except Exception as e:
                logger.error(f"Error syncing UserWorkoutPlan {obj.id}: {e}", exc_info=True)
                messages.error(request, f"Error syncing plan: {str(e)}")

    def save_formset(self, request, form, formset, change):
        """Handle saving inline user schedules and mark parent as modified."""
        instances = formset.save(commit=False)
        user_plan_obj = form.instance
        schedule_changed = False

        for obj in formset.deleted_objects:
            if obj.user_workout_plan == user_plan_obj:
                 schedule_changed = True
            obj.delete()

        for instance in instances:
            instance.user_workout_plan = user_plan_obj
            if not instance.workout_plan:
                 instance.workout_plan = user_plan_obj.workout_plan

            form_instance = next((f for f in formset.forms if f.instance == instance), None)
            if form_instance:
                 if formset.can_delete and formset._should_delete_form(form_instance):
                     if instance.pk:
                         instance.delete()
                     continue

                 if form_instance.has_changed():
                     schedule_changed = True
                     instance.save()
            elif instance.pk is None:
                 schedule_changed = True
                 instance.save()
        formset.save_m2m()

        if schedule_changed:
            if not user_plan_obj.is_modified:
                 user_plan_obj.is_modified = True
                 user_plan_obj.save(update_fields=['is_modified'])
                 messages.info(request, f"Marked {user_plan_obj} as modified due to schedule change via inline.")

    def user_link(self, obj):
        if obj.user:
            url = reverse("admin:accounts_userprofile_change", args=[obj.user.id])
            return mark_safe(f'<a href="{url}">{obj.user.user.username}</a>')
        return "N/A"
    user_link.short_description = 'User'

    def workout_plan_link(self, obj):
        if obj.workout_plan:
            url = reverse("admin:workouts_workoutplan_change", args=[obj.workout_plan.id])
            return mark_safe(f'<a href="{url}">{obj.workout_plan.name}</a>')
        return "N/A"
    workout_plan_link.short_description = 'Workout Plan'

@admin.register(WorkoutLog)
class WorkoutLogAdmin(admin.ModelAdmin):
    list_display = ('user_link', 'workout_day_link', 'date', 'is_completed', 'completion_time', 'calories_burned', 'duration_minutes')
    list_filter = ('is_completed', 'date', 'workout_day__workout_plan')
    search_fields = ('user__user__username', 'workout_day__name', 'notes')
    readonly_fields = ('user_link', 'workout_day_link')
    ordering = ('-date',)

    def user_link(self, obj):
        if obj.user:
            url = reverse("admin:accounts_userprofile_change", args=[obj.user.id])
            return mark_safe(f'<a href="{url}">{obj.user.user.username}</a>')
        return "N/A"
    user_link.short_description = 'User'

    def workout_day_link(self, obj):
        if obj.workout_day:
            url = reverse("admin:workouts_workoutday_change", args=[obj.workout_day.id])
            return mark_safe(f'<a href="{url}">{obj.workout_day.name}</a>')
        return "N/A"
    workout_day_link.short_description = 'Workout Day'

@admin.register(ExerciseLog)
class ExerciseLogAdmin(admin.ModelAdmin):
    list_display = ('workout_log_link', 'exercise_link', 'is_completed', 'actual_sets', 'actual_reps', 'actual_weight')
    list_filter = ('is_completed', 'exercise__muscle_group')
    search_fields = ('workout_log__user__user__username', 'exercise__name', 'notes')
    readonly_fields = ('workout_log_link', 'exercise_link')

    def workout_log_link(self, obj):
        if obj.workout_log:
            url = reverse("admin:workouts_workoutlog_change", args=[obj.workout_log.id])
            display_text = f"Log for {obj.workout_log.user.user.username} on {obj.workout_log.date}"
            return mark_safe(f'<a href="{url}">{display_text}</a>')
        return "N/A"
    workout_log_link.short_description = 'Workout Log'

    def exercise_link(self, obj):
        if obj.exercise:
            url = reverse("admin:workouts_exercise_change", args=[obj.exercise.id])
            return mark_safe(f'<a href="{url}">{obj.exercise.name}</a>')
        return "N/A"
    exercise_link.short_description = 'Exercise'

@admin.register(WorkoutReminder)
class WorkoutReminderAdmin(admin.ModelAdmin):
    list_display = ('user_link', 'workout_day_link', 'time', 'is_active', 'days_of_week')
    list_filter = ('is_active', 'time')
    search_fields = ('user__user__username', 'workout_day__name')
    readonly_fields = ('user_link', 'workout_day_link')
    ordering = ('time',)

    def user_link(self, obj):
        if obj.user:
            url = reverse("admin:accounts_userprofile_change", args=[obj.user.id])
            return mark_safe(f'<a href="{url}">{obj.user.user.username}</a>')
        return "N/A"
    user_link.short_description = 'User'

    def workout_day_link(self, obj):
        if obj.workout_day:
            url = reverse("admin:workouts_workoutday_change", args=[obj.workout_day.id])
            return mark_safe(f'<a href="{url}">{obj.workout_day.name}</a>')
        return "N/A"
    workout_day_link.short_description = 'Workout Day'

# We don't register the WorkoutCalendarAdmin with the admin site directly
# It's used by the custom admin site in admin_site/admin.py

@admin.register(WorkoutSessionLog)
class WorkoutSessionLogAdmin(admin.ModelAdmin):
    list_display = ('workout_log_link', 'workout_session_link', 'date', 'is_completed', 'completion_time', 'duration_minutes', 'calories_burned')
    list_filter = ('is_completed', 'date', 'workout_session__workout_day__workout_plan')
    search_fields = ('workout_log__user__user__username', 'workout_session__name', 'notes')
    readonly_fields = ('workout_log_link', 'workout_session_link')
    ordering = ('-date',)

    def workout_log_link(self, obj):
        if obj.workout_log:
            url = reverse("admin:workouts_workoutlog_change", args=[obj.workout_log.id])
            display_text = f"Log for {obj.workout_log.user.user.username} on {obj.workout_log.date}"
            return mark_safe(f'<a href="{url}">{display_text}</a>')
        return "N/A"
    workout_log_link.short_description = 'Workout Log'

    def workout_session_link(self, obj):
        if obj.workout_session:
            url = reverse("admin:workouts_workoutsession_change", args=[obj.workout_session.id])
            return mark_safe(f'<a href="{url}">{obj.workout_session.name}</a>')
        return "N/A"
    workout_session_link.short_description = 'Workout Session'


class WorkoutVideoAdmin(admin.ModelAdmin):
    list_display = ('title', 'workout_plan_link', 'order', 'duration_display', 'video_preview', 'thumbnail_preview', 'created_at')
    list_filter = ('workout_plan', 'created_at')
    search_fields = ('title', 'description', 'workout_plan__name')
    readonly_fields = ('video_preview', 'thumbnail_preview', 'workout_plan_link', 'created_at', 'updated_at')
    ordering = ('workout_plan', 'order', 'created_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('workout_plan', 'title', 'description', 'order')
        }),
        ('Media Files', {
            'fields': ('video_file', 'video_preview', 'thumbnail', 'thumbnail_preview')
        }),
        ('Metadata', {
            'fields': ('duration_seconds', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def workout_plan_link(self, obj):
        if obj.workout_plan:
            url = reverse("admin:workouts_workoutplan_change", args=[obj.workout_plan.id])
            return mark_safe(f'<a href="{url}">{obj.workout_plan.name}</a>')
        return "N/A"
    workout_plan_link.short_description = 'Workout Plan'

    def video_preview(self, obj):
        if obj.video_file:
            return mark_safe(f'''
                <video width="300" height="200" controls>
                    <source src="{obj.video_file.url}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            ''')
        return "No video uploaded"
    video_preview.short_description = 'Video Preview'

    def thumbnail_preview(self, obj):
        if obj.thumbnail:
            return mark_safe(f'<img src="{obj.thumbnail.url}" width="150" height="100" style="object-fit: cover;" />')
        return "No thumbnail"
    thumbnail_preview.short_description = 'Thumbnail Preview'

    def duration_display(self, obj):
        if obj.duration_seconds:
            minutes = obj.duration_seconds // 60
            seconds = obj.duration_seconds % 60
            return f"{minutes}:{seconds:02d}"
        return "Unknown"
    duration_display.short_description = 'Duration'
