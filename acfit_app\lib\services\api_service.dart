import '../config/api_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../constants/api_constants.dart'; // Use the constants file with all required constants
import '../models/user_score.dart'; // Import the UserScore model
import '../models/user_progress.dart'; // Import the UserProgress model
import '../utils/logger.dart'; // Import the Logger utility
import '../models/workout_log.dart'; // Imports TodaysWorkoutResponse, WorkoutLog
import '../models/workout.dart'; // Import WorkoutDay, WorkoutSession, ExerciseDetail, WorkoutPlan etc.
import '../models/meal_log.dart'; // Imports TodaysMealResponse, MealLog, Hydration
import '../models/user_profile.dart'; // Import the UserProfile model
import '../models/meal.dart'; // Import MealDetail, MealPlan etc.
import 'package:dio/dio.dart';
import 'package:intl/intl.dart'; // Import for date formatting
import 'notification_service.dart'; // Import the NotificationService
// Import logger already imported above

class ApiService {
  final _secureStorage = const FlutterSecureStorage();
  late final Dio _dio;
  final String _accessTokenKey = 'auth_token';
  final String _refreshTokenKey = 'refresh_token';

  // Cache for calorie data
  Map<String, dynamic>? _calorieData;

  // Getter for the base URL
  String get baseUrl => _dio.options.baseUrl;

  // Clear all caches
  void clearAllCaches() {
    _calorieData = null;
  }

  ApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: apiBaseUrl, // Use the constant from api_config.dart
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        validateStatus: (status) => status != null && status < 500,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          Logger.api('--> ${options.method} ${options.uri}',
              tag: 'ApiService.Request');
          final token = await _secureStorage.read(key: _accessTokenKey);
          if (token != null && token.isNotEmpty) {
            final cleanToken =
                token.replaceAll('Token ', '').replaceAll('Bearer ', '');
            options.headers['Authorization'] = 'Bearer $cleanToken';
            // Logger.log('   Headers: ${options.headers}', name: 'ApiService.Request'); // Less verbose logging
          }
          // if (options.data != null) { // Less verbose logging
          //   Logger.log('   Data: ${options.data}', name: 'ApiService.Request');
          // }
          handler.next(options);
        },
        onResponse: (response, handler) {
          Logger.api(
              '<-- ${response.statusCode} ${response.requestOptions.uri}',
              tag: 'ApiService.Response');
          // Logger.log('    Response Data: ${response.data}', name: 'ApiService.Response'); // Less verbose logging
          handler.next(response);
        },
        onError: (DioException e, handler) async {
          Logger.api(
              '<-- Error ${e.response?.statusCode ?? 'N/A'} ${e.requestOptions.uri}',
              tag: 'ApiService.Error');
          if (e.response != null) {
            Logger.api('    Error Data: ${e.response?.data}',
                tag: 'ApiService.Error');
          } else {
            Logger.api('    Error Message: ${e.message}',
                tag: 'ApiService.Error');
          }
          if (e.response?.statusCode == 401) {
            Logger.api(
                'Handling 401 Unauthorized error, attempting token refresh...',
                tag: 'ApiService.Error');
            final refreshed = await refreshToken();
            if (refreshed) {
              Logger.api(
                  'Token refreshed successfully, retrying original request.',
                  tag: 'ApiService.Error');
              final token = await _secureStorage.read(key: _accessTokenKey);
              final options = e.requestOptions;
              options.headers['Authorization'] = 'Bearer $token';
              try {
                final response = await _dio.fetch(options);
                Logger.api('Retry successful: ${response.statusCode}',
                    tag: 'ApiService.Error');
                handler.resolve(response);
                return;
              } catch (retryError) {
                Logger.api('Retry failed: $retryError',
                    tag: 'ApiService.Error');
              }
            } else {
              Logger.api('Token refresh failed.', tag: 'ApiService.Error');
            }
          }
          handler.next(e);
        },
      ),
    );
  }

  String _getErrorCode(int? statusCode) {
    switch (statusCode) {
      case 400:
        return 'BAD_REQUEST';
      case 401:
        return 'UNAUTHORIZED';
      case 403:
        return 'FORBIDDEN';
      case 404:
        return 'NOT_FOUND';
      case 429:
        return 'RATE_LIMIT_EXCEEDED';
      default:
        return 'INTERNAL_SERVER_ERROR';
    }
  }

  // --- Public HTTP Method Wrappers ---
  Future<dynamic> get(String path,
      {Map<String, dynamic>? queryParameters}) async {
    final fullPath = '${_dio.options.baseUrl}$path';
    // Logger.log('Attempting GET: $fullPath', name: 'ApiService.get'); // Less verbose
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      // Logger.log('GET Success: ${response.statusCode} for $fullPath', name: 'ApiService.get'); // Less verbose
      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300) {
        return response.data;
      } else {
        Logger.api('GET Error (Non-2xx): ${response.statusCode} for $fullPath',
            tag: 'ApiService.get');
        throw DioException(
            requestOptions: response.requestOptions, response: response);
      }
    } on DioException catch (e) {
      Logger.api(
          'GET DioException: ${e.response?.statusCode ?? 'N/A'} for $fullPath - ${e.message}',
          tag: 'ApiService.get',
          error: e);
      rethrow;
    } catch (e) {
      Logger.api('GET Unexpected Error for $fullPath: $e',
          tag: 'ApiService.get', error: e);
      rethrow;
    }
  }

  Future<dynamic> post(String path, {dynamic data, FormData? formData}) async {
    final fullPath = '${_dio.options.baseUrl}$path';
    Logger.api('Attempting POST: $fullPath with data: ${formData ?? data}',
        tag: 'ApiService.post');
    try {
      final response = await _dio.post(path, data: formData ?? data);
      Logger.api('POST Success: ${response.statusCode} for $fullPath',
          tag: 'ApiService.post');
      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300) {
        return response.data;
      } else {
        Logger.api(
            'POST Error (Non-2xx): ${response.statusCode} for $fullPath - Response: ${response.data}',
            tag: 'ApiService.post');
        throw DioException(
            requestOptions: response.requestOptions, response: response);
      }
    } on DioException catch (e) {
      Logger.api(
          'POST DioException: ${e.response?.statusCode ?? 'N/A'} for $fullPath - ${e.message} - Response: ${e.response?.data}',
          tag: 'ApiService.post',
          error: e);
      rethrow;
    } catch (e) {
      Logger.api('POST Unexpected Error for $fullPath: $e',
          tag: 'ApiService.post', error: e);
      rethrow;
    }
  }

  Future<dynamic> put(String path, {dynamic data}) async {
    final fullPath = '${_dio.options.baseUrl}$path';
    // Logger.log('Attempting PUT: $fullPath', name: 'ApiService.put'); // Less verbose
    try {
      final response = await _dio.put(path, data: data);
      // Logger.log('PUT Success: ${response.statusCode} for $fullPath', name: 'ApiService.put'); // Less verbose
      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300) {
        return response.data;
      } else {
        Logger.api('PUT Error (Non-2xx): ${response.statusCode} for $fullPath',
            tag: 'ApiService.put');
        throw DioException(
            requestOptions: response.requestOptions, response: response);
      }
    } on DioException catch (e) {
      Logger.api(
          'PUT DioException: ${e.response?.statusCode ?? 'N/A'} for $fullPath - ${e.message}',
          tag: 'ApiService.put',
          error: e);
      rethrow;
    } catch (e) {
      Logger.api('PUT Unexpected Error for $fullPath: $e',
          tag: 'ApiService.put', error: e);
      rethrow;
    }
  }

  Future<dynamic> patch(String path, {dynamic data, FormData? formData}) async {
    final fullPath = '${_dio.options.baseUrl}$path';
    // Logger.log('Attempting PATCH: $fullPath', name: 'ApiService.patch'); // Less verbose
    try {
      final response = await _dio.patch(
        path,
        data: formData ?? data,
      );
      // Logger.log('PATCH Success: ${response.statusCode} for $fullPath', name: 'ApiService.patch'); // Less verbose
      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300) {
        return response.data;
      } else {
        Logger.api(
            'PATCH Error (Non-2xx): ${response.statusCode} for $fullPath',
            tag: 'ApiService.patch');
        throw DioException(
            requestOptions: response.requestOptions, response: response);
      }
    } on DioException catch (e) {
      Logger.api(
          'PATCH DioException: ${e.response?.statusCode ?? 'N/A'} for $fullPath - ${e.message}',
          tag: 'ApiService.patch',
          error: e);
      rethrow;
    } catch (e) {
      Logger.api('PATCH Unexpected Error for $fullPath: $e',
          tag: 'ApiService.patch', error: e);
      rethrow;
    }
  }

  Future<dynamic> delete(String path, {dynamic data}) async {
    final fullPath = '${_dio.options.baseUrl}$path';
    // Logger.log('Attempting DELETE: $fullPath', name: 'ApiService.delete'); // Less verbose
    try {
      final response = await _dio.delete(path, data: data);
      // Logger.log('DELETE Success: ${response.statusCode} for $fullPath', name: 'ApiService.delete'); // Less verbose
      if (response.statusCode != null &&
          (response.statusCode! == 204 ||
              (response.statusCode! >= 200 && response.statusCode! < 300))) {
        return response.data;
      } else {
        Logger.api(
            'DELETE Error (Non-2xx): ${response.statusCode} for $fullPath',
            tag: 'ApiService.delete');
        throw DioException(
            requestOptions: response.requestOptions, response: response);
      }
    } on DioException catch (e) {
      Logger.api(
          'DELETE DioException: ${e.response?.statusCode ?? 'N/A'} for $fullPath - ${e.message}',
          tag: 'ApiService.delete',
          error: e);
      rethrow;
    } catch (e) {
      Logger.api('DELETE Unexpected Error for $fullPath: $e',
          tag: 'ApiService.delete', error: e);
      rethrow;
    }
  }

  // --- AUTHENTICATION METHODS ---
  Future<Map<String, dynamic>> login(String email, String password) async {
    Logger.api('Attempting login for email: $email', tag: 'ApiService.login');
    try {
      final data = {'email': email, 'password': password};
      final cleanDio = Dio(BaseOptions(
          baseUrl: apiBaseUrl,
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          validateStatus: (status) => true));
      final response = await cleanDio.post(ApiConstants.login, data: data);
      Logger.api('Login response status: ${response.statusCode}',
          tag: 'ApiService.login');
      if (response.statusCode == 200) {
        final responseData = response.data is Map ? response.data : {};
        final accessToken = responseData['access'];
        final refreshToken = responseData['refresh'];
        final userData = responseData['user'];
        if (accessToken != null && refreshToken != null) {
          await _secureStorage.write(key: _accessTokenKey, value: accessToken);
          await _secureStorage.write(
              key: _refreshTokenKey, value: refreshToken);
          Logger.api('Login successful, tokens stored.',
              tag: 'ApiService.login');
          return {
            'status': 'success',
            'data': {
              'message': 'Login successful',
              'user': userData,
              'access': accessToken,
              'refresh': refreshToken
            }
          };
        }
        Logger.api('Login response missing required tokens.',
            tag: 'ApiService.login');
        throw Exception('Login response missing required tokens');
      } else {
        String errorMessage = 'Login failed';
        if (response.data is Map && response.data['detail'] != null) {
          errorMessage = response.data['detail'];
        } else {
          errorMessage = 'Login failed with status ${response.statusCode}';
        }
        Logger.api('Login failed: $errorMessage', tag: 'ApiService.login');
        throw Exception(errorMessage);
      }
    } catch (e) {
      String message = 'Login failed: ${e.toString()}';
      if (e is DioException &&
          e.response?.data is Map &&
          e.response!.data['detail'] != null) {
        message = e.response!.data['detail'];
      }
      Logger.api('Login exception: $message',
          tag: 'ApiService.login', error: e);
      return {
        'status': 'error',
        'error': {
          'code': 'LOGIN_FAILED',
          'message': message,
          'details': e.toString()
        }
      };
    }
  }

  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: _accessTokenKey);
  }

  Future<bool> refreshToken() async {
    Logger.api('Attempting token refresh...', tag: 'ApiService.refreshToken');
    try {
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      if (refreshToken == null) {
        Logger.api('No refresh token found.', tag: 'ApiService.refreshToken');
        return false;
      }
      final cleanDio = Dio(BaseOptions(
          baseUrl: apiBaseUrl,
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          validateStatus: (status) => true));
      final response = await cleanDio
          .post(ApiConstants.refreshToken, data: {'refresh': refreshToken});
      Logger.api('Token refresh response status: ${response.statusCode}',
          tag: 'ApiService.refreshToken');
      if (response.statusCode == 200) {
        final newToken = response.data?['access'];
        if (newToken != null && newToken is String) {
          await _secureStorage.write(key: _accessTokenKey, value: newToken);
          Logger.api('Token refresh successful, new access token stored.',
              tag: 'ApiService.refreshToken');
          return true;
        } else {
          Logger.api('Token refresh response missing access token.',
              tag: 'ApiService.refreshToken');
        }
      } else {
        Logger.api(
            'Token refresh failed (status ${response.statusCode}), clearing tokens.',
            tag: 'ApiService.refreshToken');
        await _secureStorage.delete(key: _accessTokenKey);
        await _secureStorage.delete(key: _refreshTokenKey);
      }
      return false;
    } catch (e) {
      Logger.api('Token refresh exception, clearing tokens.',
          tag: 'ApiService.refreshToken', error: e);
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      return false;
    }
  }

  Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
    Logger.api('Attempting registration for email: ${userData['email']}',
        tag: 'ApiService.register');
    try {
      if (!userData.containsKey('password2') &&
          userData.containsKey('password')) {
        userData['password2'] = userData['password'];
      }
      final cleanDio = Dio(BaseOptions(
          baseUrl: apiBaseUrl,
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          validateStatus: (status) => true));
      final response =
          await cleanDio.post(ApiConstants.register, data: userData);
      Logger.api('Registration response status: ${response.statusCode}',
          tag: 'ApiService.register');
      if (response.statusCode == 201) {
        final responseData = response.data is Map ? response.data : {};
        Logger.api('ApiService.register: Raw responseData: $responseData',
            tag: 'ApiService.register');

        // Correctly access the nested 'requires_verification' flag
        final requiresVerification = (responseData['data'] is Map
                ? responseData['data']['requires_verification']
                : null) as bool? ??
            false;
        Logger.api(
            'ApiService.register: requires_verification evaluated to: $requiresVerification',
            tag: 'ApiService.register');

        if (requiresVerification) {
          Logger.api('Registration successful, email verification required.',
              tag: 'ApiService.register');
          return {
            'status': 'requires_verification',
            'data': {
              'message': 'Registration successful, please verify your email.',
              'user': responseData,
              'verification_code':
                  responseData['verification_code'], // Only in debug mode
            }
          };
        } else {
          Logger.api('Registration successful, attempting login...',
              tag: 'ApiService.register');
          final loginResult =
              await login(userData['email'], userData['password']);
          if (loginResult['status'] == 'success') {
            Logger.api('Login after registration successful.',
                tag: 'ApiService.register');
            return {
              'status': 'success',
              'data': {
                'message': 'Registration and login successful',
                'user': responseData,
                'tokens': loginResult['data']
              }
            };
          } else {
            Logger.api('Login after registration failed.',
                tag: 'ApiService.register');
            return {
              'status': 'success_needs_login',
              'data': {
                'message': 'Registration successful, please log in.',
                'user': responseData
              },
              'error': loginResult['error']
            };
          }
        }
      } else {
        String errorMessage = 'Registration failed';
        if (response.data is Map) {
          final errors = response.data as Map<String, dynamic>;
          if (errors.isNotEmpty) {
            errorMessage = errors.entries
                .map((e) =>
                    '${e.key}: ${e.value is List ? e.value.join(', ') : e.value}')
                .join('; ');
          } else if (errors['detail'] != null) {
            errorMessage = errors['detail'];
          } else {
            errorMessage =
                'Registration failed with status ${response.statusCode}';
          }
        } else if (response.data is String && response.data.isNotEmpty) {
          errorMessage = response.data;
        } else {
          errorMessage =
              'Registration failed with status ${response.statusCode}';
        }
        Logger.api('Registration failed: $errorMessage',
            tag: 'ApiService.register');
        return {
          'status': 'error',
          'error': {
            'code': _getErrorCode(response.statusCode),
            'message': errorMessage,
            'details': response.data
          }
        };
      }
    } catch (e) {
      Logger.api('Registration exception: ${e.toString()}',
          tag: 'ApiService.register', error: e);
      return {
        'status': 'error',
        'error': {
          'code': 'REGISTRATION_FAILED',
          'message': 'Registration failed: ${e.toString()}',
          'details': e.toString()
        }
      };
    }
  }

  Future<Map<String, dynamic>> resetPassword(String email) async {
    Logger.api('Attempting password reset for email: $email',
        tag: 'ApiService.resetPassword');
    try {
      final response =
          await _dio.post(ApiConstants.passwordReset, data: {'email': email});
      Logger.api('Password reset response status: ${response.statusCode}',
          tag: 'ApiService.resetPassword');
      if (response.statusCode == 200) {
        Logger.api('Password reset email sent successfully.',
            tag: 'ApiService.resetPassword');
        return {
          'success': true,
          'message': response.data?['detail'] ?? 'Password reset email sent',
          'data': response.data
        };
      } else {
        Logger.api('Password reset failed (Non-200): ${response.statusCode}',
            tag: 'ApiService.resetPassword');
        throw DioException(
            requestOptions: response.requestOptions, response: response);
      }
    } catch (e) {
      String message = 'Password reset failed.';
      if (e is DioException &&
          e.response?.data is Map &&
          e.response!.data['detail'] != null) {
        message = e.response!.data['detail'];
      } else if (e is DioException &&
          e.response?.data is Map &&
          e.response!.data['email'] is List) {
        message = e.response!.data['email'].join(' ');
      }
      Logger.api('Password reset exception: $message',
          tag: 'ApiService.resetPassword', error: e);
      return {'success': false, 'message': message, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> changePassword(
      String oldPassword, String newPassword) async {
    Logger.api('Attempting password change.', tag: 'ApiService.changePassword');
    try {
      final response = await _dio.post(ApiConstants.passwordChange, data: {
        'old_password': oldPassword,
        'new_password': newPassword,
        'confirm_password': newPassword
      });
      Logger.api('Password change response status: ${response.statusCode}',
          tag: 'ApiService.changePassword');
      if (response.statusCode == 200) {
        Logger.api('Password changed successfully.',
            tag: 'ApiService.changePassword');
        return {
          'success': true,
          'message':
              response.data?['detail'] ?? 'Password changed successfully',
          'data': response.data
        };
      } else {
        Logger.api('Password change failed (Non-200): ${response.statusCode}',
            tag: 'ApiService.changePassword');
        throw DioException(
            requestOptions: response.requestOptions, response: response);
      }
    } catch (e) {
      String message = 'Password change failed.';
      if (e is DioException && e.response?.data is Map) {
        final errors = e.response!.data as Map<String, dynamic>;
        if (errors['detail'] != null) {
          message = errors['detail'];
        } else {
          message = errors.entries
              .map((entry) =>
                  '${entry.key}: ${entry.value is List ? entry.value.join(', ') : entry.value}')
              .join('; ');
        }
      }
      Logger.api('Password change exception: $message',
          tag: 'ApiService.changePassword', error: e);
      return {'success': false, 'message': message, 'error': e.toString()};
    }
  }

  // --- USER PROFILE & PLANS ---
  Future<UserProfile> getUserProfile({bool skipCache = false}) async {
    Logger.api('Attempting to get user profile (${ApiConstants.userProfile}).',
        tag: 'ApiService.getUserProfile');
    try {
      final response = await get(ApiConstants.userProfile);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('User profile fetched successfully.',
            tag: 'ApiService.getUserProfile');
        return UserProfile.fromJson(response);
      } else {
        Logger.api('Failed to get user profile: Invalid response format.',
            tag: 'ApiService.getUserProfile');
        throw Exception('Failed to get user profile: Invalid response format');
      }
    } catch (e) {
      Logger.api('Failed to get user profile: $e',
          tag: 'ApiService.getUserProfile', error: e);
      throw Exception('Failed to get user profile: $e');
    }
  }

  // Update user details (first name, last name, etc.)
  Future<Map<String, dynamic>> updateUserDetails(
      Map<String, dynamic> data) async {
    Logger.api('Attempting to update user details.',
        tag: 'ApiService.updateUserDetails');
    try {
      // Try POST to the weight update endpoint as a workaround
      // This is a temporary solution until the backend supports proper user detail updates
      final response = await post(ApiConstants.userWeightUpdate, data: data);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api(
            'User details updated successfully via weight update endpoint.',
            tag: 'ApiService.updateUserDetails');
        return response;
      } else {
        Logger.api(
            'Failed to update user details: Invalid response format or status.',
            tag: 'ApiService.updateUserDetails');
        throw Exception('Failed to update user details: Invalid response');
      }
    } catch (e) {
      Logger.api('Failed to update user details: $e',
          tag: 'ApiService.updateUserDetails', error: e);
      throw Exception('Failed to update user details: $e');
    }
  }

  Future<Map<String, dynamic>> submitQuestionnaire(
      Map<String, dynamic> data) async {
    Logger.api(
        'Attempting to submit questionnaire (${ApiConstants.userQuestionnaire}).',
        tag: 'ApiService.submitQuestionnaire');
    try {
      final response = await post(ApiConstants.userQuestionnaire, data: data);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Questionnaire submitted successfully.',
            tag: 'ApiService.submitQuestionnaire');
        return response;
      } else {
        Logger.api(
            'Failed to submit questionnaire: Invalid response format or status.',
            tag: 'ApiService.submitQuestionnaire');
        throw Exception('Failed to submit questionnaire: Invalid response');
      }
    } catch (e) {
      Logger.api('Failed to submit questionnaire: $e',
          tag: 'ApiService.submitQuestionnaire', error: e);
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getUserWorkoutPlans() async {
    Logger.api(
        'Attempting to get user workout plans (${ApiConstants.userWorkoutPlans}).',
        tag: 'ApiService.getUserWorkoutPlans');
    try {
      final response = await get(ApiConstants.userWorkoutPlans);
      if (response is List) {
        Logger.api('User workout plans fetched successfully (as List).',
            tag: 'ApiService.getUserWorkoutPlans');
        return List<Map<String, dynamic>>.from(response);
      } else if (response is Map &&
          response.containsKey('results') &&
          response['results'] is List) {
        Logger.api('User workout plans fetched successfully (from results).',
            tag: 'ApiService.getUserWorkoutPlans');
        return List<Map<String, dynamic>>.from(response['results']);
      } else {
        Logger.api(
            'Invalid response format for user workout plans. Returning empty list.',
            tag: 'ApiService.getUserWorkoutPlans');
        return [];
      }
    } catch (e) {
      Logger.api('Error getting user workout plans: $e. Returning empty list.',
          tag: 'ApiService.getUserWorkoutPlans', error: e);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getUserMealPlans() async {
    Logger.api(
        'Attempting to get user meal plans (${ApiConstants.userMealPlans}).',
        tag: 'ApiService.getUserMealPlans');
    try {
      final response = await get(ApiConstants.userMealPlans);
      if (response is List) {
        Logger.api('User meal plans fetched successfully (as List).',
            tag: 'ApiService.getUserMealPlans');
        return List<Map<String, dynamic>>.from(response);
      } else if (response is Map &&
          response.containsKey('results') &&
          response['results'] is List) {
        Logger.api('User meal plans fetched successfully (from results).',
            tag: 'ApiService.getUserMealPlans');
        return List<Map<String, dynamic>>.from(response['results']);
      } else {
        Logger.api(
            'Invalid response format for user meal plans. Returning empty list.',
            tag: 'ApiService.getUserMealPlans');
        return [];
      }
    } catch (e) {
      Logger.api('Error getting user meal plans: $e. Returning empty list.',
          tag: 'ApiService.getUserMealPlans', error: e);
      return [];
    }
  }

  // --- SCHEDULES ---
  Future<List<dynamic>> getWorkoutSchedules(
      {required int userWorkoutPlanId, int? weekNumber}) async {
    Logger.api(
        'Attempting to get workout schedules for plan $userWorkoutPlanId, week $weekNumber (${ApiConstants.workoutSchedules}).',
        tag: 'ApiService.getWorkoutSchedules');
    try {
      Map<String, dynamic> queryParams = {
        'user_workout_plan': userWorkoutPlanId.toString()
      };
      if (weekNumber != null) {
        queryParams['week_number'] = weekNumber.toString();
      }
      final response = await get(ApiConstants.workoutSchedules,
          queryParameters: queryParams);

      // Handle both List and Map with 'results' key
      if (response is List) {
        Logger.api('Workout schedules fetched successfully (as List).',
            tag: 'ApiService.getWorkoutSchedules');
        return response;
      } else if (response is Map &&
          response.containsKey('results') &&
          response['results'] is List) {
        Logger.api('Workout schedules fetched successfully (from results).',
            tag: 'ApiService.getWorkoutSchedules');
        return response['results']; // Return the list inside 'results'
      } else {
        Logger.api(
            'Invalid response format for workout schedules (expected List or Map with results List). Response: $response. Returning empty list.',
            tag: 'ApiService.getWorkoutSchedules');
        return [];
      }
    } catch (e) {
      Logger.api('Error getting workout schedules: $e. Returning empty list.',
          tag: 'ApiService.getWorkoutSchedules', error: e);
      return [];
    }
  }

  Future<List<dynamic>> getMealSchedules(
      {required int userMealPlanId, int? weekNumber}) async {
    Logger.api(
        'Attempting to get meal schedules for plan $userMealPlanId, week $weekNumber (${ApiConstants.mealSchedules}).',
        tag: 'ApiService.getMealSchedules');
    try {
      Map<String, dynamic> queryParams = {
        'user_meal_plan': userMealPlanId.toString()
      };
      if (weekNumber != null) {
        queryParams['week_number'] = weekNumber.toString();
      }
      final response =
          await get(ApiConstants.mealSchedules, queryParameters: queryParams);
      if (response is List) {
        Logger.api('Meal schedules fetched successfully.',
            tag: 'ApiService.getMealSchedules');
        return response;
      } else {
        Logger.api(
            'Invalid response format for meal schedules. Returning empty list.',
            tag: 'ApiService.getMealSchedules');
        return [];
      }
    } catch (e) {
      Logger.api('Error getting meal schedules: $e. Returning empty list.',
          tag: 'ApiService.getMealSchedules', error: e);
      return [];
    }
  }

  // --- ACTIVITY LOGGING ---
  Future<Map<String, dynamic>> createOrUpdateWorkoutLog(
      Map<String, dynamic> data) async {
    final isUpdate = data.containsKey('id') && data['id'] != null;
    final logId = isUpdate ? data['id'] : 'NEW';
    Logger.api(
        '🏋️ [WORKOUT LOG] Attempting to ${isUpdate ? "update" : "create"} workout log ID: $logId (${ApiConstants.workoutLogs}) with data: $data',
        tag: 'ApiService.createOrUpdateWorkoutLog');
    try {
      // Validate required fields
      if (isUpdate) {
        // This is an update operation
        Logger.api('🏋️ [WORKOUT LOG] Updating existing workout log ID: $logId',
            tag: 'ApiService.createOrUpdateWorkoutLog');

        if (!data.containsKey('workout_day') || data['workout_day'] == null) {
          Logger.api(
              '⚠️ [WORKOUT LOG] WARNING: workout_day is missing in workout log update data for ID: $logId. This may cause backend errors.',
              tag: 'ApiService.createOrUpdateWorkoutLog');
        }
        if (!data.containsKey('date') || data['date'] == null) {
          Logger.api(
              '⚠️ [WORKOUT LOG] WARNING: date is missing in workout log update data for ID: $logId. This may cause backend errors.',
              tag: 'ApiService.createOrUpdateWorkoutLog');
        }
      } else {
        // This is a create operation
        Logger.api('🏋️ [WORKOUT LOG] Creating new workout log',
            tag: 'ApiService.createOrUpdateWorkoutLog');

        if (!data.containsKey('workout_day') || data['workout_day'] == null) {
          Logger.api(
              '⚠️ [WORKOUT LOG] WARNING: workout_day is missing in workout log create data. This may cause backend errors.',
              tag: 'ApiService.createOrUpdateWorkoutLog');
          // Instead of throwing an exception, we'll create a dummy workout log without a workout_day
          // This allows the app to continue even if the workout day doesn't exist in the backend
          // The workout player screen will handle this case
          data.remove(
              'workout_day'); // Remove the null workout_day to avoid backend errors
        }
        if (!data.containsKey('date') || data['date'] == null) {
          Logger.api(
              '❌ [WORKOUT LOG] ERROR: date is required for workout logging',
              tag: 'ApiService.createOrUpdateWorkoutLog');
          throw Exception('date is required for workout logging');
        }
      }

      Logger.api(
          '🏋️ [WORKOUT LOG] Sending ${isUpdate ? "update" : "create"} request to server for workout log ID: $logId',
          tag: 'ApiService.createOrUpdateWorkoutLog');
      final response = await post(ApiConstants.workoutLogs, data: data);

      if (response != null && response is Map<String, dynamic>) {
        final responseId = response['id'];
        Logger.api(
            '✅ [WORKOUT LOG] Workout log ${isUpdate ? "updated" : "created"} successfully. ID: $responseId, Response: $response',
            tag: 'ApiService.createOrUpdateWorkoutLog');

        // If this was a calories update, refresh calorie data
        if (isUpdate && data.containsKey('calories_burned')) {
          try {
            Logger.api(
                '🔄 [WORKOUT LOG] Refreshing calorie data after updating calories_burned',
                tag: 'ApiService.createOrUpdateWorkoutLog');
            await getCalories(skipCache: true);
          } catch (e) {
            Logger.api('⚠️ [WORKOUT LOG] Error refreshing calorie data: $e',
                tag: 'ApiService.createOrUpdateWorkoutLog', error: e);
            // Non-critical, continue
          }
        }

        return response;
      } else {
        Logger.api(
            '❌ [WORKOUT LOG] Failed to ${isUpdate ? "update" : "create"} workout log ID: $logId: Invalid response format or status.',
            tag: 'ApiService.createOrUpdateWorkoutLog');
        throw Exception(
            'Failed to ${isUpdate ? "update" : "create"} workout log: Invalid response');
      }
    } catch (e) {
      // Provide more detailed error information
      final errorMessage = e is DioException && e.response != null
          ? 'Server response: ${e.response?.statusCode} - ${e.response?.data}'
          : e.toString();

      Logger.api(
          '❌ [WORKOUT LOG] Failed to ${isUpdate ? "update" : "create"} workout log ID: $logId: $errorMessage',
          tag: 'ApiService.createOrUpdateWorkoutLog',
          error: e);
      rethrow;
    }
  }

  Future<Map<String, dynamic>> completeWorkoutLog(
      {required int workoutLogId}) async {
    final String endpoint =
        '${ApiConstants.workoutLogs}$workoutLogId/complete/';
    Logger.api(
        'Attempting to complete workout log ID: $workoutLogId via $endpoint',
        tag: 'ApiService.completeWorkoutLog');
    try {
      // Validate workout log ID
      if (workoutLogId <= 0) {
        Logger.api(
            'ERROR: Invalid workout log ID: $workoutLogId. Cannot complete workout.',
            tag: 'ApiService.completeWorkoutLog');
        throw Exception('Invalid workout log ID: $workoutLogId');
      }

      // Add cache busting to prevent caching issues
      final cacheBustingEndpoint =
          '$endpoint?_=${DateTime.now().millisecondsSinceEpoch}';

      // Log the exact endpoint being used
      Logger.api('Using endpoint with cache busting: $cacheBustingEndpoint',
          tag: 'ApiService.completeWorkoutLog');

      Logger.api('Sending POST request to complete workout log $workoutLogId',
          tag: 'ApiService.completeWorkoutLog');

      final response = await post(cacheBustingEndpoint, data: {}); // Empty body
      if (response != null && response is Map<String, dynamic>) {
        Logger.api(
            'Workout log $workoutLogId completed successfully. Response: $response',
            tag: 'ApiService.completeWorkoutLog');

        // Notify listeners that a workout was completed
        NotificationService.instance.notifyListeners('workout_completed');
        Logger.api('Notified listeners of workout completion',
            tag: 'ApiService.completeWorkoutLog');

        // Force refresh of calorie data
        try {
          Logger.api('Refreshing calorie data after workout completion',
              tag: 'ApiService.completeWorkoutLog');
          await getCalories(skipCache: true);
          Logger.api(
              'Successfully refreshed calorie data after workout completion',
              tag: 'ApiService.completeWorkoutLog');
        } catch (e) {
          Logger.api(
              'Error refreshing calorie data after workout completion: $e',
              tag: 'ApiService.completeWorkoutLog',
              error: e);
          // Non-critical, continue
        }

        return response;
      } else {
        Logger.api(
            'Failed to complete workout log $workoutLogId: Invalid response format or status. Response: $response',
            tag: 'ApiService.completeWorkoutLog');
        throw Exception(
            'Failed to complete workout log $workoutLogId: Invalid response');
      }
    } catch (e) {
      // Provide more detailed error information
      final errorMessage = e is DioException && e.response != null
          ? 'Server response: ${e.response?.statusCode} - ${e.response?.data}'
          : e.toString();

      Logger.api('Failed to complete workout log $workoutLogId: $errorMessage',
          tag: 'ApiService.completeWorkoutLog', error: e);
      rethrow;
    }
  }

  Future<Map<String, dynamic>> completeWorkoutSessionLog(
      {required int workoutSessionLogId}) async {
    final String endpoint =
        '${ApiConstants.workoutSessionLogs}$workoutSessionLogId/complete/';
    Logger.api(
        'Attempting to complete workout session log ID: $workoutSessionLogId via $endpoint',
        tag: 'ApiService.completeWorkoutSessionLog');
    try {
      // Validate workout session log ID
      if (workoutSessionLogId <= 0) {
        Logger.api(
            'ERROR: Invalid workout session log ID: $workoutSessionLogId. Cannot complete workout session.',
            tag: 'ApiService.completeWorkoutSessionLog');
        throw Exception('Invalid workout session log ID: $workoutSessionLogId');
      }

      // Add cache busting to prevent caching issues
      final cacheBustingEndpoint =
          '$endpoint?_=${DateTime.now().millisecondsSinceEpoch}';

      // Log the exact endpoint being used
      Logger.api('Using endpoint with cache busting: $cacheBustingEndpoint',
          tag: 'ApiService.completeWorkoutSessionLog');

      Logger.api(
          'Sending POST request to complete workout session log $workoutSessionLogId',
          tag: 'ApiService.completeWorkoutSessionLog');

      final response = await post(cacheBustingEndpoint, data: {}); // Empty body
      if (response != null && response is Map<String, dynamic>) {
        Logger.api(
            'Workout session log $workoutSessionLogId completed successfully. Response: $response',
            tag: 'ApiService.completeWorkoutSessionLog');

        // Notify listeners that a workout session was completed
        NotificationService.instance
            .notifyListeners('workout_session_completed');
        Logger.api('Notified listeners of workout session completion',
            tag: 'ApiService.completeWorkoutSessionLog');

        // Force refresh of calorie data
        try {
          Logger.api('Refreshing calorie data after workout session completion',
              tag: 'ApiService.completeWorkoutSessionLog');
          await getCalories(skipCache: true);
          Logger.api(
              'Successfully refreshed calorie data after workout session completion',
              tag: 'ApiService.completeWorkoutSessionLog');
        } catch (e) {
          Logger.api(
              'Error refreshing calorie data after workout session completion: $e',
              tag: 'ApiService.completeWorkoutSessionLog',
              error: e);
          // Non-critical, continue
        }

        return response;
      } else {
        Logger.api(
            'Failed to complete workout session log $workoutSessionLogId: Invalid response format or status. Response: $response',
            tag: 'ApiService.completeWorkoutSessionLog');
        throw Exception(
            'Failed to complete workout session log $workoutSessionLogId: Invalid response');
      }
    } catch (e) {
      // Provide more detailed error information
      final errorMessage = e is DioException && e.response != null
          ? 'Server response: ${e.response?.statusCode} - ${e.response?.data}'
          : e.toString();

      Logger.api(
          'Failed to complete workout session log $workoutSessionLogId: $errorMessage',
          tag: 'ApiService.completeWorkoutSessionLog',
          error: e);
      rethrow;
    }
  }

  // Direct complete a workout session without requiring a session log ID
  Future<Map<String, dynamic>> directCompleteWorkoutSession(
      {required Map<String, dynamic> data}) async {
    // Use the endpoint for the direct-complete action
    const String endpoint = ApiConstants.workoutSessionDirectComplete;
    Logger.api('Attempting to directly complete workout session via $endpoint',
        tag: 'ApiService.directCompleteWorkoutSession');
    try {
      // Add cache busting to prevent caching issues
      final cacheBustingEndpoint =
          '$endpoint?_=${DateTime.now().millisecondsSinceEpoch}';

      // Log the exact endpoint being used
      Logger.api('Using endpoint with cache busting: $cacheBustingEndpoint',
          tag: 'ApiService.directCompleteWorkoutSession');

      // Validate required fields
      if (!data.containsKey('workout_day_id') ||
          data['workout_day_id'] == null) {
        Logger.api(
            'ERROR: workout_day_id is required for direct workout completion',
            tag: 'ApiService.directCompleteWorkoutSession');
        throw Exception(
            'workout_day_id is required for direct workout completion');
      }

      if (!data.containsKey('date') || data['date'] == null) {
        Logger.api('ERROR: date is required for direct workout completion',
            tag: 'ApiService.directCompleteWorkoutSession');
        throw Exception('date is required for direct workout completion');
      }

      // Log detailed request information
      Logger.api(
          'DEBUG: Sending POST request to directly complete workout session with data: $data',
          tag: 'ApiService.directCompleteWorkoutSession');
      Logger.api(
          'DEBUG: workout_day_id=${data['workout_day_id']}, workout_session_id=${data['workout_session_id']}',
          tag: 'ApiService.directCompleteWorkoutSession');
      Logger.api(
          'DEBUG: date=${data['date']}, duration_minutes=${data['duration_minutes']}, calories_burned=${data['calories_burned']}',
          tag: 'ApiService.directCompleteWorkoutSession');

      // Use the standard post method
      Logger.api(
          'Sending POST request to $cacheBustingEndpoint with data: $data',
          tag: 'ApiService.directCompleteWorkoutSession');

      // Use the post method instead of directly using _dio.post
      final response = await post(cacheBustingEndpoint, data: data);
      Logger.api('DEBUG: Direct complete response: $response',
          tag: 'ApiService.directCompleteWorkoutSession');
      if (response is Map<String, dynamic>) {
        Logger.api(
            'Workout session directly completed successfully. Response: $response',
            tag: 'ApiService.directCompleteWorkoutSession');

        // Notify listeners that a workout was completed with more detailed data
        final notificationData = {
          'workout_log_id': response['id'] ?? 0,
          'workout_day_id': data['workout_day_id'] ?? 0,
          'workout_session_id': data['workout_session_id'] ?? 0,
          'date': data['date'] ?? '',
          'is_completed': true,
          'calories_burned': data['calories_burned'] ?? 0,
          'duration_minutes': data['duration_minutes'] ?? 0,
        };
        Logger.api(
            'Sending workout_completed notification with data: $notificationData',
            tag: 'ApiService.directCompleteWorkoutSession');
        NotificationService.instance
            .notifyListeners('workout_completed', notificationData);
        Logger.api(
            'Notified listeners of workout completion with data: $notificationData',
            tag: 'ApiService.directCompleteWorkoutSession');

        // Also notify workout_log_updated to ensure UI is refreshed
        NotificationService.instance
            .notifyListeners('workout_log_updated', notificationData);
        Logger.api('Notified listeners of workout log update',
            tag: 'ApiService.directCompleteWorkoutSession');

        // Force refresh of calorie data
        try {
          Logger.api('Refreshing calorie data after workout completion',
              tag: 'ApiService.directCompleteWorkoutSession');
          await getCalories(skipCache: true);
          Logger.api(
              'Successfully refreshed calorie data after workout completion',
              tag: 'ApiService.directCompleteWorkoutSession');
        } catch (e) {
          Logger.api(
              'Error refreshing calorie data after workout completion: $e',
              tag: 'ApiService.directCompleteWorkoutSession',
              error: e);
          // Non-critical, continue
        }

        return response;
      } else {
        Logger.api(
            'Failed to directly complete workout session: Invalid response format or status. Response: $response',
            tag: 'ApiService.directCompleteWorkoutSession');
        throw Exception(
            'Failed to directly complete workout session: Invalid response');
      }
    } catch (e) {
      // Provide more detailed error information
      if (e is DioException) {
        if (e.response != null) {
          final statusCode = e.response?.statusCode;
          final responseData = e.response?.data;

          Logger.api(
              'DEBUG: DioException with response - Status: $statusCode, Data: $responseData',
              tag: 'ApiService.directCompleteWorkoutSession');

          // Check for specific status codes
          if (statusCode == 404) {
            Logger.api(
                'DEBUG: 404 Not Found - The endpoint might not exist or is incorrectly configured',
                tag: 'ApiService.directCompleteWorkoutSession');
          } else if (statusCode == 400) {
            Logger.api(
                'DEBUG: 400 Bad Request - The server rejected the request due to invalid data',
                tag: 'ApiService.directCompleteWorkoutSession');
          } else if (statusCode == 401) {
            Logger.api('DEBUG: 401 Unauthorized - Authentication issue',
                tag: 'ApiService.directCompleteWorkoutSession');
          } else if (statusCode == 500) {
            Logger.api(
                'DEBUG: 500 Server Error - The server encountered an error processing the request',
                tag: 'ApiService.directCompleteWorkoutSession');
          }

          final errorMessage = 'Server response: $statusCode - $responseData';
          Logger.api(
              'Failed to directly complete workout session: $errorMessage',
              tag: 'ApiService.directCompleteWorkoutSession',
              error: e);
        } else {
          // No response from server (network issue)
          Logger.api(
              'DEBUG: DioException with no response - Likely a network connectivity issue',
              tag: 'ApiService.directCompleteWorkoutSession');
          Logger.api(
              'Failed to directly complete workout session: Network error - ${e.message}',
              tag: 'ApiService.directCompleteWorkoutSession',
              error: e);
        }
      } else {
        // Not a DioException
        Logger.api('DEBUG: Non-DioException error: ${e.toString()}',
            tag: 'ApiService.directCompleteWorkoutSession');
        Logger.api(
            'Failed to directly complete workout session: ${e.toString()}',
            tag: 'ApiService.directCompleteWorkoutSession',
            error: e);
      }
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createOrUpdateMealLog(
      Map<String, dynamic> data) async {
    Logger.api('Attempting to create/update meal log with data: $data',
        tag: 'ApiService.createOrUpdateMealLog');
    try {
      final response = await post(ApiConstants.mealLogs, data: data);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Meal log created/updated successfully.',
            tag: 'ApiService.createOrUpdateMealLog');
        return response;
      } else {
        Logger.api(
            'Failed to create/update meal log: Invalid response format or status.',
            tag: 'ApiService.createOrUpdateMealLog');
        throw Exception('Failed to create/update meal log: Invalid response');
      }
    } catch (e) {
      Logger.api('Failed to create/update meal log: $e',
          tag: 'ApiService.createOrUpdateMealLog', error: e);
      rethrow;
    }
  }

  Future<Map<String, dynamic>> completeMealLog({required int mealLogId}) async {
    Logger.api('🍽️ [MEAL LOG] Attempting to complete meal log ID: $mealLogId',
        tag: 'ApiService.completeMealLog');

    // Validate meal log ID
    if (mealLogId <= 0) {
      Logger.api(
          '❌ [MEAL LOG] Invalid meal log ID: $mealLogId. Cannot complete meal.',
          tag: 'ApiService.completeMealLog');
      throw Exception('Invalid meal log ID: $mealLogId');
    }

    // Clear the calorie data cache to force a refresh on next request
    _calorieData = null;
    Logger.api('🔄 [MEAL LOG] Cleared calorie data cache to force refresh',
        tag: 'ApiService.completeMealLog');

    // Normal API flow for real meal logs
    final String endpoint = '${ApiConstants.mealLogs}$mealLogId/complete/';
    try {
      // Add cache busting to the POST request
      final cacheBustingEndpoint =
          '$endpoint?_=${DateTime.now().millisecondsSinceEpoch}';

      Logger.api(
          '🍽️ [MEAL LOG] Sending POST request to complete meal log $mealLogId via $cacheBustingEndpoint',
          tag: 'ApiService.completeMealLog');

      final response = await post(cacheBustingEndpoint, data: {}); // Empty body
      if (response != null && response is Map<String, dynamic>) {
        Logger.api(
            '✅ [MEAL LOG] Meal log $mealLogId completed successfully. Response: $response',
            tag: 'ApiService.completeMealLog');

        // Notify listeners that a meal was completed
        NotificationService.instance.notifyListeners('meal_completed');
        Logger.api('📢 [MEAL LOG] Notified listeners of meal completion',
            tag: 'ApiService.completeMealLog');

        // Force refresh of calorie data
        try {
          Logger.api(
              '🔄 [MEAL LOG] Refreshing calorie data after meal completion',
              tag: 'ApiService.completeMealLog');
          // Fetch fresh data, skipping the cache
          final calorieData = await getCalories(skipCache: true);
          Logger.api(
              '✅ [MEAL LOG] Successfully refreshed calorie data after meal completion: $calorieData',
              tag: 'ApiService.completeMealLog');
        } catch (e) {
          Logger.api(
              '⚠️ [MEAL LOG] Error refreshing calorie data after meal completion: $e',
              tag: 'ApiService.completeMealLog',
              error: e);
        }

        // Fetch updated user progress to ensure day numbers are correct
        try {
          Logger.api(
              '🔄 [MEAL LOG] Refreshing user progress after meal completion',
              tag: 'ApiService.completeMealLog');
          final userProgress = await getUserProgress();
          Logger.api(
              '✅ [MEAL LOG] Successfully refreshed user progress. Current meal day: ${userProgress.mealDay}',
              tag: 'ApiService.completeMealLog');
        } catch (e) {
          Logger.api(
              '⚠️ [MEAL LOG] Error refreshing user progress after meal completion: $e',
              tag: 'ApiService.completeMealLog',
              error: e);
        }

        return response;
      } else {
        Logger.api(
            '❌ [MEAL LOG] Failed to complete meal log $mealLogId: Invalid response format or status. Response: $response',
            tag: 'ApiService.completeMealLog');
        throw Exception(
            'Failed to complete meal log $mealLogId: Invalid response');
      }
    } catch (e) {
      // Provide more detailed error information
      final errorMessage = e is DioException && e.response != null
          ? 'Server response: ${e.response?.statusCode} - ${e.response?.data}'
          : e.toString();

      Logger.api(
          '❌ [MEAL LOG] Failed to complete meal log $mealLogId: $errorMessage',
          tag: 'ApiService.completeMealLog',
          error: e);
      rethrow;
    }
  }

  Future<Map<String, dynamic>> updateHydrationLog(
      {required String date, required int amountMl}) async {
    Logger.api('Attempting to update hydration log for $date to $amountMl ml.',
        tag: 'ApiService.updateHydrationLog');
    try {
      final response = await post(ApiConstants.hydrationLogs,
          data: {'date': date, 'amount_ml': amountMl});
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Hydration log updated successfully for $date.',
            tag: 'ApiService.updateHydrationLog');
        return response;
      } else {
        Logger.api(
            'Failed to update hydration log for $date: Invalid response format or status.',
            tag: 'ApiService.updateHydrationLog');
        throw Exception('Failed to update hydration log for $date');
      }
    } catch (e) {
      Logger.api('Failed to update hydration log for $date: $e',
          tag: 'ApiService.updateHydrationLog', error: e);
      rethrow;
    }
  }

  // Log hydration (POST request to create/update)
  Future<Map<String, dynamic>> logHydration({required int amountMl}) async {
    final String dateStr = DateTime.now().toIso8601String().split('T')[0];
    Logger.api(
        'Attempting to log hydration for $dateStr: $amountMl ml (${ApiConstants.hydrationLogs}).',
        tag: 'ApiService.logHydration');
    try {
      final response = await post(ApiConstants.hydrationLogs,
          data: {'date': dateStr, 'amount_ml': amountMl});
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Hydration logged successfully for $dateStr.',
            tag: 'ApiService.logHydration');
        return response;
      } else {
        Logger.api(
            'Failed to log hydration for $dateStr: Invalid response format or status.',
            tag: 'ApiService.logHydration');
        throw Exception('Failed to log hydration for $dateStr');
      }
    } catch (e) {
      Logger.api('Failed to log hydration for $dateStr: $e',
          tag: 'ApiService.logHydration', error: e);
      rethrow;
    }
  }

  // --- PROGRESS & SCORE ---
  Future<UserProgress> getUserProgress() async {
    Logger.api(
        'Attempting to get user progress (${ApiConstants.userProgress}).',
        tag: 'ApiService.getUserProgress');
    try {
      final response = await get(ApiConstants.userProgress);
      if (response != null && response is Map<String, dynamic>) {
        if (response.isEmpty) {
          Logger.api('User progress data not found or empty.',
              tag: 'ApiService.getUserProgress');
          throw Exception('User progress data not found or empty.');
        }
        Logger.api('User progress fetched successfully.',
            tag: 'ApiService.getUserProgress');
        return UserProgress.fromJson(response);
      } else {
        Logger.api('Invalid response format for user progress.',
            tag: 'ApiService.getUserProgress');
        throw Exception('Failed to get user progress: Invalid response format');
      }
    } catch (e) {
      Logger.api('Failed to get user progress: $e',
          tag: 'ApiService.getUserProgress', error: e);
      throw Exception('Failed to get user progress: $e');
    }
  }

  Future<UserScore> getUserScore({bool skipCache = false}) async {
    Logger.api('Attempting to get user score (${ApiConstants.userScore}).',
        tag: 'ApiService.getUserScore');
    try {
      final response = await get(ApiConstants.userScore);
      if (response != null && response is Map<String, dynamic>) {
        if (response.isEmpty) {
          Logger.api(
              'User score response is empty (likely 404 or no data). Returning default score.',
              tag: 'ApiService.getUserScore');
          return UserScore(id: 0, user: 0, userEmail: '', username: '');
        }
        Logger.api('User score fetched successfully.',
            tag: 'ApiService.getUserScore');
        return UserScore.fromJson(response);
      } else {
        Logger.api(
            'Invalid response format for user score. Returning default score.',
            tag: 'ApiService.getUserScore');
        return UserScore(id: 0, user: 0, userEmail: '', username: '');
      }
    } catch (e) {
      Logger.api('Error getting user score: $e. Returning default score.',
          tag: 'ApiService.getUserScore', error: e);
      return UserScore(id: 0, user: 0, userEmail: '', username: '');
    }
  }

  Future<Map<String, dynamic>> getUserScoreBreakdown() async {
    Logger.api(
        'Attempting to get score breakdown (${ApiConstants.userScoreBreakdown}).',
        tag: 'ApiService.getUserScoreBreakdown');
    try {
      final response = await get(ApiConstants.userScoreBreakdown);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Score breakdown fetched successfully.',
            tag: 'ApiService.getUserScoreBreakdown');
        return response;
      } else {
        Logger.api('Invalid response for score breakdown. Returning empty map.',
            tag: 'ApiService.getUserScoreBreakdown');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting score breakdown: $e. Returning empty map.',
          tag: 'ApiService.getUserScoreBreakdown', error: e);
      return {};
    }
  }

  // --- METHODS FOR COMMENTED-OUT CONSTANTS (Returning defaults/errors) ---

  // Update user weight
  Future<Map<String, dynamic>> updateUserWeight(double weight) async {
    Logger.api(
        'Attempting to update user weight to $weight via ${ApiConstants.userWeightUpdate}.',
        tag: 'ApiService.updateUserWeight');
    try {
      // Use the post helper method
      final response =
          await post(ApiConstants.userWeightUpdate, data: {'weight': weight});
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('User weight updated successfully.',
            tag: 'ApiService.updateUserWeight');
        return response; // Return the full response data
      } else {
        Logger.api('Failed to update user weight: Invalid response format.',
            tag: 'ApiService.updateUserWeight');
        throw Exception('Failed to update user weight: Invalid response');
      }
    } catch (e) {
      Logger.api('Failed to update user weight: $e',
          tag: 'ApiService.updateUserWeight', error: e);
      rethrow;
    }
  }

  // --- Helper Functions ---

  // --- Refactored Methods based on API Documentation ---

  // Get today's workout based on the device's local date
  Future<TodaysWorkoutResponse> getTodayWorkout() async {
    // Get the current local date
    final now = DateTime.now();
    final localDate = DateTime(now.year, now.month, now.day);
    final dateStr = _formatDate(localDate);

    Logger.api(
        'Attempting to get workout for local date: $dateStr using date endpoint',
        tag: 'ApiService.getTodayWorkout');

    try {
      // Use the date-based endpoint instead of today endpoint
      const endpoint = ApiConstants.workoutForDate;
      final response = await get(
        endpoint,
        queryParameters: {'date': dateStr},
      );

      // Add debug logging to see the response structure
      Logger.api('Response type: ${response.runtimeType}, Response: $response',
          tag: 'ApiService.getTodayWorkout');

      if (response != null && response is Map<String, dynamic>) {
        try {
          // Try to parse the response as a TodaysWorkoutResponse
          return TodaysWorkoutResponse.fromJson(response);
        } catch (e) {
          Logger.error('Error parsing today\'s workout response: $e',
              tag: 'ApiService');

          // Try to extract basic information from the response
          String date = response['date'] ?? _formatDate(now);
          bool isRestDay = response['is_rest_day'] ?? false;
          String? message = response['message'] ??
              'Error parsing workout data: ${e.toString()}';

          // Return a default response with the error message
          return TodaysWorkoutResponse(
            date: date,
            workoutLogs: [],
            isRestDay: isRestDay,
            message: message,
          );
        }
      } else {
        // Handle non-Map responses
        if (response == null) {
          return TodaysWorkoutResponse(
            date: _formatDate(now),
            workoutLogs: [],
            isRestDay: true,
            message: 'No workout found for today (null response)',
          );
        } else {
          // Try to convert the response to a string for debugging
          final responseStr = response.toString();
          return TodaysWorkoutResponse(
            date: _formatDate(now),
            workoutLogs: [],
            isRestDay: true,
            message:
                'No workout found for today (non-map response: $responseStr)',
          );
        }
      }
    } catch (e) {
      Logger.error('Error getting workout for today: $e', tag: 'ApiService');
      return TodaysWorkoutResponse(
        date: _formatDate(now),
        workoutLogs: [],
        isRestDay: true,
        message: 'Error: ${e.toString()}',
      );
    }
  }

  // Get today's meals based on the device's local date
  Future<TodaysMealResponse> getTodayMeals(
      {bool updateUserProgress = true}) async {
    // Get the current local date
    final now = DateTime.now();
    final localDate = DateTime(now.year, now.month, now.day);
    final dateStr = _formatDate(localDate);

    Logger.api(
        'Attempting to get meals for local date: $dateStr using date endpoint',
        tag: 'ApiService.getTodayMeals');

    try {
      // Use the date-based endpoint instead of today endpoint
      const endpoint = ApiConstants.mealsForDate;
      final response = await get(
        endpoint,
        queryParameters: {'date': dateStr},
      );

      // Add debug logging to see the response structure
      Logger.api('Response type: ${response.runtimeType}, Response: $response',
          tag: 'ApiService.getTodayMeals');

      if (response != null && response is Map<String, dynamic>) {
        try {
          Logger.tempLog('PARSING_TODAYS_MEAL_RESPONSE: ${response.keys}',
              tag: 'ApiService');

          // Transform the response to match the expected structure
          if (response.containsKey('meal_logs')) {
            final mealLogsData = response['meal_logs'] as List<dynamic>?;
            Logger.tempLog(
                'MEAL_LOGS_DATA_LENGTH: ${mealLogsData?.length ?? 0}',
                tag: 'ApiService');

            // Transform each meal log to match the expected structure
            if (mealLogsData != null) {
              for (var mealLogData in mealLogsData) {
                if (mealLogData is Map<String, dynamic>) {
                  // Move meal_details to meal
                  if (mealLogData.containsKey('meal_details')) {
                    mealLogData['meal'] = mealLogData['meal_details'];
                    mealLogData.remove('meal_details');
                  }
                  // Set id from meal_log_id or default to 0
                  if (mealLogData.containsKey('meal_log_id')) {
                    mealLogData['id'] = mealLogData['meal_log_id'] ?? 0;
                  } else {
                    mealLogData['id'] = 0;
                  }
                }
              }
            }
          }

          return TodaysMealResponse.fromJson(response);
        } catch (e) {
          Logger.error('Error parsing today\'s meal response: $e',
              tag: 'ApiService');

          // Try to extract basic information from the response
          String date = response['date'] ?? _formatDate(now);
          int dayNumber = response['day_number'] ?? 0;
          bool noPlanAssigned = response['no_plan_assigned'] ?? false;
          bool isFutureDay = response['is_future_day'] ?? false;
          String? message =
              response['message'] ?? 'Error parsing meal data: ${e.toString()}';

          // Extract hydration data if available
          Hydration hydration = _extractHydrationFromResponse(response);

          // Return a default response with the error message
          return TodaysMealResponse(
            date: date,
            mealLogs: [],
            hydration: hydration,
            message: message,
            dayNumber: dayNumber,
            noPlanAssigned: noPlanAssigned,
            isFutureDay: isFutureDay,
          );
        }
      } else {
        // Handle non-Map responses
        if (response == null) {
          return TodaysMealResponse(
            date: _formatDate(now),
            mealLogs: [],
            hydration: _extractHydrationFromResponse({}),
            message: 'No meals found for today (null response)',
            dayNumber: 0,
            noPlanAssigned: false,
            isFutureDay: false,
          );
        } else {
          // Try to convert the response to a string for debugging
          final responseStr = response.toString();
          return TodaysMealResponse(
            date: _formatDate(now),
            mealLogs: [],
            hydration: _extractHydrationFromResponse({}),
            message:
                'No meals found for today (non-map response: $responseStr)',
            dayNumber: 0,
            noPlanAssigned: false,
            isFutureDay: false,
          );
        }
      }
    } catch (e) {
      Logger.error('Error getting meals for today: $e', tag: 'ApiService');
      return TodaysMealResponse(
        date: _formatDate(now),
        mealLogs: [],
        hydration: _extractHydrationFromResponse({}),
        message: 'Error: ${e.toString()}',
        dayNumber: 0,
        noPlanAssigned: false,
        isFutureDay: false,
      );
    }
  }

  // Helper to fetch hydration data from response
  Hydration _extractHydrationFromResponse(Map<String, dynamic> response) {
    try {
      if (response['hydration'] != null &&
          response['hydration'] is Map<String, dynamic>) {
        Map<String, dynamic> hydrationData = response['hydration'];
        return Hydration(
          amountMl: hydrationData['amount_ml'] ?? 0,
          targetMl: hydrationData['target_ml'] ?? 2500,
        );
      }
    } catch (e) {
      Logger.error('Error extracting hydration data: $e', tag: 'ApiService');
    }
    return Hydration(amountMl: 0, targetMl: 2500); // Default target
  }

  // Get weekly meal schedule
  Future<List<dynamic>> getWeeklyMeals() async {
    Logger.api('Attempting to get weekly meals via ${ApiConstants.weekMeals}.',
        tag: 'ApiService.getWeeklyMeals');
    try {
      final response = await get(ApiConstants.weekMeals);
      if (response is List) {
        Logger.api('Weekly meals fetched successfully.',
            tag: 'ApiService.getWeeklyMeals');
        return response;
      } else {
        Logger.api(
            'Invalid response format for weekly meals. Returning empty list.',
            tag: 'ApiService.getWeeklyMeals');
        return [];
      }
    } catch (e) {
      Logger.api('Error getting weekly meals: $e. Returning empty list.',
          tag: 'ApiService.getWeeklyMeals', error: e);
      return [];
    }
  }

  // Get nutrition progress
  Future<Map<String, dynamic>> getNutritionProgress({int days = 7}) async {
    Logger.api(
        'Attempting to get nutrition progress (last $days days) via ${ApiConstants.nutritionProgress}.',
        tag: 'ApiService.getNutritionProgress');
    try {
      final response = await get(ApiConstants.nutritionProgress,
          queryParameters: {'days': days.toString()});
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Nutrition progress fetched successfully.',
            tag: 'ApiService.getNutritionProgress');
        return response;
      } else {
        Logger.api(
            'Invalid response format for nutrition progress. Returning empty map.',
            tag: 'ApiService.getNutritionProgress');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting nutrition progress: $e. Returning empty map.',
          tag: 'ApiService.getNutritionProgress', error: e);
      return {};
    }
  }

  // Get score history
  Future<Map<String, dynamic>> getUserScoreHistory() async {
    Logger.api(
        'Attempting to get score history via ${ApiConstants.userScoreHistory}.',
        tag: 'ApiService.getUserScoreHistory');
    try {
      final response = await get(ApiConstants.userScoreHistory);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Score history fetched successfully.',
            tag: 'ApiService.getUserScoreHistory');
        return response;
      } else {
        Logger.api(
            'Invalid response format for score history. Returning empty map.',
            tag: 'ApiService.getUserScoreHistory');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting score history: $e. Returning empty map.',
          tag: 'ApiService.getUserScoreHistory', error: e);
      return {};
    }
  }

  // Get meal substitutions
  Future<Map<String, dynamic>> getMealSubstitutions() async {
    Logger.api(
        'Attempting to get meal substitutions via ${ApiConstants.mealSubstitutions}.',
        tag: 'ApiService.getMealSubstitutions');
    try {
      final response = await get(ApiConstants.mealSubstitutions);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Meal substitutions fetched successfully.',
            tag: 'ApiService.getMealSubstitutions');
        return response;
      } else {
        Logger.api(
            'Invalid response format for meal substitutions. Returning empty map.',
            tag: 'ApiService.getMealSubstitutions');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting meal substitutions: $e. Returning empty map.',
          tag: 'ApiService.getMealSubstitutions', error: e);
      return {};
    }
  }

  // Get meal nutrition
  Future<Map<String, dynamic>> getMealNutrition() async {
    Logger.api(
        'Attempting to get meal nutrition via ${ApiConstants.mealNutrition}.',
        tag: 'ApiService.getMealNutrition');
    try {
      final response = await get(ApiConstants.mealNutrition);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Meal nutrition fetched successfully.',
            tag: 'ApiService.getMealNutrition');
        return response;
      } else {
        Logger.api(
            'Invalid response format for meal nutrition. Returning empty map.',
            tag: 'ApiService.getMealNutrition');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting meal nutrition: $e. Returning empty map.',
          tag: 'ApiService.getMealNutrition', error: e);
      return {};
    }
  }

  // Get meal preferences
  Future<Map<String, dynamic>> getMealPreferences() async {
    Logger.api(
        'Attempting to get meal preferences via ${ApiConstants.mealPreferences}.',
        tag: 'ApiService.getMealPreferences');
    try {
      final response = await get(ApiConstants.mealPreferences);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Meal preferences fetched successfully.',
            tag: 'ApiService.getMealPreferences');
        return response;
      } else {
        Logger.api(
            'Invalid response format for meal preferences. Returning empty map.',
            tag: 'ApiService.getMealPreferences');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting meal preferences: $e. Returning empty map.',
          tag: 'ApiService.getMealPreferences', error: e);
      return {};
    }
  }

  // Get progress weight
  Future<Map<String, dynamic>> getProgressWeight() async {
    Logger.api(
        'Attempting to get progress weight via ${ApiConstants.progressWeight}.',
        tag: 'ApiService.getProgressWeight');
    try {
      final response = await get(ApiConstants.progressWeight);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Progress weight fetched successfully.',
            tag: 'ApiService.getProgressWeight');
        return response;
      } else {
        Logger.api(
            'Invalid response format for progress weight. Returning empty map.',
            tag: 'ApiService.getProgressWeight');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting progress weight: $e. Returning empty map.',
          tag: 'ApiService.getProgressWeight', error: e);
      return {};
    }
  }

  // Get progress measurements
  Future<Map<String, dynamic>> getProgressMeasurements() async {
    Logger.api(
        'Attempting to get progress measurements via ${ApiConstants.progressMeasurements}.',
        tag: 'ApiService.getProgressMeasurements');
    try {
      final response = await get(ApiConstants.progressMeasurements);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Progress measurements fetched successfully.',
            tag: 'ApiService.getProgressMeasurements');
        return response;
      } else {
        Logger.api(
            'Invalid response format for progress measurements. Returning empty map.',
            tag: 'ApiService.getProgressMeasurements');
        return {};
      }
    } catch (e) {
      Logger.api(
          'Error getting progress measurements: $e. Returning empty map.',
          tag: 'ApiService.getProgressMeasurements',
          error: e);
      return {};
    }
  }

  // Get notifications
  Future<Map<String, dynamic>> getNotifications() async {
    Logger.api(
        'Attempting to get notifications via ${ApiConstants.notifications}.',
        tag: 'ApiService.getNotifications');
    try {
      final response = await get(ApiConstants.notifications);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Notifications fetched successfully.',
            tag: 'ApiService.getNotifications');
        return response;
      } else {
        Logger.api(
            'Invalid response format for notifications. Returning empty map.',
            tag: 'ApiService.getNotifications');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting notifications: $e. Returning empty map.',
          tag: 'ApiService.getNotifications', error: e);
      return {};
    }
  }

  // Get notification settings
  Future<Map<String, dynamic>> getNotificationSettings() async {
    Logger.api(
        'Attempting to get notification settings via ${ApiConstants.notificationSettings}.',
        tag: 'ApiService.getNotificationSettings');
    try {
      final response = await get(ApiConstants.notificationSettings);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Notification settings fetched successfully.',
            tag: 'ApiService.getNotificationSettings');
        return response;
      } else {
        Logger.api(
            'Invalid response format for notification settings. Returning empty map.',
            tag: 'ApiService.getNotificationSettings');
        return {};
      }
    } catch (e) {
      Logger.api(
          'Error getting notification settings: $e. Returning empty map.',
          tag: 'ApiService.getNotificationSettings',
          error: e);
      return {};
    }
  }

  // Mark notifications as read
  Future<Map<String, dynamic>> markNotificationsAsRead(
      List<String> notificationIds) async {
    Logger.api(
        'Attempting to mark notifications as read via ${ApiConstants.notificationRead}.',
        tag: 'ApiService.markNotificationsAsRead');
    try {
      // Assuming the endpoint expects a POST with a list of IDs in the body
      final response = await post(ApiConstants.notificationRead,
          data: {'notification_ids': notificationIds});
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Notifications marked as read successfully.',
            tag: 'ApiService.markNotificationsAsRead');
        return response;
      } else {
        Logger.api(
            'Invalid response format for marking notifications read. Returning empty map.',
            tag: 'ApiService.markNotificationsAsRead');
        return {};
      }
    } catch (e) {
      Logger.api(
          'Error marking notifications as read: $e. Returning empty map.',
          tag: 'ApiService.markNotificationsAsRead',
          error: e);
      return {};
    }
  }

  // Get app settings
  Future<Map<String, dynamic>> getAppSettings() async {
    Logger.api(
        'Attempting to get app settings via ${ApiConstants.settingsApp}.',
        tag: 'ApiService.getAppSettings');
    try {
      final response = await get(ApiConstants.settingsApp);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('App settings fetched successfully.',
            tag: 'ApiService.getAppSettings');
        return response;
      } else {
        Logger.api(
            'Invalid response format for app settings. Returning empty map.',
            tag: 'ApiService.getAppSettings');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting app settings: $e. Returning empty map.',
          tag: 'ApiService.getAppSettings', error: e);
      return {};
    }
  }

  // Get privacy settings
  Future<Map<String, dynamic>> getPrivacySettings() async {
    Logger.api(
        'Attempting to get privacy settings via ${ApiConstants.settingsPrivacy}.',
        tag: 'ApiService.getPrivacySettings');
    try {
      final response = await get(ApiConstants.settingsPrivacy);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Privacy settings fetched successfully.',
            tag: 'ApiService.getPrivacySettings');
        return response;
      } else {
        Logger.api(
            'Invalid response format for privacy settings. Returning empty map.',
            tag: 'ApiService.getPrivacySettings');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting privacy settings: $e. Returning empty map.',
          tag: 'ApiService.getPrivacySettings', error: e);
      return {};
    }
  }

  // Get notification preferences
  Future<Map<String, dynamic>> getNotificationPreferences() async {
    Logger.api(
        'Attempting to get notification preferences via ${ApiConstants.settingsNotifications}.',
        tag: 'ApiService.getNotificationPreferences');
    try {
      final response = await get(ApiConstants.settingsNotifications);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Notification preferences fetched successfully.',
            tag: 'ApiService.getNotificationPreferences');
        return response;
      } else {
        Logger.api(
            'Invalid response format for notification preferences. Returning empty map.',
            tag: 'ApiService.getNotificationPreferences');
        return {};
      }
    } catch (e) {
      Logger.api(
          'Error getting notification preferences: $e. Returning empty map.',
          tag: 'ApiService.getNotificationPreferences',
          error: e);
      return {};
    }
  }

  // Get measurement units
  Future<Map<String, dynamic>> getMeasurementUnits() async {
    Logger.api(
        'Attempting to get measurement units via ${ApiConstants.settingsUnits}.',
        tag: 'ApiService.getMeasurementUnits');
    try {
      final response = await get(ApiConstants.settingsUnits);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Measurement units fetched successfully.',
            tag: 'ApiService.getMeasurementUnits');
        return response;
      } else {
        Logger.api(
            'Invalid response format for measurement units. Returning empty map.',
            tag: 'ApiService.getMeasurementUnits');
        return {};
      }
    } catch (e) {
      Logger.api('Error getting measurement units: $e. Returning empty map.',
          tag: 'ApiService.getMeasurementUnits', error: e);
      return {};
    }
  }

  // Get workout plan calendar
  Future<Map<String, dynamic>> getWorkoutPlanCalendar(String planId) async {
    // Construct the dynamic endpoint using the base path and planId
    final String endpoint = '${ApiConstants.workoutPlansBase}$planId/calendar/';
    Logger.api(
        'Attempting to get workout plan calendar for plan $planId via $endpoint.',
        tag: 'ApiService.getWorkoutPlanCalendar');
    try {
      final response = await get(endpoint);
      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Workout plan calendar fetched successfully.',
            tag: 'ApiService.getWorkoutPlanCalendar');
        return response;
      } else {
        Logger.api(
            'Invalid response format for workout plan calendar. Returning empty map.',
            tag: 'ApiService.getWorkoutPlanCalendar');
        return {};
      }
    } catch (e) {
      Logger.api(
          'Error getting workout plan calendar: $e. Returning empty map.',
          tag: 'ApiService.getWorkoutPlanCalendar',
          error: e);
      return {};
    }
  }

  // Get current workout plan - uses getUserWorkoutPlans
  Future<Map<String, dynamic>> getCurrentWorkoutPlan() async {
    Logger.api('Attempting to get current user workout plan.',
        tag: 'ApiService.getCurrentWorkoutPlan');
    try {
      final workoutPlans = await getUserWorkoutPlans();
      if (workoutPlans.isNotEmpty) {
        final activePlan = workoutPlans.firstWhere(
            (plan) => plan['is_active'] == true,
            orElse: () => workoutPlans.first);
        Logger.api('Current user workout plan found (ID: ${activePlan['id']}).',
            tag: 'ApiService.getCurrentWorkoutPlan');
        return activePlan;
      }
      Logger.api('No active user workout plan found. Returning fallback.',
          tag: 'ApiService.getCurrentWorkoutPlan');
      return _getFallbackWorkoutPlan(); // Use fallback method
    } catch (e) {
      Logger.api('Error getting current workout plan: $e. Returning fallback.',
          tag: 'ApiService.getCurrentWorkoutPlan', error: e);
      return _getFallbackWorkoutPlan(); // Use fallback method
    }
  }

  // Helper method for fallback workout plan
  Map<String, dynamic> _getFallbackWorkoutPlan() {
    return {
      'id': 'fallback-id',
      'name': 'Default Workout Plan',
      'description':
          'Please check back later for your personalized workout plan',
      'difficulty': 'Beginner',
      'duration_weeks': 4,
      'goal': 'General Fitness',
      'schedule': []
    };
  }

  // Get workout logs
  Future<List<dynamic>> getWorkoutLogs() async {
    Logger.api('Attempting to get workout logs (${ApiConstants.workoutLogs}).',
        tag: 'ApiService.getWorkoutLogs');
    try {
      final response = await get(ApiConstants.workoutLogs);
      final data = response; // Assuming response is the list or contains it
      if (data is List) {
        Logger.api('Workout logs fetched successfully (as List).',
            tag: 'ApiService.getWorkoutLogs');
        return data;
      } else if (data is Map &&
          data.containsKey('results') &&
          data['results'] is List) {
        Logger.api('Workout logs fetched successfully (from results).',
            tag: 'ApiService.getWorkoutLogs');
        return data['results'];
      } else if (data is Map &&
          data.containsKey('data') &&
          data['data'] is List) {
        Logger.api('Workout logs fetched successfully (from data).',
            tag: 'ApiService.getWorkoutLogs');
        return data['data'];
      }
      Logger.api(
          'Workout logs response data is not a list or expected map structure. Returning empty list.',
          tag: 'ApiService.getWorkoutLogs');
      return [];
    } catch (e) {
      Logger.api('Failed to get workout logs: $e',
          tag: 'ApiService.getWorkoutLogs', error: e);
      rethrow;
    }
  }

  // --- Date-Specific Fetching Methods ---

  // --- Private helper to get formatted date string ---
  String _formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  // --- Refactored Methods ---

  // Get workout for a specific date
  Future<TodaysWorkoutResponse> getWorkoutForDate(DateTime date) async {
    // Format the date as YYYY-MM-DD
    final dateStr = _formatDate(date);
    Logger.api('Attempting to get workout for date: $dateStr',
        tag: 'ApiService.getWorkoutForDate');
    try {
      // Use the date-based endpoint
      const endpoint = ApiConstants.workoutForDate;
      final response = await get(
        endpoint,
        queryParameters: {'date': dateStr},
      );

      // Add debug logging to see the response structure
      Logger.api('Response type: ${response.runtimeType}, Response: $response',
          tag: 'ApiService.getWorkoutForDate');

      if (response != null && response is Map<String, dynamic>) {
        try {
          return TodaysWorkoutResponse.fromJson(response);
        } catch (e) {
          Logger.error('Error parsing workout response: $e', tag: 'ApiService');
          // Return a default response with the error message
          return TodaysWorkoutResponse(
            date: dateStr,
            workoutLogs: [],
            isRestDay: true,
            message: 'Error parsing workout data: ${e.toString()}',
            sessions: _extractWorkoutSessionsFromResponse(response),
          );
        }
      } else {
        // Handle non-Map responses
        if (response == null) {
          return TodaysWorkoutResponse(
            date: dateStr,
            workoutLogs: [],
            isRestDay: true,
            message: 'No workout found for date $dateStr (null response)',
          );
        } else {
          // Try to convert the response to a string for debugging
          final responseStr = response.toString();
          return TodaysWorkoutResponse(
            date: dateStr,
            workoutLogs: [],
            isRestDay: true,
            message:
                'No workout found for date $dateStr (non-map response: $responseStr)',
          );
        }
      }
    } catch (e) {
      Logger.error('Error getting workout for date $dateStr: $e',
          tag: 'ApiService');
      return TodaysWorkoutResponse(
        date: dateStr,
        workoutLogs: [],
        isRestDay: true,
        message: 'Error: ${e.toString()}',
      );
    }
  }

  // Get meals and hydration for a specific date
  Future<TodaysMealResponse> getMealsForDate(DateTime date) async {
    // Format the date as YYYY-MM-DD
    final dateStr = _formatDate(date);
    Logger.api('Attempting to get meals for date: $dateStr',
        tag: 'ApiService.getMealsForDate');
    try {
      // Use the date-based endpoint
      const endpoint =
          ApiConstants.mealsForDate; // Corrected to use ApiConstants
      final response = await get(
        endpoint,
        queryParameters: {'date': dateStr},
      );

      // Add debug logging to see the response structure
      Logger.api('Response type: ${response.runtimeType}, Response: $response',
          tag: 'ApiService.getMealsForDate');

      if (response != null && response is Map<String, dynamic>) {
        try {
          // Transform the response to match the expected structure
          if (response.containsKey('meal_logs')) {
            final mealLogsData = response['meal_logs'] as List<dynamic>?;

            // Transform each meal log to match the expected structure
            if (mealLogsData != null) {
              for (var mealLogData in mealLogsData) {
                if (mealLogData is Map<String, dynamic>) {
                  // Move meal_details to meal
                  if (mealLogData.containsKey('meal_details')) {
                    mealLogData['meal'] = mealLogData['meal_details'];
                    mealLogData.remove('meal_details');
                  }
                  // Set id from meal_log_id or default to 0
                  if (mealLogData.containsKey('meal_log_id')) {
                    mealLogData['id'] = mealLogData['meal_log_id'] ?? 0;
                  } else {
                    mealLogData['id'] = 0;
                  }
                }
              }
            }
          }

          return TodaysMealResponse.fromJson(response);
        } catch (e) {
          Logger.error('Error parsing meal response: $e', tag: 'ApiService');
          // Return a default response with the error message
          return TodaysMealResponse(
            date: dateStr,
            mealLogs: [],
            hydration: _extractHydrationFromResponse(response),
            message: 'Error parsing meal data: ${e.toString()}',
            dayNumber: 0,
            noPlanAssigned: false,
            isFutureDay: false,
          );
        }
      } else {
        // Handle non-Map responses
        if (response == null) {
          return TodaysMealResponse(
            date: dateStr,
            mealLogs: [],
            hydration: _extractHydrationFromResponse({}),
            message: 'No meals found for date $dateStr (null response)',
            dayNumber: 0,
            noPlanAssigned: false,
            isFutureDay: false,
          );
        } else {
          // Try to convert the response to a string for debugging
          final responseStr = response.toString();
          return TodaysMealResponse(
            date: dateStr,
            mealLogs: [],
            hydration: _extractHydrationFromResponse({}),
            message:
                'No meals found for date $dateStr (non-map response: $responseStr)',
            dayNumber: 0,
            noPlanAssigned: false,
            isFutureDay: false,
          );
        }
      }
    } catch (e) {
      Logger.error('Error getting meals for date $dateStr: $e',
          tag: 'ApiService');
      return TodaysMealResponse(
        date: dateStr,
        mealLogs: [],
        hydration: _extractHydrationFromResponse({}),
        message: 'Error: ${e.toString()}',
        dayNumber: 0,
        isFutureDay: false,
        noPlanAssigned: false,
      );
    }
  }

  // Get workout by program day
  Future<TodaysWorkoutResponse> getWorkoutByProgramDay(int programDay,
      {String? date}) async {
    Logger.api('Attempting to get workout for program day: $programDay',
        tag: 'ApiService.getWorkoutByProgramDay');
    try {
      const endpoint =
          ApiConstants.workoutByProgramDay; // Corrected to use ApiConstants
      // Add date parameter if provided
      final queryParams = {'program_day': programDay.toString()};
      if (date != null) {
        queryParams['date'] = date;
      }

      final response = await get(
        endpoint,
        queryParameters: queryParams,
      );
      if (response != null && response is Map<String, dynamic>) {
        // Handle the response manually to avoid type errors
        // Use the provided date parameter or current date as fallback
        final dateToUse =
            date ?? DateTime.now().toIso8601String().split('T')[0];
        final isRestDay = response['is_rest_day'] as bool? ?? false;
        final message = response['message'] as String?;
        final programDayResponse = response['program_day'] as int?;

        // Check if workout_day exists and is not null
        WorkoutDay? workoutDay;
        String? workoutDayName;
        // Check if 'workout_day' exists and is not null before parsing
        if (response.containsKey('workout_day') &&
            response['workout_day'] != null) {
          workoutDay = WorkoutDay.fromJson(
              response['workout_day'] as Map<String, dynamic>);
          // Extract the workout day name from the workout day
          if (workoutDay.name.isNotEmpty) {
            workoutDayName = workoutDay.name;
            debugPrint(
                'DEBUG: Extracted workout day name from workout_day: $workoutDayName');
          } else {
            debugPrint('DEBUG: Workout day name is empty');
          }

          // Log the entire response for debugging
          debugPrint('DEBUG: Full API response: $response');
        }

        // Create workout logs if available
        List<WorkoutLog> workoutLogs = [];
        if (workoutDay != null) {
          workoutLogs.add(WorkoutLog(
            id: 0, // Placeholder ID
            workoutDay: workoutDay,
            date: dateToUse,
            isCompleted: false,
            sections: workoutDay.sections ?? [],
          ));
        }

        // Parse workout sessions if available
        List<WorkoutSession>? sessions =
            _extractWorkoutSessionsFromResponse(response);

        return TodaysWorkoutResponse(
          date: dateToUse,
          workoutLogs: workoutLogs,
          isRestDay: isRestDay,
          message: message,
          programDay: programDayResponse,
          sessions: sessions,
          workoutDayName: workoutDayName, // Add the workout day name
          workoutDay: workoutDay, // Add the workout day object
        );
      } else {
        return TodaysWorkoutResponse(
          date: date ?? DateTime.now().toIso8601String().split('T')[0],
          workoutLogs: [],
          isRestDay: true,
          message: 'No workout found for program day $programDay',
        );
      }
    } catch (e) {
      Logger.error('Error getting workout for program day $programDay: $e',
          tag: 'ApiService');
      return TodaysWorkoutResponse(
        date: date ?? DateTime.now().toIso8601String().split('T')[0],
        workoutLogs: [],
        isRestDay: true,
        message: 'Error: ${e.toString()}',
      );
    }
  }

  // Get meals by program day
  Future<TodaysMealResponse> getMealsByProgramDay(int programDay,
      {String? date}) async {
    Logger.api('Attempting to get meals for program day: $programDay',
        tag: 'ApiService.getMealsByProgramDay');
    try {
      const endpoint =
          ApiConstants.mealsByProgramDay; // Corrected to use ApiConstants
      // Add date parameter if provided
      // The backend expects 'day' parameter for meals endpoint
      final queryParams = {'day': programDay.toString()};
      if (date != null) {
        queryParams['date'] = date;
      }

      final response = await get(
        endpoint,
        queryParameters: queryParams,
      );
      if (response != null && response is Map<String, dynamic>) {
        // Handle the response manually to avoid type errors
        // Use the date from the response if available, otherwise use the provided date parameter or today's date
        final responseDate = response['date'] as String?;
        final dateToUse = responseDate ??
            date ??
            DateTime.now().toIso8601String().split('T')[0];
        final message = response['message'] as String? ??
            'Meals for program day $programDay';
        final dayNumber = response['day_number'] as int? ?? programDay;

        // Parse meal logs
        List<MealLog> mealLogs = [];
        if (response.containsKey('meal_logs') &&
            response['meal_logs'] != null) {
          final mealLogsData = response['meal_logs'] as List<dynamic>;
          Logger.tempLog(
              'PARSING_MEAL_LOGS: Found ${mealLogsData.length} meal logs',
              tag: 'ApiService');
          for (var mealLogData in mealLogsData) {
            try {
              // Extract meal details
              final mealDetails =
                  mealLogData['meal_details'] as Map<String, dynamic>;
              final mealType = mealLogData['meal_type'] as String? ?? 'UNKNOWN';
              final isCompleted = mealLogData['is_completed'] as bool? ?? false;
              final mealLogId = mealLogData['meal_log_id'] as int? ?? 0;

              // Create meal object using fromJson to trigger URL transformation
              final meal = MealDetail.fromJson(mealDetails);
              Logger.tempLog('CREATED_MEAL: ${meal.name} - ${meal.imageUrl}',
                  tag: 'ApiService');

              // Create meal log
              final mealLog = MealLog(
                id: mealLogId,
                // date: dateToUse, // Removed date field
                meal: meal,
                mealType: mealType, // Use mealType instead of mealTime
                isCompleted: isCompleted,
                isCompletable: true, // Default to true initially
              );
              Logger.tempLog('CREATED_MEAL_LOG: ${mealLog.meal.name}',
                  tag: 'ApiService');

              mealLogs.add(mealLog);
            } catch (e) {
              Logger.error('Error parsing meal log: $e', tag: 'ApiService');
            }
          }
        }

        Logger.tempLog('FINAL_MEAL_LOGS_COUNT: ${mealLogs.length}',
            tag: 'ApiService');

        // Parse hydration data
        Hydration hydration = _extractHydrationFromResponse(response);

        // Check if no plan is assigned
        final noPlanAssigned = response['no_plan_assigned'] as bool? ?? false;
        // Check if it's a future day
        final isFutureDay = response['is_future_day'] as bool? ?? false;

        return TodaysMealResponse(
          date: dateToUse,
          mealLogs: mealLogs,
          hydration: hydration,
          message: message,
          dayNumber: dayNumber,
          noPlanAssigned: noPlanAssigned,
          isFutureDay: isFutureDay,
        );
      } else {
        return TodaysMealResponse(
          date: date ?? DateTime.now().toIso8601String().split('T')[0],
          mealLogs: [],
          hydration: _extractHydrationFromResponse({}),
          message: 'No meals found for program day $programDay',
          dayNumber: programDay,
          noPlanAssigned: false,
          isFutureDay: false,
        );
      }
    } catch (e) {
      Logger.error('Error getting meals for program day $programDay: $e',
          tag: 'ApiService');
      return TodaysMealResponse(
        date: date ?? DateTime.now().toIso8601String().split('T')[0],
        mealLogs: [],
        hydration: _extractHydrationFromResponse({}),
        message: 'Error: ${e.toString()}',
        dayNumber: programDay,
        noPlanAssigned: false,
        isFutureDay: false,
      );
    }
  }

  // DEPRECATED: Use getWorkoutForDate or getWorkoutByProgramDay instead
  /*
  Future<TodaysWorkoutResponse> getTodayWorkout() async {
    // ... original implementation ...
  }
  */

  // DEPRECATED: Use getMealsForDate or getMealsByProgramDay instead
  /*
  Future<TodaysMealResponse> getTodayMeals() async {
    // ... original implementation ...
  }
  */

  // Create a workout session log
  Future<Map<String, dynamic>> createWorkoutSessionLog(
      {required int workoutSessionId, required int workoutDayId}) async {
    // Use the correct endpoint format with workoutSessionLogs constant
    const String endpoint = ApiConstants.workoutSessionLogs;
    Logger.workout(
        'Attempting to create workout session log for session ID: $workoutSessionId and day ID: $workoutDayId',
        tag: 'ApiService');
    try {
      // Validate IDs
      if (workoutSessionId <= 0 || workoutDayId <= 0) {
        Logger.error(
            'Invalid workout session ID: $workoutSessionId or workout day ID: $workoutDayId',
            tag: 'ApiService');
        throw Exception('Invalid workout session or day ID');
      }

      // Prepare data for creating a workout session log
      final data = {
        'workout_session': workoutSessionId,
        'workout_day': workoutDayId,
        'date': DateTime.now()
            .toIso8601String()
            .split('T')[0], // Current date in YYYY-MM-DD format
        'is_completed': false,
      };

      Logger.workout(
          'Sending POST request to create workout session log with data: $data',
          tag: 'ApiService');

      final response = await post(endpoint, data: data);
      if (response != null && response is Map<String, dynamic>) {
        Logger.workout(
            'Workout session log created successfully. Response: $response',
            tag: 'ApiService');
        return response;
      } else {
        Logger.error(
            'Failed to create workout session log: Invalid response format. Response: $response',
            tag: 'ApiService');
        throw Exception(
            'Failed to create workout session log: Invalid response format');
      }
    } catch (e) {
      // Provide more detailed error information
      final errorMessage = e is DioException && e.response != null
          ? 'Server response: ${e.response?.statusCode} - ${e.response?.data}'
          : e.toString();

      Logger.error('Error creating workout session log: $errorMessage',
          tag: 'ApiService');
      rethrow;
    }
  }

  // Get Calories using the documented endpoint or cached data
  Future<Map<String, dynamic>> getCalories({bool skipCache = false}) async {
    Logger.api(
        'Attempting to get user calories via ${ApiConstants.userCalories} or cache (skipCache: $skipCache).',
        tag: 'ApiService.getCalories');

    // --- Start: Structure modification ---
    // If we have cached data AND we are not skipping cache, return it directly, wrapped in the standard structure.
    if (_calorieData != null && !skipCache) {
      Logger.api('Using cached calorie data: $_calorieData',
          tag: 'ApiService.getCalories');
      return {'status': 'success', 'data': _calorieData!};
    }
    // --- End: Structure modification ---

    try {
      // Use the constant defined in api_constants.dart
      final response = await get(ApiConstants.userCalories);

      if (skipCache) {
        Logger.debug('RAW Calorie API Response (skipCache=true): $response',
            tag: 'ApiService.getCalories');
      }

      if (response != null && response is Map<String, dynamic>) {
        Logger.api('Calories fetched successfully: $response',
            tag: 'ApiService.getCalories');

        // Check if the response already has a nested 'data' structure
        Map<String, dynamic> dataToExtract = response;
        if (response.containsKey('status') &&
            response.containsKey('data') &&
            response['data'] is Map<String, dynamic>) {
          dataToExtract = response['data'];
        }

        // Extract the actual calorie data. IMPORTANT: Adjust keys if API differs.
        Map<String, dynamic> extractedData = {
          'consumed': dataToExtract['total_calories_consumed'] ??
              dataToExtract['consumed'] ??
              0,
          'burned': dataToExtract['total_calories_burned'] ??
              dataToExtract['burned'] ??
              0,
          'target': dataToExtract['daily_calories'] ??
              dataToExtract['target'] ??
              2200,
          'net': dataToExtract['net_calories'] ?? dataToExtract['net'] ?? 0,
          'remaining': dataToExtract['remaining_calories'] ??
              dataToExtract['remaining'] ??
              0,
          'weekly_average': dataToExtract['weekly_average'] ?? 0,
          'monthly_average': dataToExtract['monthly_average'] ?? 0,
        };

        // Update the cache
        _calorieData = extractedData;

        // Return the data wrapped in the standard structure
        return {'status': 'success', 'data': extractedData};
      } else {
        Logger.api(
            'Calories endpoint (${ApiConstants.userCalories}) returned null or invalid format. Returning defaults.',
            tag: 'ApiService.getCalories');
        // Return default data wrapped in the standard structure
        return {
          'status': 'success', // Or 'error'?
          'data': _getDefaultCalorieData()
        };
      }
    } on DioException catch (e) {
      String errorMsg = 'Error fetching calories';
      if (e.response?.statusCode == 404) {
        errorMsg =
            'Calories endpoint (${ApiConstants.userCalories}) returned 404.';
      }
      Logger.api('$errorMsg: $e', tag: 'ApiService.getCalories', error: e);
      // Return default data wrapped in error structure
      return {
        'status': 'error',
        'message': errorMsg,
        'data': _getDefaultCalorieData()
      };
    } catch (e) {
      Logger.api('Unexpected error fetching calories: $e',
          tag: 'ApiService.getCalories', error: e);
      // Return default data wrapped in error structure
      return {
        'status': 'error',
        'message': 'Unexpected error fetching calories',
        'data': _getDefaultCalorieData()
      };
    }
  }

  // Helper for default calorie data
  Map<String, dynamic> _getDefaultCalorieData() {
    return {
      'consumed': 0,
      'burned': 0,
      'target': 2200, // Default target
      'net': 0,
      'remaining': 2200,
      'weekly_average': 0,
      'monthly_average': 0,
    };
  }

  // Helper method to parse double values from API responses
  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  // Helper method to extract workout sessions from API response
  List<WorkoutSession>? _extractWorkoutSessionsFromResponse(
      Map<String, dynamic> response) {
    List<WorkoutSession>? sessions;
    if (response.containsKey('workout_sessions') &&
        response['workout_sessions'] != null) {
      try {
        sessions = (response['workout_sessions'] as List<dynamic>)
            .map((e) => WorkoutSession.fromJson(e as Map<String, dynamic>))
            .toList();
      } catch (e) {
        Logger.error('Error parsing workout_sessions: $e', tag: 'ApiService');
      }
    }
    return sessions;
  }

  // Logout method (Implementing backend call)
  Future<void> logout() async {
    Logger.api('Attempting logout.', tag: 'ApiService.logout');
    final refreshToken = await _secureStorage.read(key: _refreshTokenKey);

    if (refreshToken != null) {
      try {
        Logger.api('Calling backend logout endpoint: ${ApiConstants.logout}',
            tag: 'ApiService.logout');
        final cleanDio = Dio(BaseOptions(baseUrl: apiBaseUrl));
        await cleanDio.post(
          ApiConstants.logout,
          data: {'refresh': refreshToken},
          options: Options(
              validateStatus: (status) => status != null && status < 500),
        );
        Logger.api('Backend logout call successful or handled.',
            tag: 'ApiService.logout');
      } catch (e) {
        Logger.api('Backend logout call failed: $e',
            tag: 'ApiService.logout', error: e);
      }
    } else {
      Logger.api('No refresh token found for backend logout.',
          tag: 'ApiService.logout');
    }

    await _secureStorage.delete(key: _accessTokenKey);
    await _secureStorage.delete(key: _refreshTokenKey);
    Logger.api('Local tokens cleared.', tag: 'ApiService.logout');
  }

  // --- Add updateProfile Method ---
  // Renamed back to updateUserProfile for FitnessProvider compatibility
  Future<UserProfile> updateUserProfile(
      /*int profileId,*/ Map<String, dynamic> data) async {
    // Remove profileId parameter
    try {
      // Use the userProfileUpdate endpoint instead of userProfile
      const String endpoint =
          ApiConstants.userProfileUpdate; // Use /accounts/users/profile/update/
      // Use PATCH instead of POST for profile updates
      Logger.info('Attempting PATCH to $endpoint for profile update',
          tag: 'ApiService');
      final response = await patch(
        // Use the patch helper
        endpoint,
        data: data,
      );
      // The post helper already checks for 2xx status
      if (response != null && response is Map<String, dynamic>) {
        // Backend might return the updated profile directly
        final UserProfile updatedProfile = UserProfile.fromJson(response);
        Logger.info('Profile updated successfully via API.', tag: 'ApiService');
        return updatedProfile;
      } else {
        // Log the response body if available for debugging
        String responseBody = response?.toString() ?? 'Invalid response format';
        Logger.warning(
            'Failed to update profile: Unexpected response format: $responseBody',
            tag: 'ApiService');
        throw Exception('Failed to update profile: Unexpected response format');
      }
    } on DioException catch (e) {
      Logger.error('API Error updating profile: ${e.message}',
          error: e, tag: 'ApiService');
      rethrow;
    } catch (e) {
      Logger.error('Unexpected error updating profile: $e',
          error: e, tag: 'ApiService');
      rethrow;
    }
  }
  // --- End updateProfile Method ---

  // --- Meal Log Specific Methods ---

  Future<MealLog> updateMealLog(
      int mealLogId, Map<String, dynamic> data) async {
    Logger.api('Updating meal log ID: $mealLogId with data: $data',
        tag: 'ApiService.updateMealLog');
    try {
      final responseData =
          await patch('${ApiConstants.mealLogs}/$mealLogId/', data: data);
      Logger.api('Meal log update successful for ID: $mealLogId',
          tag: 'ApiService.updateMealLog');
      return MealLog.fromJson(
          responseData); // Assuming MealLog has a fromJson factory
    } catch (e) {
      Logger.api('Failed to update meal log ID: $mealLogId',
          tag: 'ApiService.updateMealLog', error: e);
      rethrow; // Rethrow the error to be handled by the provider
    }
  }

  // --- Hydration Specific Methods ---

  // Verify Email Code
  Future<Map<String, dynamic>> verifyEmailCode(
      String email, String code) async {
    Logger.api('Attempting to verify email for $email with code: $code',
        tag: 'ApiService.verifyEmailCode');
    try {
      final data = {'email': email, 'code': code};
      final cleanDio = Dio(BaseOptions(
          baseUrl: apiBaseUrl,
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          validateStatus: (status) => true));
      final response =
          await cleanDio.post(ApiConstants.verifyEmailCode, data: data);
      Logger.api('Email verification response status: ${response.statusCode}',
          tag: 'ApiService.verifyEmailCode');

      if (response.statusCode == 200) {
        Logger.api('Email verification successful.',
            tag: 'ApiService.verifyEmailCode');
        return {
          'status': 'success',
          'data': response.data,
        };
      } else {
        String errorMessage = 'Email verification failed';
        if (response.data is Map) {
          final errors = response.data as Map<String, dynamic>;
          if (errors.isNotEmpty) {
            errorMessage = errors.entries
                .map((e) =>
                    '${e.key}: ${e.value is List ? e.value.join(', ') : e.value}')
                .join('; ');
          } else if (errors['detail'] != null) {
            errorMessage = errors['detail'];
          } else {
            errorMessage =
                'Email verification failed with status ${response.statusCode}';
          }
        } else if (response.data is String && response.data.isNotEmpty) {
          errorMessage = response.data;
        } else {
          errorMessage =
              'Email verification failed with status ${response.statusCode}';
        }
        Logger.api('Email verification failed: $errorMessage',
            tag: 'ApiService.verifyEmailCode');
        return {
          'status': 'error',
          'error': {
            'code': _getErrorCode(response.statusCode),
            'message': errorMessage,
            'details': response.data
          }
        };
      }
    } catch (e) {
      Logger.api('Email verification exception: ${e.toString()}',
          tag: 'ApiService.verifyEmailCode', error: e);
      return {
        'status': 'error',
        'error': {
          'code': 'VERIFICATION_FAILED',
          'message': 'Email verification failed: ${e.toString()}',
          'details': e.toString()
        }
      };
    }
  }

  // Resend Email Verification Code
  Future<Map<String, dynamic>> resendEmailVerificationCode(String email) async {
    Logger.api('Attempting to resend email verification code for $email',
        tag: 'ApiService.resendEmailVerificationCode');
    try {
      final data = {'email': email};
      final cleanDio = Dio(BaseOptions(
          baseUrl: apiBaseUrl,
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          validateStatus: (status) => true));
      final response =
          await cleanDio.post(ApiConstants.resendEmailCode, data: data);
      Logger.api(
          'Resend email verification response status: ${response.statusCode}',
          tag: 'ApiService.resendEmailVerificationCode');

      if (response.statusCode == 200) {
        Logger.api('Resend email verification successful.',
            tag: 'ApiService.resendEmailVerificationCode');
        return {
          'status': 'success',
          'message': response.data?['detail'] ?? 'Verification code sent.',
          'data': response.data,
        };
      } else {
        String errorMessage = 'Failed to resend verification code';
        if (response.data is Map) {
          final errors = response.data as Map<String, dynamic>;
          if (errors.isNotEmpty) {
            errorMessage = errors.entries
                .map((e) =>
                    '${e.key}: ${e.value is List ? e.value.join(', ') : e.value}')
                .join('; ');
          } else if (errors['detail'] != null) {
            errorMessage = errors['detail'];
          } else {
            errorMessage =
                'Failed to resend verification code with status ${response.statusCode}';
          }
        } else if (response.data is String && response.data.isNotEmpty) {
          errorMessage = response.data;
        } else {
          errorMessage =
              'Failed to resend verification code with status ${response.statusCode}';
        }
        Logger.api('Resend email verification failed: $errorMessage',
            tag: 'ApiService.resendEmailVerificationCode');
        return {
          'status': 'error',
          'error': {
            'code': _getErrorCode(response.statusCode),
            'message': errorMessage,
            'details': response.data
          }
        };
      }
    } catch (e) {
      Logger.api('Resend email verification exception: ${e.toString()}',
          tag: 'ApiService.resendEmailVerificationCode', error: e);
      return {
        'status': 'error',
        'error': {
          'code': 'RESEND_FAILED',
          'message': 'Failed to resend verification code: ${e.toString()}',
          'details': e.toString()
        }
      };
    }
  }
}
