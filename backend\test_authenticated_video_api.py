#!/usr/bin/env python
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
django.setup()

from django.test import Client
from accounts.models import User, UserProfile
from workouts.models import UserWorkoutPlan, WorkoutPlan, WorkoutVideo

def test_authenticated_video_api():
    print("=== Testing Authenticated Video API ===\n")
    
    # Get the user we assigned the video plan to
    user = User.objects.get(username='dakshsharma15195')
    print(f"Testing with user: {user.username}")
    
    # Create a test client and force login
    client = Client()
    client.force_login(user)
    
    # Test 1: Get videos for active workout plan
    print("\n1. Testing /api/workouts/videos/ (user's active plan)")
    response = client.get('/api/workouts/videos/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ Success! Response type: {type(data)}")
        print(f"   Raw response: {data}")
        if isinstance(data, list):
            print(f"   Found {len(data)} videos")
            for video in data:
                print(f"     - {video['title']} (Order: {video['order']})")
        else:
            print(f"   Unexpected response format")
    else:
        print(f"   Error: {response.content.decode()}")
    
    # Test 2: Get videos by plan ID
    print("\n2. Testing /api/workouts/videos/by-plan/1/")
    response = client.get('/api/workouts/videos/by-plan/1/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ Success! Found {len(data)} videos")
        for video in data:
            print(f"     - {video['title']} (Order: {video['order']})")
    else:
        print(f"   Error: {response.content.decode()}")
    
    # Test 3: Get specific video
    print("\n3. Testing /api/workouts/videos/1/")
    response = client.get('/api/workouts/videos/1/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ Success! Video: {data['title']}")
        print(f"     Duration: {data['duration_seconds']} seconds")
        print(f"     Video URL: {data['video_url']}")
        print(f"     Thumbnail URL: {data['thumbnail_url']}")
    else:
        print(f"   Error: {response.content.decode()}")
    
    print("\n=== API Test Complete ===")
    print("The user should now see the video button in the Flutter app!")

if __name__ == "__main__":
    test_authenticated_video_api()
