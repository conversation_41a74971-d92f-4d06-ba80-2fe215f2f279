// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'meal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MealDetail _$MealDetailFromJson(Map<String, dynamic> json) => MealDetail(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      calories: _parseInt(json['calories']),
      protein: _parseDouble(json['protein']),
      carbs: _parseDouble(json['carbs']),
      fat: _parseDouble(json['fat']),
      mealType: json['meal_type'] as String?,
      dietType: json['diet_type'] as String?,
      preparationTime: _parseInt(json['preparation_time']),
      imageUrl: json['image_url'] as String?,
      mealImage: json['meal_image'] as String?,
      mealImageUrl: json['meal_image_url'] as String?,
    );

Map<String, dynamic> _$MealDetailToJson(MealDetail instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'calories': instance.calories,
      'protein': instance.protein,
      'carbs': instance.carbs,
      'fat': instance.fat,
      'meal_type': instance.mealType,
      'diet_type': instance.dietType,
      'preparation_time': instance.preparationTime,
      'image_url': instance.imageUrl,
      'meal_image': instance.mealImage,
      'meal_image_url': instance.mealImageUrl,
    };

MealPlan _$MealPlanFromJson(Map<String, dynamic> json) => MealPlan(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      coverImageUrl: json['cover_image_url'] as String?,
      dailyCalories: (json['daily_calories'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MealPlanToJson(MealPlan instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'cover_image_url': instance.coverImageUrl,
      'daily_calories': instance.dailyCalories,
    };
