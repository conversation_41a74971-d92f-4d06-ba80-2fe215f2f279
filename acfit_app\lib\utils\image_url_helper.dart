import '../utils/logger.dart';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

class ImageUrlHelper {
  static String transformImageUrl(String? url) {
    // Add a special tag for easier filtering in logs
    Logger.tempLog('TRANSFORM_URL_INPUT: $url', tag: 'ImageUrlHelper');

    // Determine the correct base URL based on platform
    String baseUrl;
    if (kIsWeb) {
      baseUrl = 'http://localhost:8000';
    } else if (Platform.isAndroid) {
      baseUrl = 'http://********:8000';
    } else {
      baseUrl = 'http://localhost:8000';
    }

    if (url == null || url.isEmpty) {
      return '$baseUrl/media/workout_days/covers/default_workout.jpg';
    }

    // Check if the URL is a localhost URL or relative path (which won't work in emulators)
    if (url.contains('********:8000') ||
        url.contains('localhost') ||
        url.contains('127.0.0.1') ||
        url.startsWith('/media/')) {
      // Log the transformation with special tag for easier filtering
      Logger.tempLog('TRANSFORM_URL_DETECTED_LOCAL: $url',
          tag: 'ImageUrlHelper');

      // Extract the path from the URL
      String path = '';
      try {
        // Try to extract the path using different methods

        // Method 1: Check if URL already starts with /media/ (relative path)
        if (url.startsWith('/media/')) {
          path = url;
          Logger.tempLog('TRANSFORM_URL_RELATIVE_PATH: $path',
              tag: 'ImageUrlHelper');

          // Replace with platform-appropriate URL
          String newUrl = '$baseUrl$path';
          Logger.tempLog('TRANSFORM_URL_RESULT: $newUrl',
              tag: 'ImageUrlHelper');
          return newUrl;
        }

        // Method 2: Find the position of /media/ in the URL
        int mediaIndex = url.indexOf('/media/');
        Logger.tempLog('TRANSFORM_URL_MEDIA_INDEX: $mediaIndex',
            tag: 'ImageUrlHelper');

        if (mediaIndex >= 0) {
          // Extract everything from /media/ onwards
          path = url.substring(mediaIndex);
          Logger.tempLog('TRANSFORM_URL_EXTRACTED_PATH: $path',
              tag: 'ImageUrlHelper');

          // Replace with platform-appropriate URL
          String newUrl = '$baseUrl$path';
          Logger.tempLog('TRANSFORM_URL_RESULT: $newUrl',
              tag: 'ImageUrlHelper');
          return newUrl;
        }

        // Method 3: Try to parse the URL and extract the path
        try {
          Uri uri = Uri.parse(url);
          if (uri.path.isNotEmpty && uri.path.contains('/media/')) {
            path = uri.path;
            Logger.tempLog('TRANSFORM_URL_EXTRACTED_PATH_URI: $path',
                tag: 'ImageUrlHelper');

            // Replace with platform-appropriate URL
            String newUrl = '$baseUrl$path';
            Logger.tempLog('TRANSFORM_URL_RESULT_URI: $newUrl',
                tag: 'ImageUrlHelper');
            return newUrl;
          }
        } catch (uriError) {
          Logger.tempLog('TRANSFORM_URL_URI_ERROR: $uriError',
              tag: 'ImageUrlHelper');
        }

        // If we get here, we couldn't find a media path
        Logger.tempLog('TRANSFORM_URL_NO_MEDIA_PATH_FOUND',
            tag: 'ImageUrlHelper');
      } catch (e) {
        Logger.tempLog('TRANSFORM_URL_ERROR: $e', tag: 'ImageUrlHelper');
      }

      // Fallback to default if path extraction fails
      String newUrl = '$baseUrl/media/workout_days/covers/default_workout.jpg';
      Logger.tempLog('TRANSFORM_URL_USING_DEFAULT: $newUrl',
          tag: 'ImageUrlHelper');
      return newUrl;
    }

    Logger.tempLog('TRANSFORM_URL_NO_CHANGE: $url', tag: 'ImageUrlHelper');
    return url;
  }
}
