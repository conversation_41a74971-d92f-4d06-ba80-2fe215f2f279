import 'package:flutter/material.dart';
import '../models/meal.dart'; // Imports MealDetail
import '../services/navigation_service.dart'; // Import NavigationService
import '../widgets/common/animated_loading.dart'; // For placeholder
import '../utils/image_utils.dart'; // Import our new image utility class

class MealCard extends StatelessWidget {
  final MealDetail meal; // Use MealDetail

  const MealCard({
    Key? key,
    required this.meal,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment:
                  CrossAxisAlignment.start, // Align items to top
              children: [
                // Meal Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: ImageUtils.getNetworkImageWithFallback(
                    imageUrl:
                        meal.mealImageUrl ?? meal.imageUrl ?? meal.mealImage,
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    fallbackIcon: Icons.restaurant_menu,
                    fallbackIconColor: Colors.grey,
                    placeholder: Container(
                      width: 60,
                      height: 60,
                      color: Colors.grey[300],
                      child: const Center(child: AnimatedLoading()),
                    ),
                  ),
                ),
                const SizedBox(width: 12), // Increased spacing
                Expanded(
                  // Meal Name and Type Chip
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        meal.name,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                        maxLines: 2, // Allow wrapping
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      _buildMealTypeChip(context), // Keep chip below name
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12), // Adjusted spacing
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildNutrientInfo(
                  context,
                  'Calories',
                  '${meal.calories ?? 0}', // Handle null
                  Icons.local_fire_department,
                ),
                _buildNutrientInfo(
                  context,
                  'Protein',
                  '${meal.protein ?? 0}g', // Handle null
                  Icons.fitness_center,
                ),
                _buildNutrientInfo(
                  context,
                  'Carbs',
                  '${meal.carbs ?? 0}g', // Handle null
                  Icons.grain,
                ),
                _buildNutrientInfo(
                  context,
                  'Fat',
                  '${meal.fat ?? 0}g', // Handle null
                  Icons.opacity,
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Navigate to actual MealDetailsScreen using NavigationService
                NavigationService.navigateToNamed(
                  NavigationService.mealDetails,
                  arguments: meal, // Pass the MealDetail object
                );
              },
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 40),
              ),
              child: const Text('View Details'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMealTypeChip(BuildContext context) {
    Color chipColor;
    String mealTypeText;

    // Use mealType from MealDetail
    switch (meal.mealType) {
      case 'B': // Match the model value
        chipColor = Colors.orange;
        mealTypeText = 'Breakfast';
        break;
      case 'L': // Match the model value
        chipColor = Colors.green;
        mealTypeText = 'Lunch';
        break;
      case 'D': // Match the model value
        chipColor = Colors.blue;
        mealTypeText = 'Dinner';
        break;
      case 'S': // Match the model value
        chipColor = Colors.purple;
        mealTypeText = 'Snack';
        break;
      default:
        chipColor = Colors.grey;
        mealTypeText =
            meal.mealType ?? 'Meal'; // Show type if unknown but present
    }

    return Chip(
      backgroundColor: chipColor.withAlpha(51), // Use withAlpha (0.2 * 255)
      label: Text(
        mealTypeText,
        style: TextStyle(color: chipColor, fontSize: 12), // Smaller font size
      ),
      padding: EdgeInsets.zero, // Reduce padding
      labelPadding:
          const EdgeInsets.symmetric(horizontal: 8.0), // Adjust label padding
      materialTapTargetSize:
          MaterialTapTargetSize.shrinkWrap, // Reduce tap target size
    );
  }

  Widget _buildNutrientInfo(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, size: 16, color: Colors.grey),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  // Removed placeholder _buildMealDetailsScreen and its helpers
}
