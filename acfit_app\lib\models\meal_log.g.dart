// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'meal_log.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TodaysMealResponse _$TodaysMealResponseFromJson(Map<String, dynamic> json) =>
    TodaysMealResponse(
      date: json['date'] as String,
      mealLogs: (json['meal_logs'] as List<dynamic>?)
              ?.map((e) => MealLog.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      hydration: json['hydration'] == null
          ? null
          : Hydration.fromJson(json['hydration'] as Map<String, dynamic>),
      noPlanAssigned: json['no_plan_assigned'] as bool? ?? false,
      message: json['message'] as String?,
      dayNumber: (json['day_number'] as num?)?.toInt() ?? 0,
      isFutureDay: json['is_future_day'] as bool? ?? false,
    );

Map<String, dynamic> _$TodaysMealResponseToJson(TodaysMealResponse instance) =>
    <String, dynamic>{
      'date': instance.date,
      'meal_logs': instance.mealLogs.map((e) => e.toJson()).toList(),
      'hydration': instance.hydration?.toJson(),
      'no_plan_assigned': instance.noPlanAssigned,
      'message': instance.message,
      'day_number': instance.dayNumber,
      'is_future_day': instance.isFutureDay,
    };

MealLog _$MealLogFromJson(Map<String, dynamic> json) => MealLog(
      id: (json['id'] as num?)?.toInt() ?? 0,
      meal: MealDetail.fromJson(json['meal'] as Map<String, dynamic>),
      mealType: json['meal_type'] as String?,
      isCompleted: json['is_completed'] as bool,
      scheduledTime: json['scheduled_time'] == null
          ? null
          : DateTime.parse(json['scheduled_time'] as String),
      completionTime: json['completion_time'] == null
          ? null
          : DateTime.parse(json['completion_time'] as String),
      actualCalories: (json['actual_calories'] as num?)?.toDouble(),
      actualProtein: (json['actual_protein'] as num?)?.toDouble(),
      actualCarbs: (json['actual_carbs'] as num?)?.toDouble(),
      actualFat: (json['actual_fat'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$MealLogToJson(MealLog instance) => <String, dynamic>{
      'id': instance.id,
      'meal': instance.meal.toJson(),
      if (instance.mealType case final value?) 'meal_type': value,
      'is_completed': instance.isCompleted,
      if (instance.scheduledTime?.toIso8601String() case final value?)
        'scheduled_time': value,
      if (instance.completionTime?.toIso8601String() case final value?)
        'completion_time': value,
      if (instance.actualCalories case final value?) 'actual_calories': value,
      if (instance.actualProtein case final value?) 'actual_protein': value,
      if (instance.actualCarbs case final value?) 'actual_carbs': value,
      if (instance.actualFat case final value?) 'actual_fat': value,
    };

Hydration _$HydrationFromJson(Map<String, dynamic> json) => Hydration(
      amountMl: (json['amount_ml'] as num).toInt(),
      targetMl: (json['target_ml'] as num).toInt(),
      percentage: (json['percentage'] as num?)?.toInt(),
    );

Map<String, dynamic> _$HydrationToJson(Hydration instance) => <String, dynamic>{
      'amount_ml': instance.amountMl,
      'target_ml': instance.targetMl,
      'percentage': instance.percentage,
    };
