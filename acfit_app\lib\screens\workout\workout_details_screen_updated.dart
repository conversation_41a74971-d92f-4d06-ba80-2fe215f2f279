import 'package:flutter/material.dart';

// Removed unused import
import '../../models/workout.dart'; // Import the WorkoutDay, WorkoutSession, etc. models
import '../../models/workout_log.dart'; // Import the WorkoutLog model
import 'workout_player_screen.dart'; // Import the WorkoutPlayerScreen
import '../../services/navigation_service.dart';
import '../../widgets/common/offline_banner.dart';
import '../../widgets/common/animated_loading.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../utils/logger.dart'; // Import the main Logger class

// Using our own class definitions at the bottom of the file

class WorkoutDetailsScreen extends StatefulWidget {
  final Map<String, dynamic>
      workout; // Accept a Map for workout data (WorkoutDay details)
  final List<WorkoutLog> workoutLogs; // Accept a list of WorkoutLog objects
  final int? targetSessionId; // Add parameter for target session ID

  const WorkoutDetailsScreen({
    Key? key,
    required this.workout,
    required this.workoutLogs, // Changed from workoutLog, now required
    this.targetSessionId, // Optional parameter to specify which session to display
  }) : super(key: key);

  @override
  State<WorkoutDetailsScreen> createState() => _WorkoutDetailsScreenState();
}

class _WorkoutDetailsScreenState extends State<WorkoutDetailsScreen> {
  bool _isLoading = false;
  final bool _isOffline = false; // Assuming online, manage this state if needed
  String? _error;
  // This will store exercises grouped by session ID
  final Map<int?, List<Map<String, dynamic>>> _exercisesBySession = {};
  List<WorkoutSession> _sessions = []; // Store the sessions derived from logs

  // Derived state variables
  bool _isFutureWorkout = false;
  bool _isDayCompleted = false;

  // This method is no longer needed as we're handling the session data directly
  // Keeping it commented out for reference
  /*
  List<WorkoutSection>? _createSectionsFromMap(List<dynamic>? sectionsData) {
    // Implementation removed
    return null;
  }
  */

  // Design constants from reference (approx)
  static const Color screenBg = Color(0xFFF3F3F3);
  static const Color headerBg = Colors.white;
  static const Color darkText = Color.fromRGBO(16, 17, 20, 1);
  static const Color lightText = Color.fromRGBO(103, 107, 116, 1);
  static const Color accentOrange = Color.fromRGBO(255, 127, 54, 1);
  // Removed unused constants

  @override
  void initState() {
    super.initState();
    _processWorkoutData();
  }

  // --- DATA PROCESSING LOGIC ---
  void _processWorkoutData() {
    if (widget.workoutLogs.isEmpty) {
      setState(() {
        _isLoading = false;
        _error = 'No workout logs provided.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
      _sessions.clear();
      _exercisesBySession.clear();
    });

    try {
      // Use the first workout log to represent the day
      final workoutLog = widget.workoutLogs.first;
      final workoutDay = workoutLog.workoutDay;

      // Check if all sessions for this day are completed
      _isDayCompleted = false;
      if (workoutDay != null &&
          workoutDay.sessions != null &&
          workoutDay.sessions!.isNotEmpty) {
        bool allSessionsCompleted = true;
        for (final session in workoutDay.sessions!) {
          if (!workoutLog.isSessionCompleted(session.id)) {
            allSessionsCompleted = false;
            break;
          }
        }
        _isDayCompleted = allSessionsCompleted;
      } else {
        // Fallback to the old method if we don't have session data
        _isDayCompleted = workoutLog.isCompleted;
      }

      _isFutureWorkout =
          DateTime.parse(workoutLog.date).isAfter(DateTime.now());

      if (workoutDay == null) {
        throw Exception('Workout day data is missing.');
      }

      // Get all sessions from the workout day
      List<WorkoutSession> allSessions = workoutDay.sessions ?? [];

      // Log details of all sessions for debugging
      for (int i = 0; i < allSessions.length; i++) {
        final session = allSessions[i];

        // Log details of sections in each session
        if (session.sections != null) {
          for (int j = 0; j < session.sections!.length; j++) {
            final section = session.sections![j];

            // Log details of exercises in each section
            for (int k = 0; k < section.exercises.length; k++) {
              final exercise = section.exercises[k];
            }
          }
        }
      }

      // Filter sessions based on targetSessionId if provided
      if (widget.targetSessionId != null) {
        _sessions = allSessions
            .where((session) => session.id == widget.targetSessionId)
            .toList();

        if (_sessions.isEmpty) {}
      } else {
        _sessions = allSessions;
      }

      // Check if sessions have exercises
      bool hasExercises = false;
      for (final session in _sessions) {
        if (session.sections != null) {
          for (final section in session.sections!) {
            if (section.exercises.isNotEmpty) {
              hasExercises = true;
              break;
            }
          }
        }
        if (hasExercises) break;
      }

      if (_sessions.isEmpty || !hasExercises) {
        // Legacy path or fallback: If sessions are not nested in workoutDay, try workoutLog.sections
        if (workoutLog.sections.isNotEmpty) {
          // Create a dummy session to hold these sections
          final dummySession = WorkoutSession(
              id: workoutLog.workoutSession ??
                  0, // Use existing session ID if available
              name: 'Workout Session', // Default name
              order: 1,
              sections: workoutLog.sections);
          _sessions = [dummySession];
        } else if (workoutLog.sessionLogs != null &&
            workoutLog.sessionLogs!.isNotEmpty) {
          // Check if there are session logs with session details

          List<WorkoutSession> extractedSessions = [];

          for (final sessionLog in workoutLog.sessionLogs!) {
            if (sessionLog.workoutSessionDetails != null) {
              extractedSessions.add(sessionLog.workoutSessionDetails!);
            }
          }

          if (extractedSessions.isNotEmpty) {
            _sessions = extractedSessions;
          } else {}
        } else {}
      }

      // Group exercises by session ID
      List<Map<String, dynamic>> sessionExercises =
          []; // Temporary list for current session

      // IMPORTANT: Clear all existing exercise data to prevent old data from showing up
      _exercisesBySession.clear();

      for (final session in _sessions) {
        if (session.sections == null || session.sections!.isEmpty) {
          _exercisesBySession[session.id] =
              []; // Ensure entry exists even if empty
          continue;
        }

        sessionExercises = []; // Reset for each new session

        for (final section in session.sections ?? []) {
          if (section.exercises.isEmpty) {
            continue;
          }

          for (final exerciseInstance in section.exercises) {
            // Check if exerciseDetails is null or if the exercise name is empty/null
            if (exerciseInstance.exerciseDetails == null ||
                exerciseInstance.exerciseDetails?.name == null ||
                exerciseInstance.exerciseDetails!.name.isEmpty) {
              continue;
            }

            // Create a map with all the data we need for this exercise
            final exerciseData = {
              'exerciseInstance': exerciseInstance,
              'workoutSection': section,
              'seq':
                  exerciseInstance.order, // Use the order property for sequence
              'name':
                  exerciseInstance.exerciseDetails?.name ?? 'Unknown Exercise',
              'type': section.name, // Use section name as the type
              'image': exerciseInstance.exerciseDetails?.imageUrl,
              'duration': exerciseInstance.duration != null
                  ? '${exerciseInstance.duration}s'
                  : exerciseInstance.sets != null
                      ? '${exerciseInstance.sets} sets'
                      : 'N/A',
              'sets': exerciseInstance.sets,
              'reps': exerciseInstance.reps,
            };

            sessionExercises.add(exerciseData);
          }
        }
        // Store exercises for the completed session
        _exercisesBySession[session.id] =
            List.from(sessionExercises); // Add a copy

        // Log the details of stored exercises for debugging
        final exercises = _exercisesBySession[session.id] ?? [];
        for (int i = 0; i < exercises.length; i++) {
          final exercise = exercises[i];
          final exerciseInstance =
              exercise['exerciseInstance'] as WorkoutExerciseLog?;
          final workoutSection = exercise['workoutSection'] as WorkoutSection?;
        }
      }

      // Store the processed exercises under the target session's ID
      // Ensure targetSession is not null before accessing its id
      // This seems redundant now as we store per session in the loop above
      // Commenting out this specific block as potentially incorrect logic after loop refactor
      /*
      if (targetSession != null) {
          _exercisesBySession[targetSession.id] = sessionExercises;
      }
      */

      // Example access: Logger.meal('Exercises for session ${_sessions.first.id}: ${_exercisesBySession[_sessions.first.id]?.length}', tag: 'WorkoutDetailsScreen');

      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to process workout data. Please try again.';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Status variables (_isLoading, _error, _isFutureWorkout, _isDayCompleted) are now managed in state

    return Scaffold(
      backgroundColor: screenBg,
      // Add a persistent bottom button for starting the workout
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: _buildStartWorkoutButton(),
      body: Column(
        children: [
          if (_isOffline)
            const OfflineBanner(), // Keep offline banner if needed
          Expanded(
            child: _buildContent(theme), // Pass only theme
          ),
          // Add space at the bottom for the floating action button
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  // Build the persistent start workout button
  Widget? _buildStartWorkoutButton() {
    // Don't show button if workout is completed or sessions are empty
    if (_isDayCompleted || _sessions.isEmpty) {
      return null;
    }

    // Check if the workout is for a future date
    if (_isFutureWorkout) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('You cannot start future workouts'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 3),
              ),
            );
          },
          icon: const Icon(Icons.calendar_today, size: 24),
          label: const Text('Future Workout'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade100,
            foregroundColor: Colors.blue.shade800,
            padding: const EdgeInsets.symmetric(vertical: 16),
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              fontFamily: 'Work Sans',
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 0,
          ),
        ),
      );
    }

    // Get the first session to start (or the target session if specified)
    WorkoutSession? sessionToStart;
    if (widget.targetSessionId != null && _sessions.isNotEmpty) {
      sessionToStart = _sessions.firstWhere(
        (s) => s.id == widget.targetSessionId,
        orElse: () => _sessions.first,
      );
    } else if (_sessions.isNotEmpty) {
      sessionToStart = _sessions.first;
    }

    if (sessionToStart == null) return null;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _startSpecificWorkoutSession(
            widget.workoutLogs.first, sessionToStart!),
        icon: const Icon(Icons.play_arrow_rounded, size: 24),
        label: const Text('Start Workout'),
        style: ElevatedButton.styleFrom(
          backgroundColor: accentOrange,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Work Sans',
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  // --- Helper to Start a Specific Workout Session ---
  void _startSpecificWorkoutSession(
      WorkoutLog workoutLog, WorkoutSession session) {
    // `workoutLog` here is the specific log for this session

    // Get the exercises for this specific session
    final List<Map<String, dynamic>> exercisesForSession =
        _exercisesBySession[session.id] ?? [];

    // Extract the WorkoutExerciseLog objects from the exercise maps
    final List<WorkoutExerciseLog> exerciseLogs = exercisesForSession
        .map((e) => e['exerciseInstance'] as WorkoutExerciseLog)
        .where((e) =>
            e.exerciseDetails !=
            null) // Remove null check on e itself as it can't be null after the cast
        .toList();

    if (exerciseLogs.isEmpty) {
      // Check if the session has sections with exercises directly
      List<WorkoutExerciseLog> directExercises = [];
      if (session.sections != null) {
        for (final section in session.sections!) {
          if (section.exercises.isNotEmpty) {
            // Filter out exercises without exercise details
            final validExercises = section.exercises
                .where((ex) => ex.exerciseDetails != null)
                .toList();
            directExercises.addAll(validExercises);
          }
        }
      }

      if (directExercises.isNotEmpty) {
        // Continue with these exercises
        _startWorkoutWithExercises(workoutLog, session, directExercises);
        return;
      }

      // No exercises found anywhere
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No exercises found for this session.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Use the exerciseLogs we extracted earlier
    _startWorkoutWithExercises(workoutLog, session, exerciseLogs);
  }

  // Helper method to start workout with a list of exercises
  void _startWorkoutWithExercises(WorkoutLog workoutLog, WorkoutSession session,
      List<WorkoutExerciseLog> exercises) {
    try {
      // Get the workout session log ID from the session
      // This is the correct ID to pass to the workout player
      final int workoutSessionLogId = session.id;

      // Navigate to the player screen, passing the specific WorkoutLog for this session
      // and the list of WorkoutExerciseLogs belonging ONLY to this session.
      NavigationService.navigateTo(
        WorkoutPlayerScreen(
          workoutLog: workoutLog, // Pass the workout day log
          workoutSessionLogId:
              workoutSessionLogId, // Pass the session ID, not the workout log ID
          startIndex: 0,
          allExercises: exercises, // Pass only this session's exercises
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error starting session: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // --- Builds the main scrollable content ---
  Widget _buildContent(ThemeData theme) {
    for (int i = 0; i < _sessions.length; i++) {
      final session = _sessions[i];
      final exercises = _exercisesBySession[session.id] ?? [];
    }
    if (_isLoading) {
      return const Center(child: AnimatedLoading());
    }

    if (_error != null) {
      return Center(
        child: CustomErrorWidget(
          message: _error!,
          onRetry: _processWorkoutData, // Retry processing
        ),
      );
    }

    // Handle case where sessions list might be empty after processing
    if (_sessions.isEmpty) {
      // This case should ideally be caught earlier, but handle it defensively
      return _buildEmptyExercisesMessage(
        message: 'No workout sessions were found for this day.',
        showRetry: true,
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // --- Styled Header ---
          Container(
            // color: headerBg, // Removed direct color, Card will handle it below if needed
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top + 10, // Status bar
              left: 16.0,
              right: 16.0,
              bottom: 16.0,
            ),
            decoration: BoxDecoration(
              // Add bottom border for separation
              color: headerBg,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200, width: 1.0),
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back_ios_new,
                      color: darkText, size: 24), // Changed icon
                  onPressed: () => NavigationService.goBack(),
                  tooltip: 'Back',
                  splashRadius: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Use session name if targetSessionId is provided, otherwise use WorkoutDay name
                      Text(
                        widget.targetSessionId != null && _sessions.isNotEmpty
                            ? _sessions.first
                                .name // Use the name of the filtered session
                            : widget.workoutLogs.first.workoutDay?.name ??
                                'Workout Details',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: darkText,
                          fontFamily: 'Work Sans',
                          fontSize: 22, // Slightly larger
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6), // Added spacing
                      // Show overall day completed status if applicable
                      if (_isDayCompleted)
                        _buildStatusBadge(
                          text: 'Day Completed',
                          icon: Icons
                              .check_circle_outline_rounded, // Changed icon
                          bgColor: Colors.green.shade50,
                          borderColor: Colors.green.shade200,
                          iconColor: Colors.green.shade700, // Darker icon
                          textColor: Colors.green.shade800,
                        )
                      // Show Future Workout badge if applicable
                      else if (_isFutureWorkout) // Use else if
                        _buildStatusBadge(
                          text: 'Scheduled', // Changed text
                          icon: Icons.calendar_today_outlined, // Changed icon
                          bgColor: Colors.blue.shade50,
                          borderColor: Colors.blue.shade200,
                          iconColor: Colors.blue.shade700,
                          textColor: Colors.blue.shade800,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16), // Added spacing after header

          // --- Main Content Area (Sessions and Exercises) ---
          // Removed outer Padding, handled by ListView padding and Card margin
          ListView.builder(
            padding: const EdgeInsets.symmetric(
                horizontal: 12.0, vertical: 8.0), // Adjusted padding
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            // IMPORTANT: When targetSessionId is provided, we should only show one session
            // This ensures we don't accidentally show multiple sessions
            itemCount: widget.targetSessionId != null
                ? (_sessions.isEmpty ? 0 : 1)
                : _sessions.length,
            itemBuilder: (context, index) {
              // When targetSessionId is provided, make sure we're using the correct session
              // This is a safety check to ensure we're only showing exercises for the target session
              WorkoutSession session;
              if (widget.targetSessionId != null && _sessions.isNotEmpty) {
                // Find the session with the matching ID
                final targetSession = _sessions.firstWhere(
                  (s) => s.id == widget.targetSessionId,
                  orElse: () => _sessions[index],
                );
                session = targetSession;
              } else {
                // Use the session at the current index
                session = _sessions[index];
              }

              // Since _sessions now only contains the target session, index is always 0
              // We use the specificLog that was passed to the screen
              final workoutLog = widget
                  .workoutLogs.first; // The single log passed to the screen

              // Use the helper method to check if this session is completed
              bool isSessionCompleted =
                  workoutLog.isSessionCompleted(session.id);

              final List<Map<String, dynamic>> exercisesForThisSession =
                  _exercisesBySession[session.id] ?? [];

              if (exercisesForThisSession.isNotEmpty) {
                for (int i = 0;
                    i <
                        (exercisesForThisSession.length > 3
                            ? 3
                            : exercisesForThisSession.length);
                    i++) {
                  final exercise = exercisesForThisSession[i];
                  final exerciseInstance =
                      exercise['exerciseInstance'] as WorkoutExerciseLog?;
                  final workoutSection =
                      exercise['workoutSection'] as WorkoutSection?;
                }
              }

              // Determine session-specific completion (requires more data, simplified for now)
              // bool isThisSpecificSessionCompleted = isDayCompleted; // Approximation

              return _buildSessionGroup(
                theme,
                session,
                workoutLog,
                isSessionCompleted, // Use the day's completion for now
                exercisesForThisSession,
              );
            },
          ),
          const SizedBox(height: 80), // Space at the bottom
        ],
      ),
    );
  }

  // --- Builds the status badge ---
  Widget _buildStatusBadge({
    required String text,
    required IconData icon,
    required Color bgColor,
    required Color borderColor,
    required Color iconColor,
    required Color textColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: 8, vertical: 4), // Adjusted padding
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16), // More rounded
        border: Border.all(color: borderColor, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: iconColor, size: 14),
          const SizedBox(width: 4), // Reduced spacing
          Text(
            text,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w600, // Slightly bolder
              fontSize: 11,
              fontFamily: 'Work Sans',
            ),
          ),
        ],
      ),
    );
  }

  // --- Builds a group for a single Workout Session ---
  Widget _buildSessionGroup(
    ThemeData theme,
    WorkoutSession session,
    WorkoutLog workoutLog, // Pass the specific log for this session
    bool isSessionCompleted, // Using the day's completion status for now
    List<Map<String, dynamic>> exercises,
  ) {
    // Removed unused variable

    // Using Card for better elevation and structure
    return Card(
      elevation: 2.0, // Subtle elevation
      margin:
          const EdgeInsets.only(bottom: 16.0), // Space between session cards
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0), // Consistent rounding
        side: BorderSide(
            color: Colors.grey.shade200, width: 1), // Optional border
      ),
      color: Colors.white,
      child: Padding(
        // Add padding inside the Card
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // --- Session Header ---
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Session Title and Exercise Count
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        // When targetSessionId is provided, show 'Exercises' as the title
                        // This makes it clear that we're showing exercises for a specific session
                        widget.targetSessionId != null
                            ? 'Exercises'
                            : session.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Work Sans',
                          color: darkText,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (exercises.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Text(
                            '${exercises.length} Exercise${exercises.length == 1 ? "" : "s"}',
                            style: const TextStyle(
                              fontSize: 13,
                              fontFamily: 'Work Sans',
                              color: lightText,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // Action Button (Start or Completed Badge)
                if (_isFutureWorkout)
                  _buildStatusBadge(
                    // Show future badge if applicable
                    text: 'Scheduled', // Consistent text
                    icon: Icons.calendar_today_outlined, // Consistent icon
                    bgColor: Colors.blue.shade50,
                    borderColor: Colors.blue.shade200,
                    iconColor: Colors.blue.shade700,
                    textColor: Colors.blue.shade800,
                  )
                else if (isSessionCompleted) // Using day completion status
                  _buildStatusBadge(
                    // Show completed badge
                    text: 'Completed',
                    icon: Icons.check_circle_outline_rounded, // Consistent icon
                    bgColor: Colors.green.shade50,
                    borderColor: Colors.green.shade200,
                    iconColor: Colors.green.shade700, // Consistent color
                    textColor: Colors.green.shade800,
                  )
                else // Show session info badge instead of start button
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: Colors.blue.shade200, width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.info_outline,
                            color: Colors.blue.shade700, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          'Ready',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                            fontFamily: 'Work Sans',
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 20), // Increased space before exercise list

            // --- Exercise List for this Session ---
            if (exercises.isEmpty)
              _buildEmptyExercisesMessage(
                  showRetry:
                      false) // Show simpler message if session has no exercises
            else
              ListView.separated(
                // Use separated for dividers
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: exercises.length,
                itemBuilder: (context, index) {
                  final exerciseMap = exercises[index];
                  // Pass the exercise map and theme
                  return _buildExerciseCard(exerciseMap, theme);
                },
                separatorBuilder: (context, index) => Divider(
                  // Add subtle dividers
                  height: 16,
                  thickness: 1,
                  color: Colors.grey.shade100,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // --- Builds the individual Exercise Card (Elegant Design) ---
  Widget _buildExerciseCard(Map<String, dynamic> exercise, ThemeData theme) {
    // Extract data from the exercise map
    final WorkoutExerciseLog? exerciseInstance =
        exercise['exerciseInstance'] as WorkoutExerciseLog?;
    final WorkoutSection? workoutSection =
        exercise['workoutSection'] as WorkoutSection?;

    // Extract data safely
    final String name =
        exerciseInstance?.exerciseDetails?.name ?? 'Unknown Exercise';
    final String? durationText = exerciseInstance?.duration != null
        ? '${exerciseInstance!.duration}s'
        : null;
    final int? setsCount = exerciseInstance?.sets;
    final int? repsCount = exerciseInstance?.reps != null
        ? int.tryParse(exerciseInstance!.reps.toString())
        : null;
    final String typeText = workoutSection?.name ?? ''; // Section name
    final String imageUrl = exerciseInstance?.exerciseDetails?.imageUrl ?? '';

    // Check if this is a placeholder
    final bool isPlaceholder =
        exerciseInstance == null || workoutSection == null;

    // Create a clean, elegant card inspired by the provided design
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // Show more details or start this specific exercise
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Starting exercise: $name')),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Exercise image with rounded corners
                Container(
                  width: 96,
                  height: 96,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    color: Colors.grey.shade200,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24),
                    child: imageUrl.isNotEmpty
                        ? Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2.0,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      accentOrange),
                                ),
                              );
                            },
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Icon(Icons.fitness_center,
                                    color: Colors.grey.shade400, size: 32),
                              );
                            },
                          )
                        : Center(
                            child: Icon(
                              isPlaceholder
                                  ? Icons.help_outline
                                  : Icons.fitness_center,
                              color: Colors.grey.shade400,
                              size: 32,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                // Exercise details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Exercise name
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Work Sans',
                          color: darkText,
                          height: 1.2,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      // Exercise stats in a clean row
                      Row(
                        children: [
                          Icon(Icons.fitness_center,
                              size: 16, color: Colors.grey.shade600),
                          const SizedBox(width: 4),
                          Text(
                            setsCount != null ? '$setsCount sets' : 'No sets',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey.shade700,
                              fontFamily: 'Work Sans',
                              height: 1,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // Reps or duration
                      Row(
                        children: [
                          Icon(
                              repsCount != null
                                  ? Icons.repeat
                                  : Icons.timer_outlined,
                              size: 16,
                              color: Colors.grey.shade600),
                          const SizedBox(width: 4),
                          Text(
                            repsCount != null
                                ? '$repsCount reps each'
                                : (durationText ?? 'No duration'),
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey.shade700,
                              fontFamily: 'Work Sans',
                              height: 1,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Optional badge for new exercises or special types
                if (typeText.isNotEmpty && typeText.toLowerCase() != 'regular')
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      color: accentOrange,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      typeText,
                      style: const TextStyle(
                        color: Colors.white,
                        fontFamily: 'Work Sans',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Removed unused _buildExerciseImage method

  // --- Builds the message shown when no exercises/sessions are found ---
  Widget _buildEmptyExercisesMessage(
      {String? message, bool showRetry = false}) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(
            vertical: 40.0, horizontal: 20.0), // Reduced padding
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.list_alt_rounded,
                size: 60, color: Colors.grey.shade300), // Updated Icon
            const SizedBox(height: 16),
            Text(
              message ??
                  'No Exercises Listed', // Slightly different default message
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.w600,
                  color: darkText, // Use darkText
                  fontFamily: 'Work Sans'),
            ),
            const SizedBox(height: 8),
            if (message ==
                null) // Show default subtext only if no custom message
              const Text(
                'There are currently no exercises assigned to this session.', // More specific default subtext
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 14,
                    color: lightText, // Use lightText
                    fontFamily: 'Work Sans'),
              ),
            if (showRetry) ...[
              const SizedBox(height: 24), // Increased spacing
              TextButton.icon(
                icon: const Icon(Icons.refresh, size: 20),
                label: const Text('Try Reloading',
                    style: TextStyle(
                        fontFamily: 'Work Sans',
                        fontWeight: FontWeight.bold)), // Bolder text
                onPressed: _processWorkoutData, // Call the process function
                style: TextButton.styleFrom(
                  foregroundColor: accentOrange,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }
}

// Ensure these exist and are correctly defined/imported
// Replace with your actual model definitions

// We're using the models from models/workout.dart and models/workout_log.dart
// These are just placeholders for local use

class SetLog {} // Placeholder
