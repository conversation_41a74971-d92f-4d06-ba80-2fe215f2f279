import logging
from datetime import date
from django.utils import timezone # Import timezone
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action # Import action
from rest_framework.response import Response # Import Response
from django.shortcuts import get_object_or_404
from django.db.models import Q # Import Q
from django.db import models # Import models for JSONField
from ..models import MealPlan, UserMealPlan, DailyMealPlan, MealSchedule, MealLog, Meal # Import MealLog and Meal
from ..serializers import (
    MealPlanSerializer, UserMealPlanSerializer, DailyMealPlanSerializer,
    MealScheduleSerializer, MealSerializer # Import MealSerializer
)
from accounts.models import UserProfile
from django_filters import rest_framework as filters
from django_filters import CharFilter # Import CharFilter for JSONField filtering

logger = logging.getLogger(__name__)

# --- Filters ---
class MealPlanFilter(filters.FilterSet):
    class Meta:
        model = MealPlan
        fields = ['goal', 'is_keto', 'is_intermittent_fasting', 'gender', 'age_group']
        filter_overrides = {
            models.JSONField: {
                'filter_class': CharFilter,
                'extra': lambda f: {
                    'lookup_expr': 'exact',
                }
            }
        }

class DailyMealPlanFilter(filters.FilterSet):
    class Meta:
        model = DailyMealPlan
        fields = ['meal_plan', 'day_of_week']

class MealScheduleFilter(filters.FilterSet):
    user_meal_plan = filters.NumberFilter(field_name='user_meal_plan__id') # Filter by user plan ID
    meal_plan = filters.NumberFilter(field_name='meal_plan__id') # Filter by global plan ID

    class Meta:
        model = MealSchedule
        fields = ['week_number', 'user_meal_plan', 'meal_plan']


# --- ViewSets ---
class MealPlanViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = MealPlan.objects.all()
    serializer_class = MealPlanSerializer
    filterset_class = MealPlanFilter
    permission_classes = [permissions.IsAuthenticated]

class DailyMealPlanViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = DailyMealPlan.objects.all()
    serializer_class = DailyMealPlanSerializer
    filterset_class = DailyMealPlanFilter
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Filter based on plans assigned to the user
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        assigned_plan_ids = UserMealPlan.objects.filter(user=user_profile).values_list('meal_plan_id', flat=True)
        return DailyMealPlan.objects.filter(meal_plan_id__in=assigned_plan_ids)

class MealScheduleViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = MealSchedule.objects.all()
    serializer_class = MealScheduleSerializer
    filterset_class = MealScheduleFilter
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Base queryset
        queryset = MealSchedule.objects.all()

        # Ensure user is authenticated
        if not self.request.user or not self.request.user.is_authenticated:
            return queryset.none()

        user_profile = get_object_or_404(UserProfile, user=self.request.user)

        # Check if filtering by a specific user_meal_plan ID
        user_plan_id = self.request.query_params.get('user_meal_plan')
        if user_plan_id:
            # Ensure the requested user_meal_plan belongs to the current user
            try:
                user_mp = UserMealPlan.objects.get(pk=user_plan_id, user=user_profile)
                # Return only schedules linked to this specific user plan instance
                return queryset.filter(user_meal_plan_id=user_plan_id)
            except UserMealPlan.DoesNotExist:
                return queryset.none() # Or raise PermissionDenied
        else:
            # Default behavior: Show user's schedules + relevant global templates
            user_meal_plan_ids = UserMealPlan.objects.filter(user=user_profile).values_list('id', flat=True)
            global_meal_plan_ids = UserMealPlan.objects.filter(user=user_profile).values_list('meal_plan_id', flat=True)

            # Combine user-specific schedules and relevant global templates
            return queryset.filter(
                Q(user_meal_plan_id__in=user_meal_plan_ids) |
                (Q(user_meal_plan__isnull=True) & Q(meal_plan_id__in=global_meal_plan_ids))
            )

# --- ADD UserMealPlanViewSet ---
class UserMealPlanViewSet(viewsets.ModelViewSet):
    serializer_class = UserMealPlanSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Filter by the logged-in user's profile
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        # Add ordering to prevent pagination warnings
        return UserMealPlan.objects.filter(user=user_profile).order_by('-created_at')

    @action(detail=True, methods=['post'], url_path='clone')
    def clone(self, request, pk=None):
        """Clone the meal plan structure for this user meal plan"""
        user_meal_plan = self.get_object()

        # Ensure the plan belongs to the requesting user
        if user_meal_plan.user.user != request.user:
            return Response({"detail": "Not authorized to clone this meal plan."}, status=status.HTTP_403_FORBIDDEN)

        try:
            # Perform the deep clone operation
            from meals.models import deep_clone_meal_plan_for_user
            deep_clone_meal_plan_for_user(user_meal_plan)

            # Return success response
            return Response({
                "detail": "Meal plan successfully cloned",
                "user_meal_plan_id": user_meal_plan.id
            })
        except Exception as e:
            logger.error(f"Error cloning meal plan: {str(e)}")
            return Response({
                "detail": f"Error cloning meal plan: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # Add perform_create if needed to set user automatically (though usually handled by signals/questionnaire)
    # def perform_create(self, serializer):
    #     user_profile = get_object_or_404(UserProfile, user=self.request.user)
    #     serializer.save(user=user_profile)

    @action(detail=True, methods=['get'], url_path='today-meals')
    def today_meals(self, request, pk=None):
        """
        Retrieves the meals scheduled for the current day for this UserMealPlan,
        including their completion status based on MealLog entries for today.
        """
        user_meal_plan = self.get_object() # Gets the specific UserMealPlan instance
        user_profile = user_meal_plan.user

        # Ensure the plan belongs to the requesting user
        if user_profile.user != request.user:
            return Response({"detail": "Not authorized to view this meal plan."}, status=status.HTTP_403_FORBIDDEN)

        # Get the DailyMealPlan for the current day
        daily_plan = user_meal_plan.get_meals_for_current_day()

        if not daily_plan:
            logger.info(f"No DailyMealPlan found for today for UserMealPlan {user_meal_plan.id}")
            # Return empty list or specific message based on frontend needs
            return Response([], status=status.HTTP_200_OK)

        today_date = timezone.now().date()
        meals_for_today = []
        meal_ids_to_check = []

        # Collect meals and their types from the daily plan
        scheduled_meals = {
            'breakfast': daily_plan.breakfast,
            'lunch': daily_plan.lunch,
            'dinner': daily_plan.dinner
        }

        for meal_type, meal_instance in scheduled_meals.items():
            if meal_instance:
                meals_for_today.append({
                    'meal_type': meal_type,
                    'meal': meal_instance,
                    'is_completed': False # Default to not completed
                })
                meal_ids_to_check.append(meal_instance.id)

        # Query MealLog for completed meals today
        if meal_ids_to_check:
            completed_logs = MealLog.objects.filter(
                user=user_profile,
                meal_id__in=meal_ids_to_check,
                date=today_date,
                is_completed=True
            ).values_list('meal_id', flat=True) # Get IDs of completed meals

            completed_meal_ids = set(completed_logs)

            # Update completion status
            for meal_info in meals_for_today:
                if meal_info['meal'].id in completed_meal_ids:
                    meal_info['is_completed'] = True

        # Serialize the result
        # We need to serialize the Meal object within our custom structure
        result_data = []
        for meal_info in meals_for_today:
            meal_serializer = MealSerializer(meal_info['meal'], context={'request': request})
            result_data.append({
                'meal_type': meal_info['meal_type'],
                'meal_details': meal_serializer.data,
                'is_completed': meal_info['is_completed']
            })

        return Response(result_data)
