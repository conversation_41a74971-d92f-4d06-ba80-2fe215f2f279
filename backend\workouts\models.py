from django.db import models
from django.conf import settings
from django.utils import timezone
from datetime import date, timedelta, datetime # Import timedelta, datetime
from django.core.validators import MinValueValidator, MaxValueValidator
from multiselectfield import MultiSelectField
from . import constants # Keep local constants import
# FIX: Import accounts.constants specifically for shared choices
import accounts.constants
from django.db.models import Q # Import Q
import logging # Import logging

logger = logging.getLogger(__name__) # Define logger
User = settings.AUTH_USER_MODEL

# Helper function moved here from accounts/views.py
def deep_clone_workout_plan_for_user(user_wp):
    # This function uses a transaction to ensure all-or-nothing behavior
    from django.db import transaction
    from django.db.models import Max
    import time

    logger.info(f"Starting deep clone for UserWorkoutPlan {user_wp.id}")
    global_plan = user_wp.workout_plan
    # Add check for start_date
    if not user_wp.start_date:
        logger.error(f"Cannot clone for UserWor<PERSON>utPlan {user_wp.id}: start_date is not set.")
        return

    if not global_plan:
        logger.error(f"Cannot clone for UserWorkoutPlan {user_wp.id}: Global WorkoutPlan link is missing.")
        return

    # Use a transaction with a lock to prevent race conditions
    with transaction.atomic():
        # Get a lock on the user workout plan to prevent concurrent modifications
        # This helps prevent race conditions that could lead to duplicate schedules
        user_wp = UserWorkoutPlan.objects.select_for_update().get(id=user_wp.id)

        # Also lock the global plan to prevent concurrent modifications to template schedules
        global_plan = WorkoutPlan.objects.select_for_update().get(id=global_plan.id)

        # Add a small delay to ensure any other transactions have completed
        time.sleep(0.5)

        # Use Max to find the highest day number if needed for duplicate resolution
        cloned_days_map = {} # Map original WorkoutDay ID to cloned WorkoutDay instance

        # --- Deletion Step ---
        # Delete existing cloned components for this user plan.
        # Order matters: Delete objects that REFERENCE others first.
        # Schedules reference Days, Sessions reference Days, Sections reference Sessions, Instances reference Sections.
        schedule_count = WorkoutSchedule.objects.filter(user_workout_plan=user_wp).count()
        WorkoutSchedule.objects.filter(user_workout_plan=user_wp).delete() # Delete Schedules first
        ExerciseInstance.objects.filter(user_workout_plan=user_wp).delete() # Then Instances
        WorkoutSection.objects.filter(user_workout_plan=user_wp).delete() # Then Sections
        WorkoutSession.objects.filter(user_workout_plan=user_wp).delete() # Then Sessions
        WorkoutDay.objects.filter(user_workout_plan=user_wp).delete() # Finally Days
        logger.info(f"Deleted {schedule_count} existing schedules and other components for UserWorkoutPlan {user_wp.id}")

        # Double-check that all schedules were deleted
        remaining = WorkoutSchedule.objects.filter(user_workout_plan=user_wp).count()
        if remaining > 0:
            logger.error(f"CRITICAL ERROR: {remaining} schedules still exist after deletion for UserWorkoutPlan {user_wp.id}")
            # Force delete again
            WorkoutSchedule.objects.filter(user_workout_plan=user_wp).delete()

        # --- Cloning Step ---
        # 1. Clone WorkoutDays
        template_days = WorkoutDay.objects.filter(workout_plan=global_plan, user_workout_plan__isnull=True)
        logger.info(f"Found {template_days.count()} template WorkoutDays for Plan {global_plan.id}")

        # Get all template schedules to access their days mapping
        template_schedules = WorkoutSchedule.objects.filter(workout_plan=global_plan, user_workout_plan__isnull=True)

        # Create a mapping of day numbers to workout days from the days field in schedules
        day_number_to_workout_day = {}
        for schedule in template_schedules:
            if schedule.days:
                for day_num, day_id in schedule.days.items():
                    if day_id is not None:
                        try:
                            day = WorkoutDay.objects.get(id=day_id)
                            day_number_to_workout_day[int(day_num)] = day
                        except (WorkoutDay.DoesNotExist, ValueError):
                            pass

        # Pre-fetch existing user days to potentially update instead of creating
        existing_user_days = {day.day_number: day for day in WorkoutDay.objects.filter(user_workout_plan=user_wp)}

        for day_template in template_days:
            original_day_id = day_template.id
            day_number = day_template.day_number

            # Calculate the target date for this day based on user's start_date and day_number
            # day_number is 1-based, subtract 1 for timedelta calculation
            target_date = user_wp.start_date + timedelta(days=day_number - 1)

            # Check if a workout day with this workout_plan and day_number already exists in the global plan
            existing_global_day = WorkoutDay.objects.filter(
                workout_plan=day_template.workout_plan,
                day_number=day_number,
                user_workout_plan__isnull=True
            ).exclude(id=day_template.id).first()

            if existing_global_day:
                # If there's already a global day with this day_number, use a different day_number
                # Find the highest day_number for this workout_plan and increment it
                max_day_number = WorkoutDay.objects.filter(
                    workout_plan=day_template.workout_plan
                ).aggregate(Max('day_number'))['day_number__max'] or 0
                new_day_number = max_day_number + 1
                logger.warning(f"Found duplicate day_number {day_number} for workout_plan {day_template.workout_plan.id}, using {new_day_number} instead")
                day_number = new_day_number
                # Recalculate target_date if day_number changes
                target_date = user_wp.start_date + timedelta(days=day_number - 1)

            # Check if a user-specific day with this day_number already exists
            if day_number in existing_user_days:
                # Update the existing user day
                cloned_day = existing_user_days[day_number]
                logger.info(f"Updating existing WorkoutDay {cloned_day.id} for day {day_number}, date {target_date}")
                cloned_day.name = day_template.name
                cloned_day.description = day_template.description
                cloned_day.date = target_date # Set the calculated date
                cloned_day.day_of_week = day_template.day_of_week
                cloned_day.total_duration = day_template.total_duration
                cloned_day.fitness_level = day_template.fitness_level
                cloned_day.calories_burn_estimate = day_template.calories_burn_estimate
                cloned_day.cover_image = day_template.cover_image
                cloned_day.save()
            else:
                # Create new WorkoutDay instance
                logger.info(f"Creating new WorkoutDay for day {day_number}, date {target_date}")
                cloned_day = WorkoutDay.objects.create(
                    user_workout_plan = user_wp, # Link to the user's plan instance
                    workout_plan = None, # Don't link to the original template plan to avoid constraint violation
                    name = day_template.name,
                    description = day_template.description,
                    day_number = day_number,
                    date = target_date, # Set the calculated date
                    day_of_week = day_template.day_of_week,
                    total_duration = day_template.total_duration,
                    fitness_level = day_template.fitness_level,
                    calories_burn_estimate = day_template.calories_burn_estimate,
                    cover_image = day_template.cover_image  # Copy cover image
                )

            cloned_days_map[original_day_id] = cloned_day # Store mapping
            logger.info(f"Cloned/Updated WorkoutDay {original_day_id} to {cloned_day.id} for UserWorkoutPlan {user_wp.id}")

            # 2. Clone WorkoutSessions for each cloned WorkoutDay
            template_sessions = WorkoutSession.objects.filter(workout_day_id=original_day_id, user_workout_plan__isnull=True).order_by('order')
            logger.info(f"Found {template_sessions.count()} template WorkoutSessions for original Day {original_day_id}")

            for session_template in template_sessions:
                original_session_id = session_template.id

                # Create new WorkoutSession instance
                cloned_session = WorkoutSession.objects.create(
                    workout_day = cloned_day, # Link to the *cloned* day
                    user_workout_plan = user_wp, # Link to user plan
                    name = session_template.name,
                    description = session_template.description,
                    order = session_template.order,
                    duration = session_template.duration,
                    calories_burn_estimate = session_template.calories_burn_estimate
                )
                logger.info(f"Cloned WorkoutSession {original_session_id} to {cloned_session.id} for cloned Day {cloned_day.id}")

                # 3. Clone WorkoutSections for each cloned WorkoutSession
                template_sections = WorkoutSection.objects.filter(workout_session_id=original_session_id, user_workout_plan__isnull=True).order_by('order')
                logger.info(f"Found {template_sections.count()} template WorkoutSections for original Session {original_session_id}")

                for section_template in template_sections:
                    original_section_id = section_template.id
                    # Store related instances before clearing PK
                    template_instances = list(ExerciseInstance.objects.filter(workout_section_id=original_section_id, user_workout_plan__isnull=True).order_by('order'))

                    # Create new WorkoutSection instance
                    cloned_section = WorkoutSection.objects.create(
                        workout_session = cloned_session, # Link to the *cloned* session
                        user_workout_plan = user_wp, # Link to user plan
                        name = section_template.name,
                        section_type = section_template.section_type,
                        order = section_template.order,
                        duration = section_template.duration,
                        notes = section_template.notes
                    )
                    logger.info(f"Cloned WorkoutSection {original_section_id} to {cloned_section.id} for cloned Session {cloned_session.id}")

                    # 4. Clone ExerciseInstances for each cloned WorkoutSection
                    logger.info(f"Found {len(template_instances)} template ExerciseInstances for original Section {original_section_id}")
                    instances_to_create = []
                    for instance_template in template_instances:
                        instances_to_create.append(
                            ExerciseInstance(
                                workout_section = cloned_section, # Link to the *cloned* section
                                user_workout_plan = user_wp, # Link to user plan
                                exercise = instance_template.exercise, # Link to global Exercise
                                order = instance_template.order,
                                sets = instance_template.sets,
                                reps = instance_template.reps,
                                duration_seconds = instance_template.duration_seconds,
                                rest_seconds = instance_template.rest_seconds,
                                weight = instance_template.weight,
                                notes = instance_template.notes
                            )
                        )
                    if instances_to_create:
                        ExerciseInstance.objects.bulk_create(instances_to_create)
                        logger.info(f"Bulk created {len(instances_to_create)} ExerciseInstances for cloned Section {cloned_section.id}")

        # 4. Clone WorkoutSchedules, linking to *cloned* WorkoutDays
        # Get template schedules and sort by week number to ensure consistent processing
        # Use distinct to ensure we only get one schedule per week number
        template_schedules = list(WorkoutSchedule.objects.filter(
            workout_plan=global_plan,
            user_workout_plan__isnull=True
        ).order_by('week_number'))

        # Remove duplicates by keeping only the first schedule for each week number
        unique_template_schedules = []
        processed_week_numbers = set()

        for schedule in template_schedules:
            if schedule.week_number not in processed_week_numbers:
                unique_template_schedules.append(schedule)
                processed_week_numbers.add(schedule.week_number)
            else:
                logger.warning(f"Found duplicate template schedule for week {schedule.week_number} in global plan {global_plan.id}")

        logger.info(f"Found {len(template_schedules)} template schedules, {len(unique_template_schedules)} unique weeks for Plan {global_plan.id}")

        # Reset for creating user schedules
        processed_week_numbers = set()
        created_schedules = []

        # Create schedules one by one to ensure uniqueness
        for schedule_template in unique_template_schedules:
            try:
                # Double-check if a schedule with this week number already exists (should not happen due to deletion above)
                existing = WorkoutSchedule.objects.filter(
                    user_workout_plan=user_wp,
                    week_number=schedule_template.week_number
                ).first()

                if existing:
                    logger.warning(f"Found existing schedule for week {schedule_template.week_number} despite deletion. Updating instead of creating.")
                    # Update the existing schedule instead of creating a new one
                    existing.monday = cloned_days_map.get(schedule_template.monday_id)
                    existing.tuesday = cloned_days_map.get(schedule_template.tuesday_id)
                    existing.wednesday = cloned_days_map.get(schedule_template.wednesday_id)
                    existing.thursday = cloned_days_map.get(schedule_template.thursday_id)
                    existing.friday = cloned_days_map.get(schedule_template.friday_id)
                    existing.saturday = cloned_days_map.get(schedule_template.saturday_id)
                    existing.sunday = cloned_days_map.get(schedule_template.sunday_id)
                    existing.save()
                    created_schedules.append(existing)
                    logger.info(f"Updated existing WorkoutSchedule {existing.id} for week {existing.week_number}")
                else:
                    # Calculate the base day number for this week
                    base_day = (schedule_template.week_number - 1) * 7 + 1

                    # Map weekday fields to day numbers
                    weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                    weekday_values = {}

                    # First try to populate weekday fields from the days mapping
                    for i, field_name in enumerate(weekday_fields):
                        day_number = base_day + i
                        # Try to get the workout day from the day_number_to_workout_day mapping
                        if day_number in day_number_to_workout_day:
                            original_day = day_number_to_workout_day[day_number]
                            weekday_values[field_name] = cloned_days_map.get(original_day.id)
                        else:
                            # Fall back to the weekday fields if not found in the mapping
                            original_day_id = getattr(schedule_template, f"{field_name}_id")
                            weekday_values[field_name] = cloned_days_map.get(original_day_id)

                    # Create a new schedule with the populated weekday fields
                    new_schedule = WorkoutSchedule.objects.create(
                        user_workout_plan=user_wp, # Link to user plan
                        workout_plan=schedule_template.workout_plan, # Keep reference to global plan
                        week_number=schedule_template.week_number,
                        monday=weekday_values.get('monday'),
                        tuesday=weekday_values.get('tuesday'),
                        wednesday=weekday_values.get('wednesday'),
                        thursday=weekday_values.get('thursday'),
                        friday=weekday_values.get('friday'),
                        saturday=weekday_values.get('saturday'),
                        sunday=weekday_values.get('sunday')
                    )

                    # Also clone the days mapping
                    days_mapping = {}

                    # First try to use the template's days mapping
                    if schedule_template.days:
                        for day_num, day_id in schedule_template.days.items():
                            if day_id is not None:
                                original_day = WorkoutDay.objects.filter(id=day_id).first()
                                if original_day:
                                    cloned_day_id = cloned_days_map.get(original_day.id)
                                    if cloned_day_id:
                                        days_mapping[day_num] = cloned_day_id.id
                            else:
                                days_mapping[day_num] = None

                    # If no days mapping was found, create one from the weekday fields
                    if not days_mapping:
                        for i, field_name in enumerate(weekday_fields):
                            day_number = base_day + i
                            workout_day = weekday_values.get(field_name)
                            if workout_day:
                                days_mapping[str(day_number)] = workout_day.id
                            else:
                                days_mapping[str(day_number)] = None

                    # Save the days mapping
                    new_schedule.days = days_mapping
                    new_schedule.save()
                    created_schedules.append(new_schedule)
                    logger.info(f"Created new WorkoutSchedule {new_schedule.id} for week {new_schedule.week_number}")
            except Exception as e:
                logger.error(f"Error creating/updating WorkoutSchedule for week {schedule_template.week_number}: {str(e)}", exc_info=True)

        # Now, update each schedule with day number mapping
        for schedule in created_schedules:
            # Create a mapping of day numbers to workout day IDs
            days_mapping = {}

            # Calculate the base day number for this week
            base_day = (schedule.week_number - 1) * 7 + 1

            # Map weekday fields to day numbers
            weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            for i, field_name in enumerate(weekday_fields):
                day_number = base_day + i
                workout_day = getattr(schedule, field_name)
                if workout_day:
                    days_mapping[str(day_number)] = workout_day.id

                    # For the first week, ensure day 1 is mapped to the first workout day
                    if schedule.week_number == 1 and i == 0:
                        # Explicitly map day 1 to the first workout day
                        days_mapping['1'] = workout_day.id
                        logger.info(f"Explicitly mapped day 1 to workout day {workout_day.id}")
                else:
                    # Explicitly mark this as a rest day (null value)
                    days_mapping[str(day_number)] = None
                    logger.info(f"Explicitly mapped day {day_number} as a rest day (null value)")

            # Also add mapping by day-in-week (1-7) for backward compatibility
            for i, field_name in enumerate(weekday_fields):
                day_in_week = i + 1
                workout_day = getattr(schedule, field_name)
                if str(day_in_week) not in days_mapping:
                    if workout_day:
                        days_mapping[str(day_in_week)] = workout_day.id
                    else:
                        # Explicitly mark this as a rest day (null value)
                        days_mapping[str(day_in_week)] = None

            # Update the schedule with the days mapping
            if days_mapping:
                schedule.days = days_mapping
                schedule.save(update_fields=['days'])
                logger.info(f"Updated WorkoutSchedule {schedule.id} with days mapping: {days_mapping}")

        # Final verification step: Check for any duplicate schedules that might have been created
        # This is a critical safety check to ensure data integrity
        final_schedules = WorkoutSchedule.objects.filter(user_workout_plan=user_wp)
        week_counts = {}
        for schedule in final_schedules:
            week_counts[schedule.week_number] = week_counts.get(schedule.week_number, 0) + 1

        duplicate_found = False
        for week, count in week_counts.items():
            if count > 1:
                duplicate_found = True
                logger.error(f"DUPLICATE DETECTED: Week {week} has {count} schedules for UserWorkoutPlan {user_wp.id}")
                # Keep only the most recently created schedule for this week
                duplicate_schedules = list(WorkoutSchedule.objects.filter(
                    user_workout_plan=user_wp,
                    week_number=week
                ).order_by('-id'))

                # Keep the first one (most recent), delete the rest
                for schedule in duplicate_schedules[1:]:
                    logger.warning(f"Deleting duplicate schedule {schedule.id} for week {week}")
                    schedule.delete()

        if duplicate_found:
            # Verify that duplicates were actually removed
            final_check = WorkoutSchedule.objects.filter(user_workout_plan=user_wp)
            week_counts = {}
            for schedule in final_check:
                week_counts[schedule.week_number] = week_counts.get(schedule.week_number, 0) + 1

            for week, count in week_counts.items():
                if count > 1:
                    logger.critical(f"CRITICAL ERROR: Failed to remove duplicates for week {week}. Still has {count} schedules.")
                    # Last resort: delete all schedules for this week and recreate one
                    WorkoutSchedule.objects.filter(user_workout_plan=user_wp, week_number=week).delete()
                    # We won't recreate here as it might cause more issues - better to have missing data than corrupt data

    logger.info(f"Finished deep clone for UserWorkoutPlan {user_wp.id}")


class Equipment(models.Model):
    """Model for workout equipment"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='equipment/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

# Removing UserEquipment as per user request
# class UserEquipment(models.Model):
#     ... (removed) ...

class Exercise(models.Model):
    """Model for individual exercises"""
    # MUSCLE_GROUP_CHOICES defined in workouts.constants
    # EQUIPMENT_CHOICES defined in workouts.constants
    # FITNESS_LEVEL_CHOICES defined in accounts.constants

    name = models.CharField(max_length=100)
    description = models.TextField()
    muscle_group = models.CharField(max_length=50, choices=constants.MUSCLE_GROUP_CHOICES)
    # FIX: Use accounts.constants for FITNESS_LEVEL_CHOICES
    fitness_level = models.CharField(max_length=20, choices=accounts.constants.FITNESS_LEVEL_CHOICES)
    equipment_required = models.CharField(max_length=50, choices=constants.EQUIPMENT_CHOICES, default='NONE')
    cover_image = models.ImageField(upload_to='exercises/images/', blank=True, null=True, help_text="Upload a cover image for the exercise")
    media_file = models.FileField(upload_to='exercises/gifs/', blank=True, null=True, help_text="Upload a video or GIF demonstration") # Changed upload_to to gifs
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class WorkoutPlan(models.Model):
    """Model for workout plan templates"""
    # GOAL_CHOICES defined in workouts.constants
    # LOCATION_CHOICES defined in accounts.constants
    # WORKOUT_DAYS_CHOICES defined in accounts.constants
    # GENDER_CHOICES defined in accounts.constants
    # AGE_GROUP_CHOICES defined in accounts.constants
    # FITNESS_LEVEL_CHOICES defined in accounts.constants

    name = models.CharField(max_length=200)
    description = models.TextField()
    duration_weeks = models.IntegerField(default=4)
    workouts_per_week = models.IntegerField(default=3)
    # FIX: Use accounts.constants
    fitness_level = models.CharField(max_length=20, choices=accounts.constants.FITNESS_LEVEL_CHOICES, default='BEGINNER')
    goal = models.CharField(max_length=20, choices=constants.WORKOUT_GOAL_CHOICES, default='MAINTAIN_HEALTH') # Use local workout goal choices
    # FIX: Use accounts.constants
    location = models.CharField(max_length=20, choices=accounts.constants.WORKOUT_LOCATION_CHOICES, default='HOME')
    # FIX: Use accounts.constants
    workout_days = models.JSONField(default=list, blank=True)
    requires_equipment = models.BooleanField(default=False)
    # FIX: Use accounts.constants
    health_conditions_allowed = MultiSelectField(
        choices=accounts.constants.HEALTH_CONDITIONS,
        max_choices=len(accounts.constants.HEALTH_CONDITIONS),
        default=['NONE'],
        blank=True
    )
    # FIX: Use accounts.constants
    gender = models.CharField(max_length=10, choices=accounts.constants.GENDER_CHOICES, default='UNISEX')
    # FIX: Use accounts.constants
    age_group = models.JSONField(default=list, blank=True)
    # image_url = models.URLField(max_length=500, blank=True, null=True, help_text="URL to workout plan preview image (Optional)") # Removed
    cover_image = models.ImageField(upload_to='workout_plans/covers/', blank=True, null=True, help_text="Upload a cover image for the plan")
    criteria = models.JSONField(default=dict, blank=True, null=True, help_text="JSON field to store criteria for plan assignment")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class WorkoutVideo(models.Model):
    """Model for videos associated with workout plans"""
    workout_plan = models.ForeignKey(WorkoutPlan, on_delete=models.CASCADE, related_name='videos')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    video_file = models.FileField(upload_to='workout_videos/', help_text="Upload workout video file")
    thumbnail = models.ImageField(upload_to='workout_videos/thumbnails/', blank=True, null=True, help_text="Upload video thumbnail")
    duration_seconds = models.IntegerField(null=True, blank=True, help_text="Video duration in seconds")
    order = models.IntegerField(default=1, help_text="Display order (priority)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'created_at']
        constraints = [
            models.UniqueConstraint(fields=['workout_plan', 'order'], name='unique_video_order_per_plan')
        ]

    def __str__(self):
        return f"{self.workout_plan.name} - {self.title}"

class WorkoutDay(models.Model):
    """Model for a specific day within a workout plan"""
    # DAY_OF_WEEK_CHOICES defined in workouts.constants
    # FITNESS_LEVEL_CHOICES defined in accounts.constants

    # Link to the original template plan (nullable for cloned instances)
    workout_plan = models.ForeignKey(WorkoutPlan, on_delete=models.SET_NULL, null=True, blank=True, related_name='template_workout_days')
    # Link to the specific user plan instance (null for templates)
    user_workout_plan = models.ForeignKey('UserWorkoutPlan', on_delete=models.CASCADE, null=True, blank=True, related_name='cloned_workout_days')

    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    day_number = models.IntegerField(help_text="Day number within the plan (e.g., 1, 2, 3)")
    date = models.DateField(null=True, blank=True, help_text="Actual date for this workout day (only for user-specific plans)")
    day_of_week = models.IntegerField(choices=constants.DAY_OF_WEEK_CHOICES, null=True, blank=True)
    total_duration = models.IntegerField(help_text="Estimated total duration in minutes", null=True, blank=True)
    # FIX: Use accounts.constants
    fitness_level = models.CharField(max_length=20, choices=accounts.constants.FITNESS_LEVEL_CHOICES, default='BEGINNER')
    calories_burn_estimate = models.IntegerField(null=True, blank=True)
    cover_image = models.ImageField(upload_to='workout_days/covers/', null=True, blank=True, help_text="Upload a cover image for this workout day")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        # unique_together = ('workout_plan', 'day_number') # Remove template constraint
        # Add constraint for user-specific cloned days
        constraints = [
            models.UniqueConstraint(fields=['user_workout_plan', 'day_number'], condition=Q(user_workout_plan__isnull=False), name='unique_user_workout_day_number'),
            models.UniqueConstraint(fields=['workout_plan', 'day_number'], condition=Q(user_workout_plan__isnull=True), name='unique_template_workout_day_number')
        ]
        ordering = ['day_number']

    def __str__(self):
        if self.user_workout_plan:
            return f"{self.user_workout_plan} - Day {self.day_number}: {self.name}"
        return f"{self.workout_plan.name} - Day {self.day_number}: {self.name}"

class WorkoutSession(models.Model):
    """Model for workout sessions within a day (e.g., Morning Cardio, Evening Strength)"""
    # Link to the parent workout day (template or cloned)
    workout_day = models.ForeignKey(WorkoutDay, on_delete=models.CASCADE, related_name='sessions')
    # Direct link to user plan for easier querying of cloned sessions (null for templates)
    user_workout_plan = models.ForeignKey('UserWorkoutPlan', on_delete=models.CASCADE, null=True, blank=True, related_name='cloned_workout_sessions')

    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    order = models.IntegerField(help_text="Order of the session within the day")
    duration = models.IntegerField(help_text="Estimated duration in minutes", null=True, blank=True)
    scheduled_time = models.TimeField(null=True, blank=True, help_text="Scheduled time for this workout session (24-hour format)")
    calories_burn_estimate = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['workout_day', 'order'], name='unique_session_order_per_day')
        ]
        ordering = ['order']

    def __str__(self):
        if self.user_workout_plan:
            return f"{self.user_workout_plan} - Day {self.workout_day.day_number} - Session {self.order}: {self.name}"
        return f"{self.workout_day} - Session {self.order}: {self.name}"

class WorkoutSection(models.Model):
    """Model for sections within a workout session (e.g., Warmup, Main, Cooldown)"""
    # SECTION_TYPE_CHOICES defined in workouts.constants

    # Link to the parent workout session (template or cloned)
    workout_session = models.ForeignKey(WorkoutSession, on_delete=models.CASCADE, related_name='sections', null=True, blank=True)
    # Direct link to user plan for easier querying of cloned sections (null for templates)
    user_workout_plan = models.ForeignKey('UserWorkoutPlan', on_delete=models.CASCADE, null=True, blank=True, related_name='cloned_workout_sections')

    name = models.CharField(max_length=100)
    section_type = models.CharField(max_length=10, choices=constants.WORKOUT_SECTION_TYPE_CHOICES, default='MAIN')
    order = models.IntegerField(help_text="Order of the section within the day")
    duration = models.IntegerField(help_text="Estimated duration in minutes", null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
             models.UniqueConstraint(fields=['workout_session', 'order'], name='unique_section_order_per_session')
        ]
        ordering = ['order']

    def __str__(self):
        # Update str to reflect potential user plan link
        if self.user_workout_plan:
             return f"{self.user_workout_plan} - Session {self.workout_session.name} - Section {self.order}: {self.name}"
        return f"{self.workout_session} - Section {self.order}: {self.name}"

class ExerciseInstance(models.Model):
    """Specific instance of an exercise within a workout section"""
    # Link to the parent workout section (template or cloned)
    workout_section = models.ForeignKey(WorkoutSection, on_delete=models.CASCADE, related_name='exercises')
     # Direct link to user plan for easier querying of cloned instances (null for templates)
    user_workout_plan = models.ForeignKey('UserWorkoutPlan', on_delete=models.CASCADE, null=True, blank=True, related_name='cloned_exercise_instances')

    exercise = models.ForeignKey(Exercise, on_delete=models.CASCADE) # Link to the global Exercise definition
    order = models.IntegerField(help_text="Order of the exercise within the section")
    sets = models.IntegerField(null=True, blank=True, help_text="Number of sets (for rep-based exercises)")
    reps = models.CharField(max_length=20, null=True, blank=True, help_text="Rep range (e.g., '8-12') or specific count (e.g., '15') (for rep-based exercises)")
    duration_seconds = models.IntegerField(null=True, blank=True, help_text="Duration in seconds (for time-based exercises like planks or holds)")
    rest_seconds = models.IntegerField(null=True, blank=True, help_text="Rest time after this exercise (in seconds)")
    weight = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True, help_text="Weight in kg or lbs (optional)")
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
             models.UniqueConstraint(fields=['workout_section', 'order'], name='unique_exercise_order_per_section')
        ]
        ordering = ['order']

    def __str__(self):
        # Update str to reflect potential user plan link
        try:
            if hasattr(self, 'user_workout_plan') and self.user_workout_plan:
                return f"{self.user_workout_plan} - ... - Ex {self.order}: {self.exercise.name}" # Abbreviate day/section
        except Exception as e:
            # Handle the case when user_workout_plan is not available
            pass
        return f"{self.workout_section} - Ex {self.order}: {self.exercise.name}"

class UserWorkoutPlan(models.Model):
    """User-specific instance of a workout plan"""
    user = models.ForeignKey('accounts.UserProfile', on_delete=models.CASCADE, related_name='user_workout_plans')
    workout_plan = models.ForeignKey(WorkoutPlan, on_delete=models.CASCADE)
    start_date = models.DateField(default=date.today) # FIX: Use date.today for DateField default
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    # version = models.IntegerField(default=1) # Removed
    # parent_plan = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, related_name='derived_plans') # Removed
    is_modified = models.BooleanField(default=False)
    has_completed_one_cycle = models.BooleanField(default=False, help_text='Indicates if the user has completed at least one full cycle of the plan')
    modifications = models.JSONField(default=dict, blank=True) # Allow blank dict
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Check if this is a modification being saved
        is_modification = kwargs.pop('is_modification', False) or self.is_modified

        # Calculate end date if not set or if this is a modification
        if (not self.end_date or is_modification) and self.workout_plan and self.workout_plan.duration_weeks:
            weeks_duration = self.workout_plan.duration_weeks
            # Ensure start_date is a date object before adding timedelta
            start_date_obj = self.start_date
            if isinstance(start_date_obj, datetime):
                start_date_obj = start_date_obj.date()
            elif not isinstance(start_date_obj, date):
                 start_date_obj = date.today() # Fallback if somehow not a date/datetime

            # If this is a modification and we already have an end date, extend it
            if is_modification and self.end_date:
                # Calculate the original duration
                original_duration = (self.end_date - start_date_obj).days
                # Add an extra week to the end date
                self.end_date = self.end_date + timedelta(weeks=1)
                logger.info(f"Extended end date for UserWorkoutPlan {self.id} due to modification")
            else:
                # Set initial end date
                self.end_date = start_date_obj + timedelta(weeks=weeks_duration)

        # Removed version logic
        super().save(*args, **kwargs)

    def get_current_day(self):
        """Calculate the current program day number (1-indexed)."""
        # Get the user's progress to determine the current workout day
        from accounts.models import UserProgress

        try:
            # Get the user's progress
            progress = UserProgress.objects.get(user=self.user)

            # If workout_day is set, use that value
            if hasattr(progress, 'workout_day') and progress.workout_day is not None:
                logger.info(f"Using workout_day from UserProgress: {progress.workout_day}")
                return progress.workout_day
        except UserProgress.DoesNotExist:
            logger.warning(f"No UserProgress found for user {self.user.id}")
            pass
        except Exception as e:
            logger.error(f"Error getting UserProgress: {e}")
            pass

        # Fallback to calculating based on start date if progress not available
        today = timezone.now().date()
        start_date_obj = self.start_date
        if isinstance(start_date_obj, datetime): start_date_obj = start_date_obj.date()
        elif not isinstance(start_date_obj, date): start_date_obj = date.today() # Fallback

        if today < start_date_obj: return 0 # Plan hasn't started
        if self.end_date and today > self.end_date: return (self.end_date - start_date_obj).days + 1 # Plan ended

        # FIX: For plans created today, always return day 1
        if today == start_date_obj:
            logger.info(f"Plan created today ({today}). Returning day 1.")
            return 1

        # Calculate days since start (1-indexed)
        days_since_start = (today - start_date_obj).days + 1

        # Log the calculation for debugging
        logger.info(f"Calculated current day for plan {self.id}: today={today}, start_date={start_date_obj}, days_since_start={days_since_start}")

        return days_since_start

    def get_workout_day_for_current_day(self):
        """Get the user-specific WorkoutDay scheduled for today's date."""
        today = timezone.now().date()
        logger.info(f"get_workout_day_for_current_day: UserWorkoutPlan {self.id}, searching for WorkoutDay with date {today}")

        try:
            # Directly query the WorkoutDay for the user's plan and today's date
            workout_day = WorkoutDay.objects.get(
                user_workout_plan=self,
                date=today
            )
            logger.info(f"get_workout_day_for_current_day: Found WorkoutDay {workout_day.id} ({workout_day.name}) for date {today}")
            return workout_day
        except WorkoutDay.DoesNotExist:
            logger.warning(f"get_workout_day_for_current_day: No WorkoutDay found for UserWorkoutPlan {self.id} and date {today}")
            return None
        except WorkoutDay.MultipleObjectsReturned:
            # This case should ideally not happen if cloning logic is correct.
            logger.error(f"CRITICAL: Multiple WorkoutDay objects found for UserWorkoutPlan {self.id} and date {today}. Returning the first one.")
            return WorkoutDay.objects.filter(user_workout_plan=self, date=today).first()
        except Exception as e:
            logger.error(f"Error fetching WorkoutDay for UserWorkoutPlan {self.id}, date {today}: {str(e)}")
            return None

    def __str__(self):
        return f"{self.user.user.username}'s {self.workout_plan.name}"

    # --- UPDATED METHOD ---
    def sync_with_global(self, overwrite_all=False):
        """
        Syncs this user workout plan instance with its parent global WorkoutPlan template.
        If overwrite_all is True or is_modified is False, performs a deep re-clone.

        Args:
            overwrite_all (bool): If True, sync occurs even if is_modified is True.
                                  If False, sync only occurs if is_modified is False.

        Returns:
            bool: True if syncing occurred, False otherwise.
        """
        if not overwrite_all and self.is_modified:
            logger.info(f"Skipping sync for modified UserWorkoutPlan {self.id} (overwrite_all=False)")
            return False # Do not sync if modified and overwrite is False

        logger.info(f"Syncing UserWorkoutPlan {self.id} with global plan {self.workout_plan.id} (overwrite_all={overwrite_all})")

        # Check if we have a valid global plan
        if not self.workout_plan:
            logger.error(f"Cannot sync UserWorkoutPlan {self.id}: No global plan linked")
            return False

        try:
            # Use a transaction to ensure all-or-nothing behavior
            from django.db import transaction
            import time

            # Add a small delay to stagger concurrent sync operations
            time.sleep(0.2)

            with transaction.atomic():
                # Get a lock on this user workout plan to prevent concurrent modifications
                user_wp = UserWorkoutPlan.objects.select_for_update().get(id=self.id)

                # Perform deep re-clone (deletes old user items, clones new from template)
                deep_clone_workout_plan_for_user(user_wp)

                # Reset modification status after successful sync/clone
                user_wp.is_modified = False
                user_wp.modifications = {}
                user_wp.save(update_fields=['is_modified', 'modifications']) # Save only these fields

                # Update self with the changes
                self.is_modified = False
                self.modifications = {}

            # Final verification outside the transaction
            # Check for any duplicate schedules that might have been created
            schedules = WorkoutSchedule.objects.filter(user_workout_plan=self)
            week_counts = {}
            for schedule in schedules:
                week_counts[schedule.week_number] = week_counts.get(schedule.week_number, 0) + 1

            # Check for and fix any duplicates
            for week, count in week_counts.items():
                if count > 1:
                    logger.error(f"DUPLICATE DETECTED after sync: Week {week} has {count} schedules for UserWorkoutPlan {self.id}")
                    # This should never happen, but if it does, we'll clean it up
                    with transaction.atomic():
                        # Keep only the most recently created schedule for this week
                        duplicate_schedules = list(WorkoutSchedule.objects.filter(
                            user_workout_plan=self,
                            week_number=week
                        ).order_by('-id'))

                        # Keep the first one (most recent), delete the rest
                        for schedule in duplicate_schedules[1:]:
                            logger.warning(f"Deleting duplicate schedule {schedule.id} for week {week} after sync")
                            schedule.delete()

            logger.info(f"UserWorkoutPlan {self.id} sync completed successfully.")
            return True # Indicate that syncing occurred
        except Exception as e:
            logger.error(f"Error syncing UserWorkoutPlan {self.id}: {str(e)}", exc_info=True)
            # The transaction will automatically roll back any changes
            return False # Indicate that syncing failed
    # --- END UPDATED METHOD ---

    def sync_future_days(self):
        """
        Syncs only future days from the global plan, preserving past and current day modifications.

        Returns:
            bool: True if syncing occurred, False otherwise.
        """
        logger.info(f"Syncing future days for UserWorkoutPlan {self.id} with global plan {self.workout_plan.id}")

        # Calculate current day in the program
        current_day = self.get_current_day()
        logger.info(f"Current day in program: {current_day}")

        # Get all schedules for this user plan
        user_schedules = WorkoutSchedule.objects.filter(user_workout_plan=self)

        # For each schedule, sync only future days
        for user_schedule in user_schedules:
            # Get the corresponding global schedule
            global_schedule = WorkoutSchedule.objects.filter(
                workout_plan=self.workout_plan,
                week_number=user_schedule.week_number,
                user_workout_plan__isnull=True
            ).first()

            if not global_schedule:
                logger.warning(f"No global schedule found for week {user_schedule.week_number}")
                continue

            # Calculate which days in this week are in the future
            week_start_day = (user_schedule.week_number - 1) * 7 + 1

            # Map of day fields to their position in the week (0-6)
            day_fields = {
                'monday': 0,
                'tuesday': 1,
                'wednesday': 2,
                'thursday': 3,
                'friday': 4,
                'saturday': 5,
                'sunday': 6
            }

            # For each day of the week
            for day_field, day_offset in day_fields.items():
                day_number = week_start_day + day_offset

                # Only sync future days
                if day_number > current_day:
                    logger.info(f"Syncing future day {day_number} ({day_field}) in week {user_schedule.week_number}")

                    # Get the global day
                    global_day = getattr(global_schedule, day_field)

                    if global_day:
                        # Clone the global day for this user if it's a future day
                        from workouts.utils import clone_workout_day_for_user
                        user_day = clone_workout_day_for_user(global_day, self)

                        # Update the user schedule with the new day
                        setattr(user_schedule, day_field, user_day)
                    else:
                        # If global day is None, set user day to None as well
                        setattr(user_schedule, day_field, None)

            # Update the days mapping
            days_mapping = user_schedule.days or {}

            # Update the days mapping for future days
            for day_field, day_offset in day_fields.items():
                day_number = week_start_day + day_offset

                # Only update future days
                if day_number > current_day:
                    workout_day = getattr(user_schedule, day_field)
                    if workout_day:
                        days_mapping[str(day_number)] = workout_day.id
                    else:
                        # Explicitly mark as rest day
                        days_mapping[str(day_number)] = None

            # Save the updated schedule with the new days mapping
            user_schedule.days = days_mapping
            user_schedule.save()

        # Mark as modified since we've made selective changes
        self.is_modified = True
        self.save(update_fields=['is_modified'])

        logger.info(f"Future days sync completed for UserWorkoutPlan {self.id}")
        return True

class WorkoutSchedule(models.Model):
    """Weekly schedule template OR user-specific override"""
    workout_plan = models.ForeignKey(WorkoutPlan, on_delete=models.CASCADE, related_name='template_schedules')
    user_workout_plan = models.ForeignKey(UserWorkoutPlan, on_delete=models.CASCADE, null=True, blank=True, related_name='schedules')
    week_number = models.IntegerField(default=1)

    @classmethod
    def cleanup_duplicates(cls, workout_plan=None, user_workout_plan=None):
        """Clean up duplicate schedules for a workout plan or user workout plan"""
        import logging
        logger = logging.getLogger(__name__)

        # Determine which plan to clean up
        if workout_plan:
            # Clean up global plan schedules
            schedules = cls.objects.filter(workout_plan=workout_plan, user_workout_plan__isnull=True)
            plan_type = "global"
            plan_id = workout_plan.id
        elif user_workout_plan:
            # Clean up user plan schedules
            schedules = cls.objects.filter(user_workout_plan=user_workout_plan)
            plan_type = "user"
            plan_id = user_workout_plan.id
        else:
            logger.error("cleanup_duplicates called without specifying a plan")
            return 0

        # Group schedules by week number
        week_schedules = {}
        for schedule in schedules:
            if schedule.week_number not in week_schedules:
                week_schedules[schedule.week_number] = []
            week_schedules[schedule.week_number].append(schedule)

        # Find and delete duplicates
        deleted_count = 0
        for week_number, week_list in week_schedules.items():
            if len(week_list) > 1:
                logger.warning(f"Found {len(week_list)} schedules for week {week_number} in {plan_type} plan {plan_id}")

                # Keep the most recently created schedule (highest ID)
                week_list.sort(key=lambda s: s.id, reverse=True)
                # First item in the sorted list is the one we'll keep

                # Delete the rest
                for schedule in week_list[1:]:
                    logger.info(f"Deleting duplicate schedule {schedule.id} for week {week_number}")
                    schedule.delete()
                    deleted_count += 1

        return deleted_count
    # Weekday fields for backward compatibility
    monday = models.ForeignKey(WorkoutDay, on_delete=models.SET_NULL, null=True, blank=True, related_name='monday_workouts')
    tuesday = models.ForeignKey(WorkoutDay, on_delete=models.SET_NULL, null=True, blank=True, related_name='tuesday_workouts')
    wednesday = models.ForeignKey(WorkoutDay, on_delete=models.SET_NULL, null=True, blank=True, related_name='wednesday_workouts')
    thursday = models.ForeignKey(WorkoutDay, on_delete=models.SET_NULL, null=True, blank=True, related_name='thursday_workouts')
    friday = models.ForeignKey(WorkoutDay, on_delete=models.SET_NULL, null=True, blank=True, related_name='friday_workouts')
    saturday = models.ForeignKey(WorkoutDay, on_delete=models.SET_NULL, null=True, blank=True, related_name='saturday_workouts')
    sunday = models.ForeignKey(WorkoutDay, on_delete=models.SET_NULL, null=True, blank=True, related_name='sunday_workouts')
    # New field for day number mapping
    days = models.JSONField(blank=True, null=True, default=dict, help_text='JSON mapping of day numbers to workout day IDs')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['user_workout_plan', 'week_number'], condition=Q(user_workout_plan__isnull=False), name='unique_user_workout_schedule_per_week'),
            models.UniqueConstraint(fields=['workout_plan', 'week_number'], condition=Q(user_workout_plan__isnull=True), name='unique_template_workout_schedule_per_week')
        ]
        ordering = ['week_number']

    def __str__(self):
        if self.user_workout_plan:
            return f"{self.user_workout_plan} - Week {self.week_number}"
        return f"{self.workout_plan.name} Template - Week {self.week_number}"

class WorkoutLog(models.Model):
    """Log of a user's completed workout day"""
    # FEELING_CHOICES defined in workouts.constants
    # INTENSITY_CHOICES defined in workouts.constants

    user = models.ForeignKey('accounts.UserProfile', on_delete=models.CASCADE, related_name='workout_logs')
    workout_day = models.ForeignKey(WorkoutDay, on_delete=models.CASCADE)
    # workout_session = models.ForeignKey(WorkoutSession, on_delete=models.CASCADE, null=True, blank=True) # Removed
    date = models.DateField(default=date.today) # FIX: Use date.today for DateField default
    is_completed = models.BooleanField(default=False)
    completion_time = models.DateTimeField(null=True, blank=True)
    calories_burned = models.IntegerField(null=True, blank=True)
    duration_minutes = models.IntegerField(null=True, blank=True)
    feeling = models.CharField(max_length=10, choices=constants.WORKOUT_LOG_FEELING_CHOICES, null=True, blank=True) # Use local constant
    intensity = models.CharField(max_length=10, choices=constants.WORKOUT_LOG_INTENSITY_CHOICES, null=True, blank=True) # Use local constant
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # TODO: Add fields for tracking progress within the day (e.g., completed_sessions_count)
    # or calculate this dynamically.

    class Meta:
        # Include workout_session in unique_together to match migration 0010
        # unique_together = ('user', 'workout_day', 'workout_session', 'date') # Old constraint
        unique_together = ('user', 'workout_day', 'date') # New constraint
        ordering = ['-date']

    def __str__(self):
        return f"{self.user.user.username} - {self.workout_day.name} ({self.date}) {'Completed' if self.is_completed else 'Pending'}"

# NEW Model: WorkoutSessionLog
class WorkoutSessionLog(models.Model):
    """Log of a user's completed workout session within a workout day log"""
    # FEELING_CHOICES defined in workouts.constants
    # INTENSITY_CHOICES defined in workouts.constants

    workout_log = models.ForeignKey(WorkoutLog, on_delete=models.CASCADE, related_name='session_logs')
    workout_session = models.ForeignKey(WorkoutSession, on_delete=models.CASCADE, related_name='session_logs')
    # user = models.ForeignKey('accounts.UserProfile', on_delete=models.CASCADE, related_name='workout_session_logs') # User is accessible via workout_log
    date = models.DateField(default=date.today) # Keep date for potential direct queries, synced with WorkoutLog
    is_completed = models.BooleanField(default=False)
    completion_time = models.DateTimeField(null=True, blank=True)
    duration_minutes = models.IntegerField(null=True, blank=True)
    calories_burned = models.IntegerField(null=True, blank=True)
    feeling = models.CharField(max_length=10, choices=constants.WORKOUT_LOG_FEELING_CHOICES, null=True, blank=True)
    intensity = models.CharField(max_length=10, choices=constants.WORKOUT_LOG_INTENSITY_CHOICES, null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('workout_log', 'workout_session') # Unique session per day log
        ordering = ['-date', 'workout_session__order']

    def __str__(self):
        return f"Session Log for {self.workout_log.user.user.username} - Session: {self.workout_session.name} ({self.date}) {'Completed' if self.is_completed else 'Pending'}"

class ExerciseLog(models.Model):
    """Log of a specific exercise within a completed workout log"""
    # workout_log = models.ForeignKey(WorkoutLog, on_delete=models.CASCADE, related_name='exercise_logs') # Changed
    workout_session_log = models.ForeignKey(WorkoutSessionLog, on_delete=models.CASCADE, related_name='exercise_logs') # Removed null=True
    exercise = models.ForeignKey(Exercise, on_delete=models.CASCADE)
    is_completed = models.BooleanField(default=True) # Assume completed if logged
    actual_sets = models.IntegerField(null=True, blank=True)
    actual_reps = models.IntegerField(null=True, blank=True)
    actual_weight = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_duration_seconds = models.IntegerField(null=True, blank=True)
    difficulty_rating = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        try:
            return f"{self.workout_session_log.workout_log} - {self.exercise.name}"
        except Exception:
            return f"Exercise Log: {self.exercise.name}"

class WorkoutReminder(models.Model):
    """Model for workout reminders"""
    user = models.ForeignKey('accounts.UserProfile', on_delete=models.CASCADE, related_name='workout_reminders')
    workout_day = models.ForeignKey(WorkoutDay, on_delete=models.CASCADE)
    time = models.TimeField()
    is_active = models.BooleanField(default=True)
    days_of_week = models.CharField(max_length=20, default="1,3,5") # Comma-separated days (e.g., "1,3,5" for Mon,Wed,Fri)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.user.username} - {self.workout_day.name} - {self.time}"
