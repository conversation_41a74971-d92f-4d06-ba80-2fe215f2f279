import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // For potential date/time formatting if needed
// import 'dart:developer' as developer; // Import developer for logging (removed) // Import for logging

import '../models/meal_log.dart';
import '../widgets/common/animated_loading.dart'; // For image placeholder
import '../utils/image_utils.dart'; // Import our new image utility class
import '../services/navigation_service.dart'; // Import NavigationService
import '../screens/meal/meal_completion_screen.dart'; // Import MealCompletionScreen

class DetailedMealCard extends StatefulWidget {
  final MealLog mealLog;
  final VoidCallback onComplete; // Callback when completion is triggered

  const DetailedMealCard({
    Key? key,
    required this.mealLog,
    required this.onComplete,
  }) : super(key: key);

  @override
  State<DetailedMealCard> createState() => _DetailedMealCardState();
}

class _DetailedMealCardState extends State<DetailedMealCard>
    with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  bool _isCompleted = false;
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _isCompleted = widget.mealLog.isCompleted;

    // Setup animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Handle the completion with animation
  Future<void> _handleComplete() async {
    if (_isLoading || _isCompleted) {
      // Removed log
      return;
    }

    // Check if the meal is completable
    if (!widget.mealLog.isCompletable) {
      // Removed log
      return;
    }

    // Removed log

    setState(() {
      _isLoading = true;
    });

    // Start the fade out animation
    _animationController.forward();

    // Call the actual completion callback
    await Future.delayed(const Duration(milliseconds: 300));
    widget.onComplete();

    // After a delay, update the UI to show completed state
    await Future.delayed(const Duration(milliseconds: 500));

    if (mounted) {
      setState(() {
        _isLoading = false;
        _isCompleted = true;
      });

      // Reset animation for potential future use
      _animationController.reset();
    }
  }

  // Helper to get display text and icon for meal type
  Map<String, dynamic> _getMealTypeInfo(String? mealType) {
    switch (mealType) {
      case 'B': // From MealDetail model
      case 'BREAKFAST': // From MealLog model (assuming consistency or checking both)
        return {'text': 'Breakfast', 'icon': Icons.free_breakfast};
      case 'L':
      case 'LUNCH':
        return {'text': 'Lunch', 'icon': Icons.lunch_dining};
      case 'D':
      case 'DINNER':
        return {'text': 'Dinner', 'icon': Icons.dinner_dining};
      case 'S':
      case 'SNACK':
        return {'text': 'Snack', 'icon': Icons.fastfood}; // Example icon
      default:
        return {'text': 'Meal', 'icon': Icons.restaurant};
    }
  }

  @override
  Widget build(BuildContext context) {
    final mealDetail = widget.mealLog.meal;
    final mealTypeInfo =
        _getMealTypeInfo(mealDetail.mealType ?? widget.mealLog.mealType);

    // Use the state variable for completion status
    // This ensures we show the animated state during completion
    final bool isCompleted = _isCompleted || widget.mealLog.isCompleted;

    // Log debug info to help diagnose issues
    // Removed log

    final theme = Theme.of(context);
    final textColor = isCompleted ? Colors.grey.shade600 : Colors.black87;
    final iconColor = isCompleted ? Colors.grey.shade500 : Colors.black54;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        // Apply the opacity animation when loading
        final opacity = _isLoading ? _opacityAnimation.value : 1.0;

        return Opacity(
            opacity: opacity,
            child: Card(
              margin:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
              elevation: 3.0,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.0)),
              color: Colors.white,
              child: InkWell(
                borderRadius: BorderRadius.circular(16.0),
                onTap: () {
                  // Navigate to meal details screen
                  if (!isCompleted) {
                    // For non-completed meals, navigate to details screen
                    NavigationService.navigateToNamed(
                      NavigationService.mealDetails,
                      arguments: {
                        'mealLog': widget.mealLog,
                        'date': DateTime.now(),
                      },
                    );
                  } else {
                    // For completed meals, navigate to completion screen
                    NavigationService.navigateTo(
                      MealCompletionScreen(mealLog: widget.mealLog),
                    );
                  }
                },
                child: Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          // Top Row: Image, Title, Meal Type, Prep Time
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Image
                              ClipRRect(
                                borderRadius: BorderRadius.circular(12.0),
                                child: ImageUtils.getNetworkImageWithFallback(
                                  imageUrl: mealDetail.mealImageUrl ??
                                      mealDetail.imageUrl ??
                                      mealDetail.mealImage,
                                  width: 80,
                                  height: 80,
                                  fit: BoxFit.cover,
                                  fallbackIcon: mealTypeInfo['icon'],
                                  fallbackIconColor: Colors.grey.shade600,
                                  placeholder: Container(
                                    width: 80,
                                    height: 80,
                                    color: Colors.grey[300],
                                    child:
                                        const Center(child: AnimatedLoading()),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Title, Type, Time
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      mealDetail.name,
                                      style:
                                          theme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: textColor,
                                        decoration: isCompleted
                                            ? TextDecoration.lineThrough
                                            : null,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 6),
                                    Row(
                                      children: [
                                        Icon(mealTypeInfo['icon'],
                                            size: 16, color: iconColor),
                                        const SizedBox(width: 4),
                                        Text(
                                          mealTypeInfo['text'],
                                          style: theme.textTheme.bodyMedium
                                              ?.copyWith(color: iconColor),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    if (mealDetail.preparationTime != null)
                                      Row(
                                        children: [
                                          Icon(Icons.timer_outlined,
                                              size: 16, color: iconColor),
                                          const SizedBox(width: 4),
                                          Text(
                                            '${mealDetail.preparationTime} min',
                                            style: theme.textTheme.bodyMedium
                                                ?.copyWith(color: iconColor),
                                          ),
                                        ],
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // Divider
                          Divider(color: Colors.grey.shade300),
                          const SizedBox(height: 12),
                          // Nutrient Info Row
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildNutrientInfo(
                                  context,
                                  'Calories',
                                  '${mealDetail.calories ?? 0}',
                                  Icons.local_fire_department_outlined,
                                  isCompleted),
                              _buildNutrientInfo(
                                  context,
                                  'Protein',
                                  '${mealDetail.protein?.toStringAsFixed(0) ?? 0}g',
                                  Icons.fitness_center_outlined,
                                  isCompleted),
                              _buildNutrientInfo(
                                  context,
                                  'Carbs',
                                  '${mealDetail.carbs?.toStringAsFixed(0) ?? 0}g',
                                  Icons.grain_outlined,
                                  isCompleted),
                              _buildNutrientInfo(
                                  context,
                                  'Fat',
                                  '${mealDetail.fat?.toStringAsFixed(0) ?? 0}g',
                                  Icons.opacity_outlined,
                                  isCompleted),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // Show loading indicator during completion
                          if (_isLoading)
                            Center(
                              child: Column(
                                children: [
                                  const SizedBox(height: 8),
                                  const CircularProgressIndicator(),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Completing meal...',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          // Action Button - Show if not completed, not loading, and completable
                          if (!isCompleted &&
                              !_isLoading &&
                              widget.mealLog.isCompletable)
                            Center(
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.check_circle_outline),
                                label: const Text('Mark as Completed'),
                                onPressed:
                                    _handleComplete, // Use our animated handler
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: Colors.white,
                                  backgroundColor:
                                      theme.primaryColor, // Text color
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          // Show "Completed" text if it is completed (regardless of date)
                          if (isCompleted && !_isLoading)
                            Center(
                              child: Text(
                                'Completed${widget.mealLog.completionTime != null ? ' on ${DateFormat.yMd().add_jm().format(widget.mealLog.completionTime!.toLocal())}' : ''}', // Show date/time if available
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.green.shade700,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          // Show message for non-completable meals (past or future dates)
                          if (!isCompleted &&
                              !_isLoading &&
                              !widget.mealLog.isCompletable)
                            Center(
                              child: Text(
                                'Only available on current program day',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey.shade600,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    // Simple completion indicator
                    if (isCompleted)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.shade600,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color:
                                    Colors.black.withAlpha(77), // 0.3 opacity
                                spreadRadius: 1,
                                blurRadius: 3,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    // Green border for completed meals
                    if (isCompleted)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.0),
                            border: Border.all(
                                color: Colors.green.shade500, width: 2),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ));
      },
    );
  }

  // Helper method to build nutrient info widgets
  Widget _buildNutrientInfo(BuildContext context, String label, String value,
      IconData icon, bool isCompleted) {
    final theme = Theme.of(context);
    final textColor = isCompleted ? Colors.grey.shade600 : Colors.black87;
    final iconColor = isCompleted ? Colors.grey.shade500 : Colors.black54;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 20, color: iconColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: textColor,
            decoration: isCompleted ? TextDecoration.lineThrough : null,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(color: iconColor),
        ),
      ],
    );
  }
}
