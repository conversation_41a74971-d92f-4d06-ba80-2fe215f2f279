import logging # Import logging
from datetime import datetime, date # Import date
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction # Import transaction
from ..models import (
    WorkoutPlan, WorkoutDay, WorkoutSession, WorkoutSection,
    WorkoutLog, WorkoutSessionLog, UserWorkoutPlan, # Added WorkoutSessionLog
    ExerciseInstance, ExerciseLog
)
# Update serializer imports to use the new workouts.serializers module
from ..serializers import (
    WorkoutPlanSerializer, WorkoutDaySerializer, WorkoutSessionSerializer,
    WorkoutSectionSerializer, WorkoutLogSerializer, WorkoutSessionLogSerializer, # Added WorkoutSessionLogSerializer
    ExerciseLogSerializer, UserWorkoutPlanSerializer
)
# Ensure UserProfile is imported for get_queryset methods
from accounts.models import UserProfile, UserProgress, UserScore
from django.utils import timezone
# Ensure UserWorkoutPlan is imported (already imported via ..models)
from django.db.models import F # Import F expression
from django.db import models # Import models for JSONField
from django_filters import rest_framework as filters # Import filters
from django_filters import CharFilter # Import CharFilter for JSONField filtering
from rest_framework.exceptions import ValidationError # Import ValidationError

# Get an instance of a logger
logger = logging.getLogger(__name__)

# --- Moved ViewSets from accounts/views.py (Keep as is) ---
class WorkoutPlanFilter(filters.FilterSet):
    class Meta:
        model = WorkoutPlan
        fields = [
            'fitness_level',
            'goal',
            'duration_weeks',
            'workouts_per_week',
            'age_group' # Add age_group to fields
        ]
        filter_overrides = {
            models.JSONField: {
                'filter_class': CharFilter,
                'extra': lambda f: {
                    'lookup_expr': 'exact',
                }
            }
        }

class WorkoutPlanViewSet(viewsets.ReadOnlyModelViewSet): # Changed to ReadOnly as per accounts/views.py
    queryset = WorkoutPlan.objects.all()
    serializer_class = WorkoutPlanSerializer
    filterset_class = WorkoutPlanFilter # Now defined
    permission_classes = [permissions.IsAuthenticated] # Changed from original in this file

    # Note: get_permissions and get_queryset from accounts/views.py version are more complex
    # Keeping the simpler version from the original workouts/views/workout.py for now.
    # If the complex logic (Admin vs User permissions/queryset) is needed, it should be merged carefully.

    # today_workout action already exists below, no need to duplicate

class WorkoutDayViewSet(viewsets.ModelViewSet):
    queryset = WorkoutDay.objects.all()
    serializer_class = WorkoutDaySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Ensure user is authenticated and filter through UserProfile
        if not self.request.user or not self.request.user.is_authenticated:
            return WorkoutDay.objects.none()
        # UserProfile is imported above
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        # Fetch WorkoutDay objects directly linked to the user's UserWorkoutPlan instances
        user_plan_ids = UserWorkoutPlan.objects.filter(user=user_profile, is_active=True).values_list('id', flat=True)
        # Return only the days cloned for this user's active plans
        return WorkoutDay.objects.filter(user_workout_plan_id__in=user_plan_ids)

class WorkoutSessionViewSet(viewsets.ModelViewSet):
    queryset = WorkoutSession.objects.all()
    serializer_class = WorkoutSessionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Ensure user is authenticated and filter through UserProfile
        if not self.request.user or not self.request.user.is_authenticated:
            return WorkoutSession.objects.none()
        # UserProfile is imported above
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        # Fetch WorkoutSession objects directly linked to the user's UserWorkoutPlan instances
        user_plan_ids = UserWorkoutPlan.objects.filter(user=user_profile, is_active=True).values_list('id', flat=True)
        # Return only the sessions cloned for this user's active plans
        return WorkoutSession.objects.filter(workout_day__user_workout_plan_id__in=user_plan_ids)

class WorkoutSectionViewSet(viewsets.ModelViewSet):
    serializer_class = WorkoutSectionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Ensure user is authenticated and filter through UserProfile
        if not self.request.user or not self.request.user.is_authenticated:
            return WorkoutSection.objects.none()
        # UserProfile is imported above
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        # UserWorkoutPlan is imported via ..models
        assigned_plan_ids = UserWorkoutPlan.objects.filter(user=user_profile).values_list('workout_plan_id', flat=True)
        # Filter sections belonging to workout days within plans assigned to the user
        return WorkoutSection.objects.filter(workout_day__workout_plan_id__in=assigned_plan_ids)

# --- UPDATED: WorkoutLogViewSet (Handles Day-level logs) ---
class WorkoutLogViewSet(viewsets.ModelViewSet):
    serializer_class = WorkoutLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            user_profile = UserProfile.objects.get(user=self.request.user)
            # Prefetch related session logs and exercises for efficiency
            return WorkoutLog.objects.filter(user=user_profile).prefetch_related(
                'session_logs__exercise_logs__exercise',
                'session_logs__workout_session'
            )
        except UserProfile.DoesNotExist:
            return WorkoutLog.objects.none()

    def perform_create(self, serializer):
        # Set user automatically
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        serializer.save(user=user_profile)

    # Create method simplified: Primarily handles day-level log creation if needed directly.
    # Most creation might happen implicitly via WorkoutSessionLogViewSet.
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            raise ValidationError("User profile not found for the current user.")

        validated_data = serializer.validated_data
        workout_day = validated_data.get('workout_day')
        log_date_input = validated_data.get('date', timezone.now())
        log_date = log_date_input.date() if isinstance(log_date_input, datetime) else log_date_input

        if not workout_day:
            raise ValidationError("Workout Day is required for logging.")

        # Use update_or_create for the day log
        workout_log, created = WorkoutLog.objects.update_or_create(
            user=user_profile,
            workout_day=workout_day,
            date=log_date,
            defaults=validated_data # Pass other validated data as defaults
        )

        response_serializer = self.get_serializer(workout_log)
        headers = self.get_success_headers(response_serializer.data)
        response_status = status.HTTP_201_CREATED if created else status.HTTP_200_OK
        return Response(response_serializer.data, status=response_status, headers=headers)

    # Removed `complete` action - Moved to WorkoutSessionLogViewSet
    # Removed `log_exercises` action - Moved to WorkoutSessionLogViewSet

# --- NEW: WorkoutSessionLogViewSet (Handles Session-level logs and actions) ---
class WorkoutSessionLogViewSet(viewsets.ModelViewSet):
    serializer_class = WorkoutSessionLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        try:
            user_profile = UserProfile.objects.get(user=self.request.user)
            # Filter session logs by user through the parent WorkoutLog
            return WorkoutSessionLog.objects.filter(workout_log__user=user_profile).select_related(
                'workout_log', 'workout_session'
            ).prefetch_related('exercise_logs__exercise')
        except UserProfile.DoesNotExist:
            return WorkoutSessionLog.objects.none()

    # Override create to handle finding/creating parent WorkoutLog
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            raise ValidationError("User profile not found for the current user.")

        validated_data = serializer.validated_data
        workout_session = validated_data.get('workout_session')
        log_date_input = validated_data.get('date', timezone.now())
        log_date = log_date_input.date() if isinstance(log_date_input, datetime) else log_date_input

        if not workout_session:
            raise ValidationError("Workout Session is required for logging.")

        workout_day = workout_session.workout_day # Get day from session

        # Find or create the parent WorkoutLog (for the day)
        workout_log, workout_log_created = WorkoutLog.objects.get_or_create(
            user=user_profile,
            workout_day=workout_day,
            date=log_date,
            defaults={'is_completed': False} # Default day log to not completed
        )

        # Use update_or_create for the WorkoutSessionLog
        session_log, created = WorkoutSessionLog.objects.update_or_create(
            workout_log=workout_log,
            workout_session=workout_session,
            defaults={**validated_data, 'date': log_date} # Ensure date is set, pass other data
        )

        response_serializer = self.get_serializer(session_log)
        headers = self.get_success_headers(response_serializer.data)
        response_status = status.HTTP_201_CREATED if created else status.HTTP_200_OK
        return Response(response_serializer.data, status=response_status, headers=headers)

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        session_log = self.get_object()
        user_profile = session_log.workout_log.user

        # Ensure the log belongs to the requesting user
        if user_profile.user != request.user:
            return Response({"detail": "Not allowed to complete this session log."}, status=status.HTTP_403_FORBIDDEN)

        if session_log.is_completed:
            # Already completed, maybe return current state or a specific message
            serializer = self.get_serializer(session_log)
            return Response(serializer.data, status=status.HTTP_200_OK)

        try:
            with transaction.atomic():
                # Mark the session log as complete
                session_log.is_completed = True
                session_log.completion_time = timezone.now()
                # Add duration/calories if provided in request body
                duration = request.data.get('duration_minutes')
                calories = request.data.get('calories_burned')
                if duration is not None: session_log.duration_minutes = duration
                if calories is not None: session_log.calories_burned = calories
                session_log.save(update_fields=['is_completed', 'completion_time', 'duration_minutes', 'calories_burned'])
                logger.info(f"Marked WorkoutSessionLog {session_log.id} as complete.")

                # +++ Log SESSION completion activity +++
                try:
                    from activity_logs.utils import log_workout_session_completion # Import new function
                    log_workout_session_completion(user_profile, session_log)
                    logger.info(f"Logged SESSION workout completion activity for user {user_profile.user.username}, session log {session_log.id}")
                except Exception as e:
                    logger.error(f"Error logging SESSION workout completion activity: {e}", exc_info=True)
                # --- End activity logging ---

                # Check if all sessions for the parent WorkoutLog are now complete
                parent_log = session_log.workout_log
                # Get ALL sessions for this workout day (both template and user-specific)
                all_sessions_in_day = WorkoutSession.objects.filter(workout_day=parent_log.workout_day)
                # Log all sessions for debugging
                logger.info(f"All sessions for workout day {parent_log.workout_day.id}:")
                for s in all_sessions_in_day:
                    logger.info(f"  - Session ID: {s.id}, Name: {s.name}, User Plan: {s.user_workout_plan_id}")

                # Get all completed session logs for this workout log
                completed_session_logs = WorkoutSessionLog.objects.filter(
                    workout_log=parent_log,
                    is_completed=True
                )
                logger.info(f"Completed sessions: {completed_session_logs.count()}/{all_sessions_in_day.count()} for user {user_profile.id}, workout day {parent_log.workout_day.id}")

                # Log the completed session logs for debugging
                for s_log in completed_session_logs:
                    logger.info(f"  - Completed Session Log ID: {s_log.id}, Session ID: {s_log.workout_session.id}, Name: {s_log.workout_session.name}")

                day_now_complete = all_sessions_in_day.count() == completed_session_logs.count()
                day_was_already_complete = parent_log.is_completed

                # Update parent WorkoutLog if all sessions are complete and it wasn't already
                if day_now_complete and not day_was_already_complete:
                    parent_log.is_completed = True
                    parent_log.completion_time = timezone.now()
                    # Sum duration/calories from session logs
                    parent_log.duration_minutes = sum(filter(None, completed_session_logs.values_list('duration_minutes', flat=True)))
                    parent_log.calories_burned = sum(filter(None, completed_session_logs.values_list('calories_burned', flat=True)))
                    parent_log.save(update_fields=['is_completed', 'completion_time', 'duration_minutes', 'calories_burned'])
                    logger.info(f"Marked parent WorkoutLog {parent_log.id} as complete.")

                    # Trigger score/progress update for DAY completion
                    try:
                        progress, _ = UserProgress.objects.get_or_create(user=user_profile)
                        progress.update_streak() # Update streak on day completion
                        progress.save()

                        score, _ = UserScore.objects.get_or_create(user=user_profile)
                        score.calculate_score() # Recalculate score on day completion
                        score.save()

                        # Log activity for DAY completion
                        from activity_logs.utils import log_workout_completion
                        log_workout_completion(user_profile, parent_log) # Log parent log completion
                        logger.info(f"Logged DAY workout completion activity for user {user_profile.user.username}, workout log {parent_log.id}")

                    except Exception as e:
                        logger.error(f"Error updating progress/score/activity for DAY completion: {e}", exc_info=True)

                # Update score directly - 2 points per workout session
                score, score_created = UserScore.objects.get_or_create(user=user_profile)

                # Add 2 points for workout session completion, max 30
                score.workout_score = min(30, (score.workout_score or 0) + 2)

                # Update total score
                score.total_score = min(100, (
                    (score.workout_score or 0) +
                    (score.streak_score or 0) +
                    (score.nutrition_score or 0) +
                    (score.goal_score or 0)
                ))

                # Update total points earned
                score.total_points_earned = (score.total_points_earned or 0) + 2
                score.last_calculated = timezone.now()
                score.save()

        except Exception as e:
            logger.error(f"Error completing session log {pk} for user {user_profile.user.email}: {e}", exc_info=True)
            return Response({"detail": "An error occurred while completing the session."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        serializer = self.get_serializer(session_log)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], url_path='direct-complete')
    def direct_complete(self, request):
        """Directly complete a workout session without requiring a session log ID"""
        logger.info("VIEWSET ACTION: direct_complete called")
        logger.info(f"Request path: {request.path}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request data: {request.data}")
        user_profile = get_object_or_404(UserProfile, user=request.user)

        try:
            # Get the workout day from the request data
            workout_day_id = request.data.get('workout_day_id')
            if not workout_day_id:
                # Fallback to first workout day if not provided
                workout_day = WorkoutDay.objects.first()
                if not workout_day:
                    return Response({"detail": "No workout days found in the database"}, status=status.HTTP_404_NOT_FOUND)
            else:
                # Get the workout day by ID
                try:
                    workout_day = WorkoutDay.objects.get(id=workout_day_id)
                except WorkoutDay.DoesNotExist:
                    return Response({"detail": f"Workout day with ID {workout_day_id} not found"}, status=status.HTTP_404_NOT_FOUND)

            # Get or create a workout log
            workout_log, created = WorkoutLog.objects.get_or_create(
                user=user_profile,
                workout_day=workout_day,
                date=request.data.get('date', timezone.now().date()),
                defaults={
                    'is_completed': True,
                    'completion_time': timezone.now(),
                    'calories_burned': request.data.get('calories_burned', 0),
                    'duration_minutes': request.data.get('duration_minutes', 30),
                    'notes': request.data.get('notes', '')
                }
            )

            # If the workout log already exists, update it
            if not created:
                workout_log.is_completed = True
                workout_log.completion_time = timezone.now()
                workout_log.calories_burned = request.data.get('calories_burned', 0)
                workout_log.duration_minutes = request.data.get('duration_minutes', 30)
                workout_log.notes = request.data.get('notes', '')
                workout_log.save()

            # Get the workout session from the request data or use the first session for the workout day
            workout_session_id = request.data.get('workout_session_id')
            if workout_session_id:
                try:
                    workout_session = WorkoutSession.objects.get(id=workout_session_id, workout_day=workout_day)
                except WorkoutSession.DoesNotExist:
                    return Response({"detail": f"Workout session with ID {workout_session_id} not found for workout day {workout_day.id}"}, status=status.HTTP_404_NOT_FOUND)
            else:
                # Get the first session for the workout day
                workout_session = WorkoutSession.objects.filter(workout_day=workout_day).first()
                if not workout_session:
                    return Response({"detail": f"No workout sessions found for workout day {workout_day.id}"}, status=status.HTTP_404_NOT_FOUND)

            # Get or create a workout session log
            session_log, session_created = WorkoutSessionLog.objects.get_or_create(
                workout_log=workout_log,
                workout_session=workout_session,
                defaults={
                    'date': request.data.get('date', timezone.now().date()),
                    'is_completed': True,
                    'completion_time': timezone.now(),
                    'calories_burned': request.data.get('calories_burned', 0),
                    'duration_minutes': request.data.get('duration_minutes', 30),
                    'notes': request.data.get('notes', '')
                }
            )

            # If the session log already exists, update it
            if not session_created:
                session_log.is_completed = True
                session_log.completion_time = timezone.now()
                session_log.calories_burned = request.data.get('calories_burned', 0)
                session_log.duration_minutes = request.data.get('duration_minutes', 30)
                session_log.notes = request.data.get('notes', '')
                session_log.save()

            # Check if all sessions for this workout day are now completed
            try:
                # Get ALL sessions for this workout day (both template and user-specific)
                all_sessions = WorkoutSession.objects.filter(
                    workout_day=workout_day
                )
                all_session_count = all_sessions.count()

                # Log all sessions for debugging
                logger.info(f"All sessions for workout day {workout_day.id}:")
                for s in all_sessions:
                    logger.info(f"  - Session ID: {s.id}, Name: {s.name}, User Plan: {s.user_workout_plan_id}")

                # Get all completed session logs for this workout log
                completed_session_logs = WorkoutSessionLog.objects.filter(
                    workout_log=workout_log,
                    is_completed=True
                )
                completed_session_count = completed_session_logs.count()

                logger.info(f"Completed sessions: {completed_session_count}/{all_session_count} for user {user_profile.id}, workout day {workout_day.id}")

                # If all sessions are completed, mark the workout log as completed
                if completed_session_count >= all_session_count and all_session_count > 0:
                    logger.info(f"All sessions completed for user {user_profile.id}, workout day {workout_day.id}. Marking workout log {workout_log.id} as completed.")

                    # Calculate total calories and duration from all session logs
                    total_calories = 0
                    total_duration = 0
                    for s_log in completed_session_logs:
                        total_calories += s_log.calories_burned or 0
                        total_duration += s_log.duration_minutes or 0

                    # Update the workout log with completion info and totals
                    workout_log.is_completed = True
                    workout_log.completion_time = timezone.now()
                    workout_log.calories_burned = total_calories
                    workout_log.duration_minutes = total_duration
                    workout_log.save(update_fields=['is_completed', 'completion_time', 'calories_burned', 'duration_minutes'])
            except Exception as e:
                logger.error(f"Error checking session completion status: {e}")

            # Update user progress
            progress, _ = UserProgress.objects.get_or_create(user=user_profile)
            progress.total_workouts_completed = (progress.total_workouts_completed or 0) + 1
            progress.save(update_fields=['total_workouts_completed'])

            # Update streak
            try:
                progress.update_streak()
            except Exception as e:
                logger.error(f"Error updating streak: {e}", exc_info=True)

            # Update score directly - 2 points per workout session
            score, _ = UserScore.objects.get_or_create(user=user_profile)

            # Add 2 points for workout session completion, max 30
            score.workout_score = min(30, (score.workout_score or 0) + 2)

            # Update total score
            score.total_score = min(100, (
                (score.workout_score or 0) +
                (score.streak_score or 0) +
                (score.nutrition_score or 0) +
                (score.goal_score or 0)
            ))

            # Update total points earned
            score.total_points_earned = (score.total_points_earned or 0) + 2
            score.last_calculated = timezone.now()
            score.save()

            # Log the activity
            try:
                from activity_logs.utils import log_workout_session_completion
                log_workout_session_completion(user_profile, session_log)
            except Exception as e:
                logger.error(f"Error logging workout completion activity: {e}", exc_info=True)

            serializer = self.get_serializer(session_log)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error in direct_complete: {e}", exc_info=True)
            return Response({"detail": f"Error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def log_exercises(self, request, pk=None):
        """Batch log multiple exercises for a workout session log"""
        session_log = self.get_object()
        user_profile = session_log.workout_log.user

        # Ensure the log belongs to the requesting user
        if user_profile.user != request.user:
            return Response({"detail": "Not allowed to log exercises for this session."}, status=status.HTTP_403_FORBIDDEN)

        exercises_data = request.data.get('exercises', [])
        if not exercises_data:
            return Response({"detail": "No exercises provided."}, status=status.HTTP_400_BAD_REQUEST)

        created_logs = []
        updated_logs = []
        errors = []

        try:
            with transaction.atomic():
                for exercise_data in exercises_data:
                    exercise_id = exercise_data.get('exercise')
                    if not exercise_id:
                        errors.append({"detail": "Exercise ID is required", "data": exercise_data})
                        continue

                    # Ensure is_completed is set if provided, default to True if logging
                    is_completed = exercise_data.get('is_completed', True)

                    # Prepare data for update_or_create
                    defaults = {k: v for k, v in exercise_data.items() if k not in ['exercise']}
                    defaults['is_completed'] = is_completed # Ensure completion status is saved

                    try:
                        # Use update_or_create for ExerciseLog
                        exercise_log, created = ExerciseLog.objects.update_or_create(
                            workout_session_log=session_log,
                            exercise_id=exercise_id,
                            defaults=defaults
                        )
                        if created:
                            created_logs.append(exercise_log)
                        else:
                            updated_logs.append(exercise_log)
                    except Exception as e:
                        logger.error(f"Error logging exercise {exercise_id} for session {session_log.id}: {str(e)}", exc_info=True)
                        errors.append({"detail": str(e), "data": exercise_data})

                # Mark the session as complete if all exercises were logged successfully
                # (This might be too aggressive - consider if session completion should be explicit)
                # if not errors:
                #     if not session_log.is_completed:
                #         session_log.is_completed = True
                #         session_log.completion_time = timezone.now()
                #         session_log.save(update_fields=['is_completed', 'completion_time'])
                #         # Potentially trigger day completion check here too

        except Exception as e:
            logger.error(f"Error in batch exercise logging transaction: {str(e)}", exc_info=True)
            return Response({"detail": f"Error logging exercises: {str(e)}", "errors": errors},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Return the results
        return Response({
            "created": len(created_logs),
            "updated": len(updated_logs),
            "errors": errors,
            "workout_session_log": session_log.id # Return session log ID
        })

# --- UserWorkoutPlanViewSet (Keep as is) ---
class UserWorkoutPlanViewSet(viewsets.ModelViewSet):
    serializer_class = UserWorkoutPlanSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Filter by the logged-in user's profile
        user_profile = get_object_or_404(UserProfile, user=self.request.user)
        # Add ordering to prevent pagination warnings
        return UserWorkoutPlan.objects.filter(user=user_profile).order_by('-start_date')

    @action(detail=True, methods=['post'], url_path='clone')
    def clone(self, request, pk=None):
        """Clone the workout plan structure for this user workout plan"""
        user_workout_plan = self.get_object()

        # Ensure the plan belongs to the requesting user
        if user_workout_plan.user.user != request.user:
            return Response({"detail": "Not authorized to clone this workout plan."}, status=status.HTTP_403_FORBIDDEN)

        try:
            # Perform the deep clone operation
            from ..models import deep_clone_workout_plan_for_user
            deep_clone_workout_plan_for_user(user_workout_plan)

            # Return success response
            return Response({
                "detail": "Workout plan successfully cloned",
                "user_workout_plan_id": user_workout_plan.id
            })
        except Exception as e:
            logger.error(f"Error cloning workout plan: {str(e)}")
            return Response({
                "detail": f"Error cloning workout plan: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # Add perform_create if needed to set user automatically (though usually handled by signals/questionnaire)
    # def perform_create(self, serializer):
    #     user_profile = get_object_or_404(UserProfile, user=self.request.user)
    #     serializer.save(user=user_profile)

# Note: Original WorkoutPlanViewSet, WorkoutDayViewSet, WorkoutSectionViewSet definitions
# from this file were overwritten by the moved code above. Ensure imports are correct.

# Removed UserWorkoutViewSet as UserWorkout model is deleted
# class UserWorkoutViewSet(viewsets.ModelViewSet): ...
