#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
django.setup()

from accounts.models import User, UserProfile
from workouts.models import UserWorkoutPlan, WorkoutPlan, WorkoutVideo

def debug_video_setup():
    print("=== Video Setup Debug Report ===\n")
    
    # Check the test user
    try:
        user = User.objects.get(username='dakshsharma15195')
        user_profile = UserProfile.objects.get(user=user)
        print(f"✓ User: {user.username}")
        print(f"✓ Profile: {user_profile.id}")
        
        # Check active workout plans
        active_plans = UserWorkoutPlan.objects.filter(user=user_profile, is_active=True)
        print(f"✓ Active Plans: {active_plans.count()}")
        
        for plan in active_plans:
            print(f"  - Plan: {plan.workout_plan.name}")
            videos = WorkoutVideo.objects.filter(workout_plan=plan.workout_plan)
            print(f"  - Videos: {videos.count()}")
            
            for video in videos:
                has_video = bool(video.video_file)
                has_thumb = bool(video.thumbnail)
                print(f"    • {video.title}: Video={has_video}, Thumb={has_thumb}")
                if has_video:
                    print(f"      Video URL: {video.video_file.url}")
                if has_thumb:
                    print(f"      Thumb URL: {video.thumbnail.url}")
        
        print(f"\n✓ Setup Status: READY FOR TESTING")
        print(f"✓ Login with: {user.username}")
        print(f"✓ Expected: Video button should appear on home screen")
        
    except User.DoesNotExist:
        print("✗ Test user not found!")
    except UserProfile.DoesNotExist:
        print("✗ User profile not found!")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    print(f"\n=== Server Status ===")
    import requests
    try:
        response = requests.get('http://127.0.0.1:8000/acfit-admin/', timeout=2)
        if response.status_code in [200, 302]:
            print("✓ Backend server is running")
        else:
            print(f"⚠ Backend server returned: {response.status_code}")
    except requests.exceptions.RequestException:
        print("✗ Backend server not accessible")
        print("  Run: python manage.py runserver")

if __name__ == "__main__":
    debug_video_setup()
