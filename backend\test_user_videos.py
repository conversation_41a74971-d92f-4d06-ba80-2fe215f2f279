#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
django.setup()

from accounts.models import User, UserProfile
from workouts.models import UserWorkoutPlan, WorkoutPlan, WorkoutVideo

def test_user_video_access():
    print("=== Testing User Video Access ===\n")
    
    # Check users and their workout plans
    users = User.objects.all()
    print(f"Total users: {users.count()}")
    
    for user in users[:5]:  # Check first 5 users
        print(f"\n--- User: {user.username} ---")
        
        try:
            user_profile = UserProfile.objects.get(user=user)
            print(f"  Profile: ✓")
            
            # Check active workout plans
            active_plans = UserWorkoutPlan.objects.filter(user=user_profile, is_active=True)
            print(f"  Active workout plans: {active_plans.count()}")
            
            for plan in active_plans:
                print(f"    - {plan.workout_plan.name}")
                
                # Check videos for this plan
                videos = WorkoutVideo.objects.filter(workout_plan=plan.workout_plan)
                print(f"      Videos: {videos.count()}")
                for video in videos:
                    print(f"        - {video.title}")
                    
        except UserProfile.DoesNotExist:
            print(f"  Profile: ✗ (No profile)")
    
    # Check which workout plan has videos
    print(f"\n--- Workout Plans with Videos ---")
    plans_with_videos = WorkoutPlan.objects.filter(videos__isnull=False).distinct()
    for plan in plans_with_videos:
        video_count = WorkoutVideo.objects.filter(workout_plan=plan).count()
        print(f"  {plan.name}: {video_count} videos")
    
    # Suggest assignment
    print(f"\n--- Recommendations ---")
    if plans_with_videos.exists():
        plan_with_videos = plans_with_videos.first()
        users_without_videos = []
        
        for user in users[:3]:  # Check first 3 users
            try:
                user_profile = UserProfile.objects.get(user=user)
                active_plans = UserWorkoutPlan.objects.filter(user=user_profile, is_active=True)
                
                has_videos = False
                for plan in active_plans:
                    if WorkoutVideo.objects.filter(workout_plan=plan.workout_plan).exists():
                        has_videos = True
                        break
                
                if not has_videos:
                    users_without_videos.append(user.username)
            except UserProfile.DoesNotExist:
                pass
        
        if users_without_videos:
            print(f"  Users who need video access: {', '.join(users_without_videos)}")
            print(f"  Assign them to: {plan_with_videos.name}")
        else:
            print(f"  All users have access to videos!")

if __name__ == "__main__":
    test_user_video_access()
