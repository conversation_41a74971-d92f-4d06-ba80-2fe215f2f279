import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import '../utils/logger.dart';

class WorkoutFrameWidget extends StatelessWidget {
  final String? coverImageUrl;
  final String workoutName;
  final int? duration;
  final int? calories;
  final VoidCallback onTap;
  final bool isCompleted;
  final bool hasVideos;
  final VoidCallback? onVideoTap;

  const WorkoutFrameWidget({
    Key? key,
    required this.coverImageUrl,
    required this.workoutName,
    this.duration,
    this.calories,
    required this.onTap,
    this.isCompleted = false,
    this.hasVideos = false,
    this.onVideoTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Figma Flutter Generator FrameWidget - FRAME - VERTICAL
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            // Fixed height to ensure proper layout
            height: 250, // Increased height to match design
            width: double.infinity,
            // Apply decoration with border radius
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(32)),
              // Fallback color when image is loading
              color: Color.fromRGBO(16, 17, 20, 1),
              // No background image here - we'll use CachedNetworkImage as a child instead
            ),
            // Apply clipping to ensure the background image respects the border radius
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                // Background image with CachedNetworkImage
                // Always show an image - either the provided one or a default
                Builder(builder: (context) {
                  // Log the original URL for debugging
                  Logger.tempLog('Original coverImageUrl: $coverImageUrl',
                      tag: 'WorkoutFrameWidget');

                  // Default image URL to use as fallback - use a working image
                  const String defaultImageUrl =
                      'http://localhost:8000/media/workout_days/covers/ChatGPT_Image_Apr_18_2025_11_08_40_PM.png';

                  // Use the provided URL if it's not null or empty, otherwise use default
                  String imageUrl =
                      (coverImageUrl != null && coverImageUrl!.isNotEmpty)
                          ? coverImageUrl!
                          : defaultImageUrl;

                  Logger.tempLog('Using image URL: $imageUrl',
                      tag: 'WorkoutFrameWidget');

                  return SizedBox.expand(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: const Color.fromRGBO(16, 17, 20, 1),
                        child: const Center(
                          child: CircularProgressIndicator(
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white70),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) {
                        // Log the error for debugging
                        Logger.error(
                            'Error loading workout image: $url - $error',
                            tag: 'WorkoutFrameWidget');

                        // If the provided URL failed, try the default URL
                        if (url != defaultImageUrl) {
                          Logger.tempLog('Falling back to default image URL',
                              tag: 'WorkoutFrameWidget');
                          return CachedNetworkImage(
                            imageUrl: defaultImageUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: const Color.fromRGBO(16, 17, 20, 1),
                              child: const Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white70),
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) {
                              // If even the default URL fails, show an icon
                              Logger.error(
                                  'Error loading default image: $error',
                                  tag: 'WorkoutFrameWidget');
                              return Container(
                                color: const Color.fromRGBO(16, 17, 20, 1),
                                child: const Center(
                                  child: Icon(
                                    Icons.fitness_center,
                                    color: Colors.white54,
                                    size: 40,
                                  ),
                                ),
                              );
                            },
                          );
                        }

                        // If we're already using the default URL and it failed, show an icon
                        return Container(
                          color: const Color.fromRGBO(16, 17, 20, 1),
                          child: const Center(
                            child: Icon(
                              Icons.fitness_center,
                              color: Colors.white54,
                              size: 40,
                            ),
                          ),
                        );
                      },
                    ),
                  );
                }),
                // Content container with gradient overlay
                Container(
                  // Semi-transparent overlay for text readability
                  decoration: const BoxDecoration(
                    color: Color.fromRGBO(16, 17, 20, 0.5), // Overlay color
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  child: Column(
                    // Use spaceBetween to position elements at top and bottom
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment:
                        CrossAxisAlignment.start, // Align text left
                    children: <Widget>[
                      // Duration and calories at the top
                      if (duration != null || calories != null)
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            if (duration != null) ...[
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  const Icon(Icons.timer,
                                      color: Colors.white, size: 18),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${duration}min',
                                    textAlign: TextAlign.left,
                                    style: const TextStyle(
                                      color: Color.fromRGBO(255, 255, 255, 1),
                                      fontFamily: 'Work Sans',
                                      fontSize: 14,
                                      letterSpacing: 0,
                                      fontWeight: FontWeight.normal,
                                      height: 1,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            if (duration != null && calories != null)
                              const SizedBox(width: 8),
                            // Add divider between time and calories
                            if (duration != null && calories != null)
                              const Text('|',
                                  style: TextStyle(color: Colors.white54)),
                            if (duration != null && calories != null)
                              const SizedBox(width: 8),
                            if (calories != null) ...[
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  const Icon(Icons.local_fire_department,
                                      color: Colors.white, size: 18),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${calories}kcal',
                                    textAlign: TextAlign.left,
                                    style: const TextStyle(
                                      color: Color.fromRGBO(255, 255, 255, 1),
                                      fontFamily: 'Work Sans',
                                      fontSize: 14,
                                      letterSpacing: 0,
                                      fontWeight: FontWeight.normal,
                                      height: 1,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      const SizedBox(height: 8), // Adjusted spacing

                      // Workout name and play icon
                      Row(
                        mainAxisAlignment: MainAxisAlignment
                            .spaceBetween, // Space between title and icon
                        crossAxisAlignment:
                            CrossAxisAlignment.end, // Align bottom edges
                        children: <Widget>[
                          Expanded(
                            child: Builder(builder: (context) {
                              // Debug log the workout name
                              Logger.meal(
                                  'Displaying workout name: "$workoutName"',
                                  tag: 'WorkoutFrameWidget');

                              // Check if the name is empty or just whitespace
                              if (workoutName.isEmpty) {
                                Logger.meal('Workout name is empty',
                                    tag: 'WorkoutFrameWidget');
                              } else if (workoutName.trim().isEmpty) {
                                Logger.meal(
                                    'Workout name contains only whitespace',
                                    tag: 'WorkoutFrameWidget');
                              }

                              // Use a fallback name if empty or just whitespace
                              final displayName = workoutName.trim().isNotEmpty
                                  ? workoutName
                                  : 'Workout for ${DateFormat('MMM d').format(DateTime.now())}';

                              return Text(
                                displayName,
                                textAlign: TextAlign.left,
                                style: const TextStyle(
                                  color: Color.fromRGBO(255, 255, 255, 1),
                                  fontFamily: 'Work Sans',
                                  fontSize: 24,
                                  letterSpacing: 0,
                                  fontWeight:
                                      FontWeight.bold, // Bold for visibility
                                  height: 1.2, // Adjusted height
                                ),
                              );
                            }),
                          ),
                          // Play icon with orange background
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Color(0xFFF97316), // Orange color
                              boxShadow: [
                                BoxShadow(
                                  color: Color(
                                      0x80000000), // Black with 50% opacity
                                  blurRadius: 8,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: const Icon(Icons.play_arrow,
                                color: Colors.white, size: 28),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Video Button - Show if videos are available
          if (hasVideos && onVideoTap != null)
            Positioned(
              top: 12,
              left: 12,
              child: GestureDetector(
                onTap: () {
                  print('DEBUG: Video button tapped in WorkoutFrameWidget');
                  onVideoTap?.call();
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(
                        red: 0,
                        green: 0,
                        blue: 0,
                        alpha: 179), // 0.7 * 255 = 179
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.play_circle_filled,
                    color: Color(0xFFF97316),
                    size: 24,
                  ),
                ),
              ),
            ),

          // Completion Badge - Always show, but with different styles based on completion status
          Positioned(
            top: 12,
            right: 12,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isCompleted
                    ? Colors.white
                    : Colors.white.withValues(
                        red: 255, green: 255, blue: 255, alpha: 204),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black
                        .withValues(red: 0, green: 0, blue: 0, alpha: 38),
                    spreadRadius: 0,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isCompleted ? Icons.check_circle : Icons.circle_outlined,
                    color: isCompleted
                        ? Colors.green.shade600
                        : Colors.grey.shade600,
                    size: 14,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    isCompleted ? 'Completed' : 'Pending',
                    style: TextStyle(
                      color: isCompleted
                          ? Colors.green.shade800
                          : Colors.grey.shade800,
                      fontWeight: FontWeight.bold,
                      fontSize: 11,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
