import os
import sys
import json
import django
import logging
import requests
import argparse
import random
from django.conf import settings
from django.core.files.base import ContentFile
from io import BytesIO
from PIL import Image
from django.core.files.storage import default_storage
from datetime import datetime, timedelta, date

# API Endpoints
UNSPLASH_API_URL = "https://api.unsplash.com/search/photos"
GIPHY_API_URL = "https://api.giphy.com/v1/gifs/search"

# Import BaseCommand
from django.core.management.base import BaseCommand, CommandError

# Set up Django (already done by manage.py)
# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'acfit_backend.settings')
# django.setup()

# Import models after Django setup
from products.models import Product
from workouts.models import (
    Exercise, WorkoutPlan, WorkoutDay, WorkoutSection, WorkoutSchedule,
    ExerciseInstance, WorkoutLog, ExerciseLog, WorkoutSession, UserWorkoutPlan,
    WorkoutSessionLog
)
from meals.models import (
    Meal, MealPlan, DailyMealPlan, MealSchedule, UserMealPlan
)
from django.contrib.auth import get_user_model
from accounts.models import UserProfile, UserProgress, UserScore

# Import functions from other populate scripts directly (now relative imports)
from .populate_plans_from_criteria import populate_plans_from_criteria
from .populate_questionnaire import populate_questionnaire as populate_questionnaire_func
from .populate_faqs import populate_faqs as populate_faqs_func

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

User = get_user_model()

def ensure_directories():
    """Ensure all required media directories exist."""
    directories = [
        'media/exercises/images',
        'media/exercises/videos',
        'media/exercises/gifs',
        'media/meals/images',
        'media/meals/videos',
        'media/products',
        'media/achievements',
        'media/progress_photos',
        'media/equipment'
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Ensured directory exists: {directory}")

def get_unsplash_image_url(query):
    """Fetch a random image URL from Unsplash based on a query."""
    headers = {
        "Authorization": f"Client-ID {settings.UNSPLASH_ACCESS_KEY}"
    }
    params = {
        "query": query,
        "per_page": 1,
        "orientation": "landscape",
        "content_filter": "high"
    }
    try:
        response = requests.get(UNSPLASH_API_URL, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if data and data['results']:
            # Return the regular size image URL
            return data['results'][0]['urls']['regular']
        logger.warning(f"No Unsplash image found for query: {query}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching Unsplash image for '{query}': {e}")
        return None

def get_giphy_gif_url(query):
    """Fetch a random GIF URL from Giphy based on a query."""
    params = {
        "api_key": settings.GIPHY_API_KEY,
        "q": query,
        "limit": 1,
        "rating": "g",
        "lang": "en"
    }
    try:
        response = requests.get(GIPHY_API_URL, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if data and data['data']:
            # Return the original GIF URL
            return data['data'][0]['images']['original']['url']
        logger.warning(f"No Giphy GIF found for query: {query}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching Giphy GIF for '{query}': {e}")
        return None

def download_and_save_image(url, directory, filename):
    """Download image from URL and save it to media storage, with caching."""
    if not url:
        logger.warning("No URL provided for image download.")
        return None

    # Construct the full path relative to MEDIA_ROOT
    filepath_in_storage = os.path.join(directory, filename)
    
    # Check if the file already exists in storage
    if default_storage.exists(filepath_in_storage):
        logger.info(f"Image already exists: {filepath_in_storage}. Reusing existing file.")
        return filepath_in_storage

    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            img = Image.open(BytesIO(response.content))
            img_io = BytesIO()
            img.save(img_io, format='JPEG', quality=85)
            img_io.seek(0)
            
            # Save to storage
            path_saved = default_storage.save(filepath_in_storage, ContentFile(img_io.read()))
            logger.info(f"Successfully downloaded and saved image to: {path_saved}")
            return path_saved
        else:
            logger.error(f"Failed to download image: {response.status_code} - {url}")
            return None
    except Exception as e:
        logger.error(f"Error downloading image {url}: {str(e)}")
        return None

def download_and_save_gif(url, directory, filename):
    """Download GIF from URL and save it to media storage, with caching."""
    if not url:
        logger.warning("No URL provided for GIF download.")
        return None

    # Construct the full path relative to MEDIA_ROOT
    filepath_in_storage = os.path.join(directory, filename)

    # Check if the file already exists in storage
    if default_storage.exists(filepath_in_storage):
        logger.info(f"GIF already exists: {filepath_in_storage}. Reusing existing file.")
        return filepath_in_storage

    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            # Save to storage directly (no processing needed for GIFs)
            path_saved = default_storage.save(filepath_in_storage, ContentFile(response.content))
            logger.info(f"Successfully downloaded and saved GIF to: {path_saved}")
            return path_saved
        else:
            logger.error(f"Failed to download GIF: {response.status_code} - {url}")
            return None
    except Exception as e:
        logger.error(f"Error downloading GIF {url}: {str(e)}")
        return None

def populate_questionnaire():
    """Run the populate_questionnaire.py script."""
    try:
        logger.info("Running populate_questionnaire.py...")
        # Call the function directly
        populate_questionnaire_func()
        logger.info("Successfully populated questionnaire data")
        return True
    except Exception as e:
        logger.error(f"Error running populate_questionnaire.py: {e}")
        return False

def populate_faqs():
    """Run the populate_faqs.py script."""
    try:
        logger.info("Running populate_faqs.py...")
        # Call the function directly
        populate_faqs_func()
        logger.info("Successfully populated FAQ data")
        return True
    except Exception as e:
        logger.error(f"Error running populate_faqs.py: {e}")
        return False

def populate_products(json_file):
    """Populate products from JSON file."""
    try:
        # Load product data from JSON file
        # Use settings.BASE_DIR for correct path resolution
        full_json_file_path = os.path.join(settings.BASE_DIR, json_file)

        with open(full_json_file_path, 'r') as file:
            products_data = json.load(file)

        logger.info(f"Loaded {len(products_data)} products from {full_json_file_path}")

        # Process each product
        for product_data in products_data:
            product_name = product_data.get('name')
            if not product_name:
                logger.warning("Skipping product with no name")
                continue

            # Check if product already exists
            product, created = Product.objects.get_or_create(
                name=product_name,
                defaults={
                    'description': product_data.get('description', ''),
                    'buy_link': product_data.get('buy_link', f"https://example.com/products/{product_name.replace(' ', '-').lower()}")
                }
            )

            # Update existing product
            if not created:
                product.description = product_data.get('description', product.description)
                product.buy_link = product_data.get('buy_link', product.buy_link)
                logger.info(f"Updated product: {product_name}")
            else:
                logger.info(f"Created new product: {product_name}")

            # Fetch and save image from Unsplash
            # Use product name or a generic query if name is too specific
            image_query = product_name if product_name else "fitness product"
            image_url = get_unsplash_image_url(image_query)
            if image_url:
                filename = f"{product_name.replace(' ', '_').lower()}.jpg"
                saved_image_path = download_and_save_image(image_url, 'products', filename)

                if saved_image_path:
                    product.image = saved_image_path
                    logger.info(f"Added Unsplash image for product: {product_name}")
                else:
                    logger.warning(f"Failed to add Unsplash image for product: {product_name}")
            else:
                logger.warning(f"No Unsplash image URL found for product: {product_name}")

            # Save the product
            product.save()

        logger.info("Product population completed successfully")
        return True

    except Exception as e:
        logger.error(f"Error populating products: {str(e)}")
        return False

def populate_workout_meal_data(json_file):
    """Populate workout and meal data from JSON file."""
    try:
        # Load workout and meal data from JSON file
        # Use settings.BASE_DIR for correct path resolution
        full_json_file_path = os.path.join(settings.BASE_DIR, json_file)

        with open(full_json_file_path, 'r') as file:
            data = json.load(file)

        logger.info(f"Loaded workout and meal data from {full_json_file_path}")

        # Populate exercises
        logger.info("Creating sample exercises...")
        for exercise_data in data.get('exercises', []):
            exercise, created = Exercise.objects.get_or_create(
                name=exercise_data['name'],
                defaults={
                    'description': exercise_data.get('description', ''),
                    'fitness_level': exercise_data.get('difficulty', 'BEGINNER'),
                    'equipment_required': exercise_data.get('equipment_needed', 'NONE'),
                    'muscle_group': exercise_data.get('muscle_group', 'FULL_BODY'),
                }
            )

            # Update existing exercise
            if not created:
                exercise.description = exercise_data.get('description', exercise.description)
                exercise.fitness_level = exercise_data.get('difficulty', exercise.fitness_level)
                exercise.equipment_required = exercise_data.get('equipment_needed', exercise.equipment_required)
                exercise.muscle_group = exercise_data.get('muscle_group', exercise.muscle_group)
                logger.info(f"Updated exercise: {exercise.name}")
            else:
                logger.info(f"Created exercise: {exercise.name}")

            # Fetch and save image from Unsplash
            image_query = exercise.name
            image_url = get_unsplash_image_url(image_query)
            if image_url:
                filename = f"{exercise.name.replace(' ', '_').lower()}.jpg"
                saved_image_path = download_and_save_image(image_url, 'exercises/images', filename)

                if saved_image_path:
                    exercise.cover_image = saved_image_path # Assign to cover_image
                    logger.info(f"Added Unsplash image for exercise: {exercise.name}")
                else:
                    logger.warning(f"Failed to add Unsplash image for exercise: {exercise.name}")
            else:
                logger.warning(f"No Unsplash image URL found for exercise: {exercise.name}")

            # Fetch and save GIF from Giphy
            gif_query = exercise.name + " exercise"
            gif_url = get_giphy_gif_url(gif_query)
            if gif_url:
                filename = f"{exercise.name.replace(' ', '_').lower()}.gif"
                saved_gif_path = download_and_save_gif(gif_url, 'exercises/gifs', filename)

                if saved_gif_path:
                    exercise.media_file = saved_gif_path # Assign to media_file
                    logger.info(f"Added Giphy GIF for exercise: {exercise.name}")
                else:
                    logger.warning(f"Failed to add Giphy GIF for exercise: {exercise.name}")
            else:
                logger.warning(f"No Giphy GIF URL found for exercise: {exercise.name}")

            # Save the exercise
            exercise.save()

        # Populate meals
        logger.info("Creating sample meals...")
        for meal_data in data.get('meals', []):
            # Map meal type to single-character code
            meal_type_map = {
                'breakfast': 'B',
                'lunch': 'L',
                'dinner': 'D',
                'snack': 'S'
            }
            meal_type = meal_type_map.get(meal_data.get('meal_type', ''), 'B')

            meal, created = Meal.objects.get_or_create(
                name=meal_data['name'],
                defaults={
                    'description': meal_data.get('description', ''),
                    'calories': meal_data.get('calories', 0),
                    'protein': meal_data.get('protein', 0),
                    'carbs': meal_data.get('carbs', 0),
                    'fat': meal_data.get('fat', 0),
                    'meal_type': meal_type,
                    'instructions': meal_data.get('instructions', ''),
                    'preparation_time': meal_data.get('preparation_time', 15),
                    'cooking_time': meal_data.get('cooking_time', 30),
                }
            )

            # Update existing meal
            if not created:
                meal.description = meal_data.get('description', meal.description)
                meal.calories = meal_data.get('calories', meal.calories)
                meal.protein = meal_data.get('protein', meal.protein)
                meal.carbs = meal_data.get('carbs', meal.carbs)
                meal.fat = meal_data.get('fat', meal.fat)
                # Use the mapped meal type
                meal.meal_type = meal_type
                meal.instructions = meal_data.get('instructions', meal.instructions)
                meal.preparation_time = meal_data.get('preparation_time', meal.preparation_time)
                meal.cooking_time = meal_data.get('cooking_time', meal.cooking_time)
                logger.info(f"Updated meal: {meal.name}")
            else:
                logger.info(f"Created meal: {meal.name}")

            # Fetch and save image from Unsplash
            image_query = meal.name + " food"
            image_url = get_unsplash_image_url(image_query)
            if image_url:
                filename = f"{meal.name.replace(' ', '_').lower()}.jpg"
                saved_image_path = download_and_save_image(image_url, 'meals/images', filename)

                if saved_image_path:
                    meal.meal_image = saved_image_path # Assign to meal_image
                    logger.info(f"Added Unsplash image for meal: {meal.name}")
                else:
                    logger.warning(f"Failed to add Unsplash image for meal: {meal.name}")
            else:
                logger.warning(f"No Unsplash image URL found for meal: {meal.name}")

            # Save the meal
            meal.save()

        # Removed redundant workout plan population as it's handled by populate_plans_from_criteria and generate_daily_plan_templates_for_all_plans
        logger.info("Skipping sample workout plan population (handled by other functions).")

        # Removed redundant meal plan population as it's handled by populate_plans_from_criteria and generate_daily_plan_templates_for_all_plans
        logger.info("Skipping sample meal plan population (handled by other functions).")

        # Create test user
        logger.info("Creating 1 specific test user...")
        user_data = data.get('test_user', {})

        # Delete existing test user if it exists
        User.objects.filter(email=user_data.get('email')).delete()
        logger.info(f"Deleted test user: {user_data.get('email')}")

        # Create new test user
        user = User.objects.create_user(
            username=user_data.get('username', 'testuser'),
            email=user_data.get('email', '<EMAIL>'),
            password=user_data.get('password', 'password123'),
            first_name=user_data.get('first_name', 'Test'),
            last_name=user_data.get('last_name', 'User'),
            is_active=True
        )

        # Create email address for allauth (if using allauth)
        try:
            from allauth.account.models import EmailAddress
            EmailAddress.objects.create(
                user=user,
                email=user.email,
                verified=True,
                primary=True
            )
            logger.info(f"Created verified email address for {user.email}")
        except ImportError:
            logger.warning("Could not import EmailAddress from allauth.account.models")
        except Exception as e:
            logger.warning(f"Could not create EmailAddress for {user.email}: {e}")

        logger.info(f"Created user: {user.username}")

        # Update user profile
        profile_data = user_data.get('profile', {})
        profile = UserProfile.objects.get(user=user)
        profile.height = profile_data.get('height', 175)
        profile.weight = profile_data.get('weight', 75)
        profile.age = profile_data.get('age', 30)
        profile.gender = profile_data.get('gender', 'male')
        profile.activity_level = profile_data.get('activity_level', 'moderate')
        profile.goal = profile_data.get('goal', 'weight_loss')
        profile.save()

        logger.info(f"Updated profile for {user.username} with specific defaults.")

        # Initialize progress and score
        UserProgress.objects.get_or_create(user=profile)
        UserScore.objects.get_or_create(user=profile)
        logger.info(f"Initialized progress and score for {user.username}")

        # Create workout logs
        logger.info("Creating sample workout logs...")

        # Assign workout plan to user
        workout_plan = WorkoutPlan.objects.first()
        if workout_plan:
            UserWorkoutPlan.objects.get_or_create(
                user=profile,  # Use UserProfile instance, not User
                workout_plan=workout_plan
            )
            logger.info(f"Created user workout plan for {user.username}")

        # Assign meal plan to user
        meal_plan = MealPlan.objects.first()
        if meal_plan:
            UserMealPlan.objects.get_or_create(
                user=profile,  # Use UserProfile instance, not User
                meal_plan=meal_plan
            )
            logger.info(f"Created user meal plan for {user.username}")

        # Create workout logs
        logger.info("Creating sample workout logs...")

        # Get all workout days
        workout_days = WorkoutDay.objects.all()
        if not workout_days.exists():
            logger.warning("No workout days found to create logs")
        else:
            # Create logs for the past 7 days
            from datetime import date, timedelta
            today = date.today()

            # Delete any existing workout logs for this user
            WorkoutLog.objects.filter(user=profile).delete()
            logger.info(f"Deleted existing workout logs for {user.username}")

            # Create one log per day for the past 14 days (2 weeks)
            for i in range(14):
                log_date = today - timedelta(days=i+1)  # Start from yesterday
                workout_day = workout_days[i % workout_days.count()]  # Cycle through available workout days

                # Create workout log
                workout_log = WorkoutLog.objects.create(
                    user=profile,
                    workout_day=workout_day,
                    date=log_date,
                    is_completed=True,
                    notes=f"Completed workout on {log_date.strftime('%A, %B %d')}"
                )

                logger.info(f"Created workout log for {user.username} - {workout_day.name} - {log_date}")

                # Create a workout session log first
                try:
                    # Find a workout session for this day
                    workout_session = WorkoutSession.objects.filter(workout_day=workout_day).first()

                    if workout_session:
                        # Create workout session log
                        session_log = WorkoutSessionLog.objects.create(
                            workout_log=workout_log,
                            workout_session=workout_session,
                            date=log_date,
                            is_completed=True
                        )

                        logger.info(f"Created workout session log for session {workout_session.name}")

                        # Get all exercises for this workout section
                        exercise_instances = ExerciseInstance.objects.filter(
                            workout_section__workout_session=workout_session
                        )

                        # Create exercise logs for each exercise instance
                        for instance in exercise_instances:
                            # Create exercise log with safe conversions
                            try:
                                # Handle reps safely
                                if instance.reps and isinstance(instance.reps, str):
                                    if instance.reps.isdigit():
                                        actual_reps = int(instance.reps)
                                    else:
                                        # Handle ranges like "8-12" by taking the average
                                        if '-' in instance.reps:
                                            parts = instance.reps.split('-')
                                            if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                                                actual_reps = (int(parts[0]) + int(parts[1])) // 2
                                            else:
                                                actual_reps = 10
                                        else:
                                            actual_reps = 10
                                else:
                                    actual_reps = 10

                                # Create the exercise log
                                ExerciseLog.objects.create(
                                    workout_session_log=session_log,
                                    exercise=instance.exercise,
                                    actual_sets=instance.sets if instance.sets else 3,
                                    actual_reps=actual_reps,
                                    actual_weight=float(instance.weight) if instance.weight else 0,
                                    actual_duration_seconds=0  # Default to 0 for duration
                                )
                            except Exception as e:
                                logger.error(f"Error creating exercise log for {instance.exercise.name}: {str(e)}")

                            logger.info(f"Created exercise log for {instance.exercise.name}")
                    else:
                        logger.warning(f"No workout session found for workout day {workout_day.name}")

                except Exception as e:
                    logger.error(f"Error creating workout session log: {str(e)}")

        logger.info("Finished creating sample workout logs")

        logger.info("Script finished setting up initial data.")
        logger.info("--- Initial Data Setup Complete ---")
        logger.info(f"Created/updated 1 specific test user.")
        logger.info(f"Ensured workout plan exists: {workout_plan.name if workout_plan else 'None'}")
        logger.info(f"Ensured meal plan exists: {MealPlan.objects.first().name if MealPlan.objects.exists() else 'None'}")
        logger.info("Test user credentials:")
        logger.info(f"  User: Email: {user.email}, Password: {user_data.get('password', 'password123')}")

        return True

    except Exception as e:
        logger.error(f"Error populating workout and meal data: {str(e)}")
        return False

def generate_daily_plan_templates_for_all_plans():
    logger.info("Generating 14-day daily plan templates for all MealPlans and WorkoutPlans...")

    # --- Meal Plans ---
    all_meal_plans = MealPlan.objects.all()
    all_meals = list(Meal.objects.all())
    if not all_meals:
        logger.warning("No Meal objects found. Creating dummy meals for template generation.")
        dummy_meal_names = ["Dummy Breakfast", "Dummy Lunch", "Dummy Dinner", "Dummy Snack"]
        for name in dummy_meal_names:
            Meal.objects.get_or_create(
                name=name,
                defaults={
                    'description': f"A sample {name}",
                    'meal_type': 'B' if 'Breakfast' in name else ('L' if 'Lunch' in name else ('D' if 'Dinner' in name else 'S')),
                    'calories': random.randint(300, 800),
                    'protein': random.randint(10, 50),
                    'carbs': random.randint(20, 100),
                    'fat': random.randint(10, 40),
                    'instructions': f"Prepare {name} as usual.",
                    'preparation_time': 10,
                    'cooking_time': 20,
                }
            )
        all_meals = list(Meal.objects.all())
        if not all_meals:
            logger.error("Failed to create dummy meals. Cannot generate meal plan templates.")
            return

    for meal_plan in all_meal_plans:
        logger.info(f"Processing MealPlan: {meal_plan.name} (ID: {meal_plan.id})")

        # Delete existing template DailyMealPlans and MealSchedules for this plan
        DailyMealPlan.objects.filter(meal_plan=meal_plan, user_meal_plan__isnull=True).delete()
        MealSchedule.objects.filter(meal_plan=meal_plan, user_meal_plan__isnull=True).delete()
        logger.info(f"Deleted existing template daily plans and schedules for {meal_plan.name}")

        created_daily_plans = []
        for day_number in range(1, 15): # 14 days
            day_of_week = (day_number - 1) % 7 # 0=Monday, 6=Sunday

            # Assign random meals
            breakfast_meal = random.choice(all_meals) if all_meals else None
            lunch_meal = random.choice(all_meals) if all_meals else None
            dinner_meal = random.choice(all_meals) if all_meals else None

            daily_plan, created = DailyMealPlan.objects.get_or_create(
                meal_plan=meal_plan,
                day_number=day_number,
                user_meal_plan__isnull=True, # Ensure we are creating/getting a template
                defaults={
                    'name': f"Day {day_number} Meal Plan",
                    'day_of_week': day_of_week,
                    'date': None, # Templates don't have specific dates
                    'breakfast': breakfast_meal,
                    'lunch': lunch_meal,
                    'dinner': dinner_meal,
                }
            )
            if not created:
                # Update existing template daily plan
                daily_plan.name = f"Day {day_number} Meal Plan"
                daily_plan.day_of_week = day_of_week
                daily_plan.breakfast = breakfast_meal
                daily_plan.lunch = lunch_meal
                daily_plan.dinner = dinner_meal
                daily_plan.save()
            created_daily_plans.append(daily_plan)
            logger.info(f"{'Created' if created else 'Updated'} template DailyMealPlan {daily_plan.id} for {meal_plan.name}, Day {day_number}")

        # Create/Update MealSchedules for 2 weeks
        for week_number in range(1, 3): # Week 1 and Week 2
            schedule, created = MealSchedule.objects.get_or_create(
                meal_plan=meal_plan,
                week_number=week_number,
                user_meal_plan__isnull=True, # Ensure we are creating/getting a template
                defaults={}
            )

            # Populate days JSON field and weekday fields
            days_mapping = {}
            weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            for i in range(7):
                day_index = (week_number - 1) * 7 + i
                if day_index < len(created_daily_plans):
                    daily_plan = created_daily_plans[day_index]
                    days_mapping[str(day_index + 1)] = daily_plan.id # Day numbers are 1-indexed
                    setattr(schedule, weekday_fields[i], daily_plan)
                else:
                    days_mapping[str(day_index + 1)] = None
                    setattr(schedule, weekday_fields[i], None)

            schedule.days = days_mapping
            schedule.save()
            logger.info(f"{'Created' if created else 'Updated'} template MealSchedule {schedule.id} for {meal_plan.name}, Week {week_number}")

    # --- Workout Plans ---
    all_workout_plans = WorkoutPlan.objects.all()
    all_exercises = list(Exercise.objects.all())
    if not all_exercises:
        logger.warning("No Exercise objects found. Creating dummy exercises for template generation.")
        dummy_exercise_names = ["Push-up", "Squat", "Plank", "Lunge", "Burpee"]
        for name in dummy_exercise_names:
            Exercise.objects.get_or_create(
                name=name,
                defaults={
                    'description': f"A sample {name} exercise.",
                    'muscle_group': 'FULL_BODY',
                    'fitness_level': 'BEGINNER',
                    'equipment_required': 'NONE',
                }
            )
        all_exercises = list(Exercise.objects.all())
        if not all_exercises:
            logger.error("Failed to create dummy exercises. Cannot generate workout plan templates.")
            return

    for workout_plan in all_workout_plans:
        logger.info(f"Processing WorkoutPlan: {workout_plan.name} (ID: {workout_plan.id})")

        # Fetch and save cover image for WorkoutPlan
        image_query = workout_plan.name + " workout plan"
        image_url = get_unsplash_image_url(image_query)
        if image_url:
            filename = f"{workout_plan.name.replace(' ', '_').lower()}_cover.jpg"
            saved_image_path = download_and_save_image(image_url, 'workout_plans/covers', filename)

            if saved_image_path:
                workout_plan.cover_image = saved_image_path
                workout_plan.save(update_fields=['cover_image']) # Save only the image field
                logger.info(f"Added Unsplash cover image for WorkoutPlan: {workout_plan.name}")
            else:
                logger.warning(f"Failed to add Unsplash cover image for WorkoutPlan: {workout_plan.name}")
        else:
            logger.warning(f"No Unsplash image URL found for WorkoutPlan: {workout_plan.name}")

        # Delete existing template WorkoutDay, Session, Section, Instance, and Schedule for this plan
        ExerciseInstance.objects.filter(workout_section__workout_session__workout_day__workout_plan=workout_plan, user_workout_plan__isnull=True).delete()
        WorkoutSection.objects.filter(workout_session__workout_day__workout_plan=workout_plan, user_workout_plan__isnull=True).delete()
        WorkoutSession.objects.filter(workout_day__workout_plan=workout_plan, user_workout_plan__isnull=True).delete()
        WorkoutDay.objects.filter(workout_plan=workout_plan, user_workout_plan__isnull=True).delete()
        WorkoutSchedule.objects.filter(workout_plan=workout_plan, user_workout_plan__isnull=True).delete()
        logger.info(f"Deleted existing template workout components and schedules for {workout_plan.name}")

        created_workout_days = []
        for day_number in range(1, 15): # 14 days
            day_of_week = (day_number - 1) % 7 # 0=Monday, 6=Sunday

            workout_day, created = WorkoutDay.objects.get_or_create(
                workout_plan=workout_plan,
                day_number=day_number,
                user_workout_plan__isnull=True, # Ensure we are creating/getting a template
                defaults={
                    'name': f"Day {day_number} Workout",
                    'description': f"Workout for day {day_number} of {workout_plan.name}",
                    'day_of_week': day_of_week,
                    'date': None, # Templates don't have specific dates
                    'total_duration': random.randint(30, 90),
                    'fitness_level': workout_plan.fitness_level,
                    'calories_burn_estimate': random.randint(200, 600),
                }
            )
            if not created:
                # Update existing template workout day
                workout_day.name = f"Day {day_number} Workout"
                workout_day.description = f"Workout for day {day_number} of {workout_plan.name}"
                workout_day.day_of_week = day_of_week
                workout_day.total_duration = random.randint(30, 90)
                workout_day.fitness_level = workout_plan.fitness_level
                workout_day.calories_burn_estimate = random.randint(200, 600)
                workout_day.save()
            created_workout_days.append(workout_day)
            logger.info(f"{'Created' if created else 'Updated'} template WorkoutDay {workout_day.id} for {workout_plan.name}, Day {day_number}")

            # Create WorkoutSession, WorkoutSection, ExerciseInstance templates for this WorkoutDay
            workout_session, session_created = WorkoutSession.objects.get_or_create(
                workout_day=workout_day,
                order=0, # Main session
                user_workout_plan__isnull=True,
                defaults={
                    'name': 'Main Session',
                    'description': 'Primary workout session for the day',
                    'duration': random.randint(30, 60),
                }
            )
            logger.info(f"{'Created' if session_created else 'Updated'} template WorkoutSession {workout_session.id} for Day {workout_day.id}")

            workout_section, section_created = WorkoutSection.objects.get_or_create(
                workout_session=workout_session,
                order=0, # Main section
                user_workout_plan__isnull=True,
                defaults={
                    'name': 'Main Workout Section',
                    'section_type': 'MAIN',
                    'duration': random.randint(20, 50),
                }
            )
            logger.info(f"{'Created' if section_created else 'Updated'} template WorkoutSection {workout_section.id} for Session {workout_session.id}")

            # Create 3 ExerciseInstances
            for i in range(3):
                exercise = random.choice(all_exercises) if all_exercises else None
                if exercise:
                    ExerciseInstance.objects.get_or_create(
                        workout_section=workout_section,
                        exercise=exercise,
                        order=i,
                        user_workout_plan__isnull=True,
                        defaults={
                            'sets': random.randint(3, 5),
                            'reps': str(random.randint(8, 15)),
                            'rest_seconds': random.randint(30, 90),
                            'weight': random.randint(10, 50) if random.random() > 0.5 else None,
                        }
                    )
                    logger.info(f"Created template ExerciseInstance for {exercise.name} in Section {workout_section.id}")
                else:
                    logger.warning("No exercises available to create ExerciseInstance.")

        # Create/Update WorkoutSchedules for 2 weeks
        for week_number in range(1, 3): # Week 1 and Week 2
            schedule, created = WorkoutSchedule.objects.get_or_create(
                workout_plan=workout_plan,
                week_number=week_number,
                user_workout_plan__isnull=True, # Ensure we are creating/getting a template
                defaults={}
            )

            # Populate days JSON field and weekday fields
            days_mapping = {}
            weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            for i in range(7):
                day_index = (week_number - 1) * 7 + i
                if day_index < len(created_workout_days):
                    workout_day = created_workout_days[day_index]
                    days_mapping[str(day_index + 1)] = workout_day.id # Day numbers are 1-indexed
                    setattr(schedule, weekday_fields[i], workout_day)
                else:
                    days_mapping[str(day_index + 1)] = None
                    setattr(schedule, weekday_fields[i], None)

            schedule.days = days_mapping
            schedule.save()
            logger.info(f"{'Created' if created else 'Updated'} template WorkoutSchedule {schedule.id} for {workout_plan.name}, Week {week_number}")

    logger.info("Finished generating 14-day daily plan templates for all MealPlans and WorkoutPlans.")

class Command(BaseCommand):
    help = 'Populates all initial data for the AC FIT backend, including questionnaire, FAQs, products, and 14-day meal/workout plan templates.'

    def add_arguments(self, parser):
        parser.add_argument('--products-json', type=str, default='products.json',
                            help='Path to the products JSON file relative to backend directory.')
        parser.add_argument('--data-json', type=str, default='data.json',
                            help='Path to the workout and meal data JSON file relative to backend directory.')
        parser.add_argument('--skip-questionnaire', action='store_true',
                            help='Skip populating questionnaire data.')
        parser.add_argument('--skip-faqs', action='store_true',
                            help='Skip populating FAQ data.')
        parser.add_argument('--skip-products', action='store_true',
                            help='Skip populating product data.')
        parser.add_argument('--skip-workout-meal', action='store_true',
                            help='Skip populating workout and meal data (from data.json).')
        parser.add_argument('--skip-plan-templates', action='store_true',
                            help='Skip generating 14-day daily plan templates for all plans.')

    def handle(self, *args, **options):
        logger.info("Starting full data population script...")

        # Ensure all required directories exist
        ensure_directories()

        # Populate questionnaire data
        if not options['skip_questionnaire']:
            self.stdout.write(self.style.SUCCESS("Running populate_questionnaire..."))
            if not populate_questionnaire():
                self.stderr.write(self.style.ERROR("Failed to populate questionnaire data"))
                raise CommandError("Failed to populate questionnaire data")

        # Populate FAQ data
        if not options['skip_faqs']:
            self.stdout.write(self.style.SUCCESS("Running populate_faqs..."))
            if not populate_faqs():
                self.stderr.write(self.style.ERROR("Failed to populate FAQ data"))
                raise CommandError("Failed to populate FAQ data")

        # Populate product data
        if not options['skip_products']:
            self.stdout.write(self.style.SUCCESS("Running populate_products..."))
            if not populate_products(options['products_json']):
                self.stderr.write(self.style.ERROR("Failed to populate product data"))
                raise CommandError("Failed to populate product data")

        # Populate plans from criteria (new step)
        self.stdout.write(self.style.SUCCESS("Populating plans from criteria..."))
        populate_plans_from_criteria() # Call the new population function

        # Generate 14-day daily plan templates for all plans
        if not options['skip_plan_templates']:
            self.stdout.write(self.style.SUCCESS("Generating daily plan templates for all plans..."))
            generate_daily_plan_templates_for_all_plans()

        # Populate workout and meal data (from data.json)
        if not options['skip_workout_meal']:
            self.stdout.write(self.style.SUCCESS("Running populate_workout_meal_data..."))
            if not populate_workout_meal_data(options['data_json']):
                self.stderr.write(self.style.ERROR("Failed to populate workout and meal data"))
                raise CommandError("Failed to populate workout and meal data")

        self.stdout.write(self.style.SUCCESS("\n--- All Initial Data Setup Complete ---"))
        self.stdout.write(self.style.SUCCESS("Please run 'python manage.py createsuperuser' to create an admin user if you haven't already."))
        self.stdout.write(self.style.SUCCESS("You can now run the Django development server: 'python manage.py runserver'"))
