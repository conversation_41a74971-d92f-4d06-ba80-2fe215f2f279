class QuestionnaireData {
  // Basic Information
  String? gender; // 'M', 'F'
  String? ageGroup; // 'UNDER_30', '30_40', '40_PLUS'
  int? age;
  double? height; // in cm
  double? weight; // in kg
  List<String>?
      healthConditions; // ['NONE', 'DIABETES', 'HIGH_CHOLESTEROL', 'JOINT_ISSUES', 'OTHER']
  String? primaryFitnessGoal; // 'LOSE_WEIGHT', 'GAIN_MUSCLE', 'MAINTAIN_HEALTH'

  // Diet Preferences
  String? isKeto; // 'YES', 'NO'
  String? isIntermittentFasting; // 'YES', 'NO'
  String? cookingPreference; // 'HOME', 'OUT', 'BOTH'
  String? waterIntake; // 'LESS_4', '4_8', 'MORE_8'
  String? isPhysicallyActive; // 'YES', 'NO'

  // Workout Preferences
  String? workoutLocation; // 'HOME', 'GYM', 'HYBRID'
  String? workoutDays; // '2_3_DAYS', '4_5_DAYS', '6_7_DAYS'
  String? hasHomeEquipment; // 'YES', 'NO'
  String? fitnessLevel; // 'BEGINNER', 'INTERMEDIATE', 'ADVANCED'

  QuestionnaireData({
    this.gender,
    this.ageGroup,
    this.age,
    this.height,
    this.weight,
    this.healthConditions,
    this.primaryFitnessGoal,
    this.isKeto,
    this.isIntermittentFasting,
    this.cookingPreference,
    this.waterIntake,
    this.isPhysicallyActive,
    this.workoutLocation,
    this.workoutDays,
    this.hasHomeEquipment,
    this.fitnessLevel,
  });

  // Create from JSON
  factory QuestionnaireData.fromJson(Map<String, dynamic> json) {
    return QuestionnaireData(
      gender: json['gender'] as String?,
      ageGroup: json['age_group'] as String?,
      age: json['age'] as int?,
      height: json['height'] != null
          ? (json['height'] is int
              ? (json['height'] as int).toDouble()
              : json['height'] as double?)
          : null,
      weight: json['weight'] != null
          ? (json['weight'] is int
              ? (json['weight'] as int).toDouble()
              : json['weight'] as double?)
          : null,
      healthConditions:
          (json['health_conditions'] as List<dynamic>?)?.cast<String>(),
      primaryFitnessGoal: json['primary_fitness_goal'] as String?,
      isKeto: json['is_keto'] as String?,
      isIntermittentFasting: json['is_intermittent_fasting'] as String?,
      cookingPreference: json['cooking_preference'] as String?,
      waterIntake: json['water_intake'] as String?,
      isPhysicallyActive: json['is_physically_active'] as String?,
      workoutLocation: json['workout_location'] as String?,
      workoutDays: json['workout_days'] as String?,
      hasHomeEquipment: json['has_home_equipment'] as String?,
      fitnessLevel: json['fitness_level'] as String?,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    // Only add fields that have been set
    if (gender != null) {
      data['gender'] = gender;
    }
    if (ageGroup != null) {
      data['age_group'] = ageGroup;
    }
    if (age != null) {
      data['age'] = age;
    }
    if (height != null) {
      data['height'] = height;
    } else {
      // Provide a default height if none is set to ensure the field is always included
      data['height'] = 170.0; // Default height in cm
    }
    if (weight != null) {
      data['weight'] = weight;
    }
    if (healthConditions != null && healthConditions!.isNotEmpty) {
      data['health_conditions'] = healthConditions;
    }
    if (primaryFitnessGoal != null) {
      data['primary_fitness_goal'] = primaryFitnessGoal;
    }
    if (isKeto != null) {
      data['is_keto'] = isKeto;
    }
    if (isIntermittentFasting != null) {
      data['is_intermittent_fasting'] = isIntermittentFasting;
    }
    if (cookingPreference != null) {
      data['cooking_preference'] = cookingPreference;
    }
    if (waterIntake != null) {
      data['water_intake'] = waterIntake;
    }
    if (isPhysicallyActive != null) {
      data['is_physically_active'] = isPhysicallyActive;
    }
    if (workoutLocation != null) {
      data['workout_location'] = workoutLocation;
    }
    if (workoutDays != null) {
      data['workout_days'] = workoutDays;
    }
    if (hasHomeEquipment != null) {
      data['has_home_equipment'] = hasHomeEquipment;
    }
    if (fitnessLevel != null) {
      data['fitness_level'] = fitnessLevel;
    }

    return data;
  }

  // Create a copy with modifications
  QuestionnaireData copyWith({
    String? gender,
    String? ageGroup,
    int? age,
    double? height,
    double? weight,
    List<String>? healthConditions,
    String? primaryFitnessGoal,
    String? isKeto,
    String? isIntermittentFasting,
    String? cookingPreference,
    String? waterIntake,
    String? isPhysicallyActive,
    String? workoutLocation,
    String? workoutDays,
    String? hasHomeEquipment,
    String? fitnessLevel,
  }) {
    return QuestionnaireData(
      gender: gender ?? this.gender,
      ageGroup: ageGroup ?? this.ageGroup,
      age: age ?? this.age,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      healthConditions: healthConditions ?? this.healthConditions,
      primaryFitnessGoal: primaryFitnessGoal ?? this.primaryFitnessGoal,
      isKeto: isKeto ?? this.isKeto,
      isIntermittentFasting:
          isIntermittentFasting ?? this.isIntermittentFasting,
      cookingPreference: cookingPreference ?? this.cookingPreference,
      waterIntake: waterIntake ?? this.waterIntake,
      isPhysicallyActive: isPhysicallyActive ?? this.isPhysicallyActive,
      workoutLocation: workoutLocation ?? this.workoutLocation,
      workoutDays: workoutDays ?? this.workoutDays,
      hasHomeEquipment: hasHomeEquipment ?? this.hasHomeEquipment,
      fitnessLevel: fitnessLevel ?? this.fitnessLevel,
    );
  }
}
