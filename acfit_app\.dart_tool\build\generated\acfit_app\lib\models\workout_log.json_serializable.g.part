// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TodaysWorkoutResponse _$TodaysWorkoutResponseFromJson(
        Map<String, dynamic> json) =>
    TodaysWorkoutResponse(
      date: json['date'] as String,
      workoutLogs: (json['workout_logs'] as List<dynamic>?)
              ?.map((e) => WorkoutLog.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      isRestDay: json['is_rest_day'] as bool? ?? false,
      nextWorkoutDate: json['next_workout_date'] as String?,
      workoutPlanCoverImageUrl: json['workout_plan_cover_image_url'] as String?,
      coverImageUrl: json['workout_day_cover_image_url'] as String?,
      noPlanAssigned: json['no_plan_assigned'] as bool? ?? false,
      message: json['message'] as String?,
      sessions: (json['workout_sessions'] as List<dynamic>?)
              ?.map((e) => WorkoutSession.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      workoutDayName: json['workout_day_name'] as String?,
      programDay: (json['program_day'] as num?)?.toInt(),
      workoutDay: json['workout_day'] == null
          ? null
          : WorkoutDay.fromJson(json['workout_day'] as Map<String, dynamic>),
      dayOfProgram: (json['day_of_program'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$TodaysWorkoutResponseToJson(
        TodaysWorkoutResponse instance) =>
    <String, dynamic>{
      'date': instance.date,
      'workout_logs': instance.workoutLogs.map((e) => e.toJson()).toList(),
      'is_rest_day': instance.isRestDay,
      'next_workout_date': instance.nextWorkoutDate,
      'workout_plan_cover_image_url': instance.workoutPlanCoverImageUrl,
      'workout_day_cover_image_url': instance.coverImageUrl,
      'no_plan_assigned': instance.noPlanAssigned,
      'message': instance.message,
      'workout_sessions': instance.sessions?.map((e) => e.toJson()).toList(),
      'workout_day_name': instance.workoutDayName,
      'program_day': instance.programDay,
      'workout_day': instance.workoutDay?.toJson(),
      'day_of_program': instance.dayOfProgram,
    };

WorkoutLog _$WorkoutLogFromJson(Map<String, dynamic> json) => WorkoutLog(
      id: (json['id'] as num).toInt(),
      workoutDay: _workoutDayFromJson(json['workoutDay']),
      date: json['date'] as String,
      isCompleted: json['is_completed'] as bool,
      scheduledTime: json['scheduled_time'] == null
          ? null
          : DateTime.parse(json['scheduled_time'] as String),
      completionTime: json['completion_time'] == null
          ? null
          : DateTime.parse(json['completion_time'] as String),
      durationMinutes: (json['duration_minutes'] as num?)?.toInt(),
      caloriesBurned: (json['calories_burned'] as num?)?.toInt(),
      feeling: json['feeling'] as String?,
      intensity: (json['intensity'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      sections: (json['sections'] as List<dynamic>?)
              ?.map((e) => WorkoutSection.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      workoutPlanCoverImageUrl: json['workoutPlanCoverImageUrl'] as String?,
      workoutSession: (json['workoutSession'] as num?)?.toInt(),
      sessionLogs: (json['sessionLogs'] as List<dynamic>?)
          ?.map((e) => WorkoutSessionLog.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkoutLogToJson(WorkoutLog instance) =>
    <String, dynamic>{
      'id': instance.id,
      'workoutDay': instance.workoutDay?.toJson(),
      'date': instance.date,
      'is_completed': instance.isCompleted,
      'scheduled_time': instance.scheduledTime?.toIso8601String(),
      'completion_time': instance.completionTime?.toIso8601String(),
      'duration_minutes': instance.durationMinutes,
      'calories_burned': instance.caloriesBurned,
      'feeling': instance.feeling,
      'intensity': instance.intensity,
      'notes': instance.notes,
      'sections': instance.sections.map((e) => e.toJson()).toList(),
      'workoutPlanCoverImageUrl': instance.workoutPlanCoverImageUrl,
      'sessionLogs': instance.sessionLogs?.map((e) => e.toJson()).toList(),
      'workoutSession': instance.workoutSession,
    };
