// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_score.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserScore _$UserScoreFromJson(Map<String, dynamic> json) => UserScore(
      id: (json['id'] as num).toInt(),
      user: (json['user'] as num).toInt(),
      userEmail: json['user_email'] as String,
      username: json['username'] as String,
      totalScore: (json['total_score'] as num?)?.toInt(),
      workoutScore: (json['workout_score'] as num?)?.toInt(),
      streakScore: (json['streak_score'] as num?)?.toInt(),
      nutritionScore: (json['nutrition_score'] as num?)?.toInt(),
      goalScore: (json['goal_score'] as num?)?.toInt(),
      consecutiveDaysActive: (json['consecutive_days_active'] as num?)?.toInt(),
      totalPointsEarned: (json['total_points_earned'] as num?)?.toInt(),
      perfectWeeks: (json['perfect_weeks'] as num?)?.toInt(),
      weightMilestones: (json['weight_milestones'] as num?)?.toInt(),
      lastCalculated: json['last_calculated'] == null
          ? null
          : DateTime.parse(json['last_calculated'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$UserScoreToJson(UserScore instance) => <String, dynamic>{
      'id': instance.id,
      'user': instance.user,
      'user_email': instance.userEmail,
      'username': instance.username,
      'total_score': instance.totalScore,
      'workout_score': instance.workoutScore,
      'streak_score': instance.streakScore,
      'nutrition_score': instance.nutritionScore,
      'goal_score': instance.goalScore,
      'consecutive_days_active': instance.consecutiveDaysActive,
      'total_points_earned': instance.totalPointsEarned,
      'perfect_weeks': instance.perfectWeeks,
      'weight_milestones': instance.weightMilestones,
      'last_calculated': instance.lastCalculated?.toIso8601String(),
      'created_at': instance.createdAt?.toIso8601String(),
    };
